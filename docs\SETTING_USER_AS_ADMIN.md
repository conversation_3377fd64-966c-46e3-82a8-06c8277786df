# How to Set a User as an Admin

To grant a user admin privileges in the system, follow these steps:

## 1. Add an Entry to the `user_role` Table
- The `user_role` table is used exclusively for admin users.
- Insert a new row for the user you want to make an admin, with `role_id` set to `0`.
- Example SQL:

  ```sql
  INSERT INTO user_role (user_id, role_id) VALUES (<USER_ID>, 0);
  ```
  Replace `<USER_ID>` with the actual user's ID.

## 2. Effect
- The user will now have admin access throughout the system.
- No further configuration is required.

---

**Note:**
- Only add users to the `user_role` table with `role_id = 0` if they should have full administrative privileges.
- Changes take effect immediately after inserting the row.
