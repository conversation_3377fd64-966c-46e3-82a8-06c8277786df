# Project Assignments System

## Overview
This system manages team memberships and project assignments in a two-tier structure:

1. **Team-Only Memberships**: Users are first invited to join a team (without project assignment)
2. **Project Assignments**: Team members can then be assigned to specific projects within their team

## Database Structure

### TeamMappings Table
- `teammapping_id` (Primary Key)
- `username` - User identifier
- `role_name` - Role within team/project
- `team_name` - Team identifier
- `project_id` - Project identifier (NULL for team-only memberships)
- `invitation_status` - pending/accepted/declined
- `invitation_token` - Unique token for invitations

### Roles Table
- `role_id` (Primary Key)
- `role_name` - Name of the role
- `team_id` - Team identifier (optional)
- `project_id` - Project identifier (NULL for global roles, NOT NULL for project-specific roles)

### Key Concepts

#### Role System
- **Team Roles**: `project_id` IS NULL - Used for team-only memberships (e.g., "Team Lead", "Team Member")
- **Project-Specific Roles**: `project_id` IS NOT NULL - Specific to one project (e.g., "Lead Developer", "UI Designer", "Tester")
- **Users can have different roles in different projects** - This is the core feature

#### Team-Only Memberships
- Entries where `project_id` IS NULL
- These represent basic team membership
- Used for team invitations
- Role represents **team role** (Team Manager, Team Member, etc.)
- Uses roles where `project_id` IS NULL (team roles)

#### Project Assignments
- Entries where `project_id` IS NOT NULL
- These represent project-specific assignments
- Created from existing team memberships
- Role represents **project-specific role** (Project Manager, Developer, Tester, etc.)
- Uses roles where `project_id` matches the project (project-specific roles only)
- **Same user can have different roles in different projects**

## Features

### 1. Team Management (Existing)
- `/teammappings/{team_name}` - View team members (team-only)
- `/teammappings/{team_name}/create` - Invite new team members
- Team invitations with email notifications

### 2. Project Assignments (New)
- `/project-assignments` - Dashboard showing all projects with assignment status
- `/project-assignments/create?project_id={id}` - Assign team members to projects
- `/project-assignments/{project_id}` - View and manage project assignments

### 3. Navigation
- "Team" menu shows team-only memberships
- "Project Assignments" menu shows project-specific assignments

## Workflow

1. **Team Setup**: Admin invites users to teams (creates team-only membership)
2. **Accept Invitation**: Users accept team invitations
3. **Project Assignment**: Team managers assign team members to specific projects
4. **Role Management**: Different roles can be assigned at team and project levels

## Model Relationships

### TeamMapping Model
```php
// Scopes
scopeTeamOnly() - Filter team-only memberships (project_id IS NULL)
scopeProjectAssignments() - Filter project assignments (project_id IS NOT NULL)

// Helper Methods
isTeamOnlyMembership() - Check if entry is team-only
isProjectAssignment() - Check if entry is project assignment
getRoleContext() - Get role context (Team Role/Project Role)
getAvailableRoles() - Get available roles based on membership context (team roles for team-only, project roles for project assignments)

// Static Methods
getTeamMembers($teamName) - Get team members for a team
getProjectAssignments($projectId) - Get project assignments for a project
```

### Project Model
```php
teamAssignments() - HasMany relationship to TeamMapping for project assignments
projectRoles() - HasMany relationship to Role for project-specific roles
getAvailableRoles() - Get project-specific roles for this project (only roles where project_id matches)
```

### Role Model
```php
project() - BelongsTo relationship to Project
team() - BelongsTo relationship to Team
isProjectSpecific() - Check if role is project-specific (project_id IS NOT NULL)
isGlobal() - Check if role is a team role (project_id IS NULL)
```

## Controllers

### TeamMappingController (Updated)
- `index()` - Now filters to show only team-only memberships
- Other methods remain unchanged for team management

### ProjectAssignmentController (New)
- `index()` - Dashboard view of all projects with assignment stats
- `create()` - Form to assign team members to projects
- `store()` - Create new project assignment
- `show()` - View all assignments for a specific project
- `destroy()` - Remove project assignment (keeps team membership)
- `updateRole()` - Update project role via AJAX

## Security Notes

- Users can only be assigned to projects if they're already team members
- Project assignments are automatically "accepted" since team membership is already established
- Removing project assignment doesn't affect team membership
- Different permission levels can be implemented at team vs project level

## Usage Examples

### Check if user is team member:
```php
$isTeamMember = TeamMapping::teamOnly()
    ->where('username', $username)
    ->where('team_name', $teamName)
    ->where('invitation_status', 'accepted')
    ->exists();
```

### Get user's project assignments with roles:
```php
$assignments = TeamMapping::projectAssignments()
    ->where('username', $username)
    ->with(['project', 'role'])
    ->get();

// Each assignment can have a different role
foreach($assignments as $assignment) {
    echo "User has role '{$assignment->role_name}' in project '{$assignment->project->proj_name}'";
}
```

### Get project-specific roles for a project:
```php
$projectRoles = Role::where('project_id', $projectId)->get(); // Only project-specific roles
$teamRoles = Role::whereNull('project_id')->get(); // Team roles
$availableProjectRoles = $project->getAvailableRoles(); // Only project-specific roles for assignment
```

### Assign different roles to same user in different projects:
```php
// User can be "Lead Developer" in Project A
TeamMapping::create([
    'username' => 'john_doe',
    'role_name' => 'Lead Developer',
    'team_name' => 'Development Team',
    'project_id' => 1, // Project A
    'invitation_status' => 'accepted'
]);

// And "Tester" in Project B
TeamMapping::create([
    'username' => 'john_doe',
    'role_name' => 'Tester',
    'team_name' => 'Development Team', 
    'project_id' => 2, // Project B
    'invitation_status' => 'accepted'
]);
```

### Get all team members available for project assignment:
```php
$availableMembers = TeamMapping::teamOnly()
    ->where('team_name', $project->team_name)
    ->where('invitation_status', 'accepted')
    ->whereNotIn('username', function($query) use ($projectId) {
        $query->select('username')
              ->from('teammappings')
              ->where('project_id', $projectId);
    })
    ->get();
```
