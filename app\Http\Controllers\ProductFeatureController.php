<?php
//Controller for Project List, Sprint

namespace App\Http\Controllers;
use App\Project;
use App\TeamMapping;
use App\Sprint;
use App\User;
use App\Status;
use App\UserStory;
use App\ProductFeature;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\GeneralNFR;
use App\UserStoryGeneralNfr;
use App\NFR;

class ProductFeatureController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

     public function getGeneralNFRs($projectId)
{
    // Get distinct general_nfr values where proj_id matches the selected project
    $generalNFRs = GeneralNFR::where('proj_id', $projectId)
                    ->whereNotNull('general_nfr')
                    ->distinct()
                    ->pluck('general_nfr');

    // Return as JSON response
    return response()->json($generalNFRs);
}



    //Projects List Page
    public function index(User $id, Project $project)
{
    if (\Auth::check()) {
        $user = \Auth::user();

        if ($user->id === 7) {
            $projects = Project::all();
        } else {
            $teammapping = TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray();
        // $projects = Project::whereIn('team_name', $teammapping)->get();

        // Fetch projects that match the team mapping and where the user has project access
        $projects = Project::whereIn('team_name', $teammapping)
                ->get();
        }

        return view('profeature.index')
            ->with('pros', $projects)
            ->with('title', 'Project');
    } else {
        $allProjects = $project->all(); // Fetch all projects if user not authenticated

        return view('project.index', ['projects' => $allProjects]);
    }
}

    // New projects page
    public function indexReloaded()
    {
        if (Auth::check()) {
            $user = Auth::user();

            // Get project IDs where user has specific project assignments (not just team membership)
            $assignedProjectIds = TeamMapping::where('username', '=', $user->username)
                ->where('invitation_status', 'accepted')
                ->whereNotNull('project_id')
                ->pluck('project_id')
                ->toArray();

            // Get pending project invitations for the alert message
            $pendingInvitations = TeamMapping::where('username', '=', $user->username)
                ->where('invitation_status', 'pending')
                ->whereNotNull('project_id')
                ->get();

            // Fetch projects where user has specific project assignments
            $projects = Project::whereIn('id', $assignedProjectIds)->get();

            return view('project.newIndex', compact('projects', 'pendingInvitations'));
        }
        
        // Redirect to login if not authenticated
        return redirect()->route('login');
    }
    


    //Main Sprint Page
    public function index2($proj_name)
    {
        //Get the project where user's team name(s) is the same with project's team name
        $user = \Auth::user();
        $teammapping = TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); 
        // use pluck() to retrieve an array of team names

        $pro = Project::whereIn('team_name', $teammapping)->get(); 
        // use whereIn() to retrieve the projects that have a team_name value in the array

        //Gets the current project
        $project = Project::where('proj_name', $proj_name)->first();

        //Gets all the sprints related to the project
        // $sprint = Sprint::where('proj_name', '=', "$proj_name")->get();

        $userAccess = \App\UserAccess::where('user_id', $user->id)
        ->where('project_id', $project->id)
        ->first();

        // Get all sprints related to the project that the user has access to
        $sprint = collect(); // Default to an empty collection

        $sprint = Sprint::where('proj_name', $proj_name)->get();

        return view('profeature.index2')
            ->with('title', 'Sprints for ' . $proj_name)
            ->with('sprints', $sprint)
            ->with('pros', $pro)
            ->with('projects', $project);
    }

    public function details($id)
    {
        if (Auth::check()) {
            $user = Auth::user();
            
            // Get user's team names
            $teamNames = TeamMapping::where('username', '=', $user->username)
                ->pluck('team_name')
                ->toArray();
            
            // Find the project and verify user has access to it
            $project = Project::where('id', $id)
                ->whereIn('team_name', $teamNames)
                ->firstOrFail();
                
            // Return the project details view
            return view('project.details', compact('project'));
        }
        
        return redirect()->route('login');
    }

    //Main UserStory Page
    public function index3($sprint_id)
{
    // Get the project where user's team name(s) is the same with project's team name
    $user = \Auth::user();
    $teammapping = \App\TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); // use pluck() to retrieve an array of team names
    $pro = \App\Project::whereIn('team_name', $teammapping)->get(); // use whereIn() to retrieve the projects that have a team_name value in the array

    $statuses = Status::all();

    // Get current sprint
    $sprint = Sprint::where('sprint_id', $sprint_id)->first();

    // Fetch user stories for the sprint
    $userstories = \App\UserStory::where('sprint_id', '=', $sprint_id)->get();

    // Prepare NFR data
    $nfrData = [];
    foreach ($userstories as $userstory) {
        $userStoryNfrs = \App\UserStoryGeneralNfr::where('user_story_id', $userstory->u_id)->get();

        foreach ($userStoryNfrs as $userStoryNfr) {
            // Fetch general NFR
            $generalNFR = \App\GeneralNFR::find($userStoryNfr->general_nfr_id);

            if ($generalNFR) {
                $generalTitle = $generalNFR->general_nfr;

                if (!isset($nfrData[$userstory->u_id][$generalTitle])) {
                    $nfrData[$userstory->u_id][$generalTitle] = [];
                }

                // Fetch specific NFR
                $specificNFR = \App\SpecificNFR::find($userStoryNfr->specific_nfr_id);
                if ($specificNFR) {
                    $nfrData[$userstory->u_id][$generalTitle][] = $specificNFR->specific_nfr;
                }
            }
        }
    }

    // Pass NFR data to the view
    return view('profeature.index3', [
        'userstories' => $userstories,
        'nfrData' => $nfrData,
    ])
        ->with('sprint_id', $sprint->sprint_id)
        ->with('pros', $pro)
        ->with('statuses', $statuses)
        ->with('title', 'User Story for ' . $sprint->sprint_name);
}


    public function backlog($proj_id)
    {
        //Get the project where user's team name(s) is the same with project's team name
        $user = \Auth::user();
        $teammapping = \App\TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); // use pluck() to retrieve an array of team names
        $pro = \App\Project::whereIn('team_name', $teammapping)->get(); // use whereIn() to retrieve the projects that have a team_name value in the array

        //Get current project
        $project = Project::where('id', $proj_id)->first();

        $userstory = \App\UserStory::where('proj_id', $proj_id)
            ->where('sprint_id', '=', 0)
            ->get();

        return view('backlog.index',['userstories'=>$userstory,])
            ->with('project', $project)
            ->with('pros', $pro)
            ->with('title', 'Backlog for ' . $project->proj_name);
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('project.create');
    }

    public function edit2()
    {
        $project = new Project;
        $status = new Status;
        $userStory = new UserStory;
        return view('userstory.edit',['statuses'=>$status->all(),'userstory'=>$userStory, 'projects'=>$project->all()]);
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\ProductFeature  $productFeature
     * @return \Illuminate\Http\Response
     */
    public function show(ProductFeature $productFeature)
    {
        //
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\ProductFeature  $productFeature
     * @return \Illuminate\Http\Response
     */
    public function edit(ProductFeature $productFeature)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\ProductFeature  $productFeature
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProductFeature $productFeature)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\ProductFeature  $productFeature
     * @return \Illuminate\Http\Response
     */

     /**
     * Display a shareable view of the project details.
     *
     * @param  string  $slug
     * @return \Illuminate\Http\Response
     */
    public function sharedView($slug)
    {
        if (Auth::check()) {
            // Find the project by the shareable slug
            $project = Project::where('shareable_slug', $slug)->firstOrFail();
            
            // Set view-only flag to disable edit controls
            $viewOnly = true;
            
            // Return the shared project view
            return view('project.shared', compact('project', 'viewOnly'));
        }
        
        return redirect()->route('login')->with('message', 'You must be logged in to view shared projects.');
    }

    /**
     * Generate a shareable link for a project.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function generateShareableLink(Request $request)
    {
        $projectId = $request->project_id;
        $project = Project::findOrFail($projectId);
        
        // Check if user has permission to share this project
        if (!Auth::user()->can('share_details', $project)) {
            return response()->json(['error' => 'You do not have permission to share this project.'], 403);
        }
        
        // Generate a unique slug if one doesn't exist
        if (empty($project->shareable_slug)) {
            // Create a slug based on project name and a random string
            $slug = \Illuminate\Support\Str::slug($project->proj_name) . '-' . \Illuminate\Support\Str::random(8);
            $project->shareable_slug = $slug;
            $project->save();
        }
        
        $shareableUrl = route('projects.shared', $project->shareable_slug);
        
        return response()->json([
            'success' => true,
            'url' => $shareableUrl
        ]);
    }
}
