<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class EmailNotifier extends Mailable
{
    use Queueable, SerializesModels;

    public $teamName;
    public $roleName;
    public $userEmail;
    public $invitationToken;
    
    public function __construct($teamName, $roleName, $userEmail, $invitationToken)
    {
        $this->teamName = $teamName;
        $this->roleName = $roleName;
        $this->userEmail = $userEmail;
        $this->invitationToken = $invitationToken;
    }

    public function build()
    {
        return $this->from(config('mail.from.address'), config('mail.from.name'))
                    ->subject('Invitation to Join Team ' . $this->teamName)
                    ->markdown('mail.team-invitation');
    }
}
