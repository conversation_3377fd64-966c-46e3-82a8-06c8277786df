<?php

namespace App\Http\Controllers;
use App\Task;
use App\Sprint;
use App\Status;
use App\Project;
use Illuminate\Http\Request;
use Carbon\Carbon;

class BurnDownChartController extends Controller
{
    public function getBurndownData($proj_id, $sprint_id = null)
    {
        $project = Project::where('id', operator: $proj_id)->firstOrFail();
        $projectName = $project->proj_name;
        
        // If no sprint_id provided, get the active sprint
        if (!$sprint_id) {
            $sprint = Sprint::where('proj_name', $projectName)
                ->where('active_sprint', 1)
                ->first();
                
            // If no active sprint, check if there is a sprint with current date between start and end dates
            if (!$sprint) {
                $currentDate = \Carbon\Carbon::now();
                $sprint = Sprint::where('proj_name', $projectName)
                            ->whereNull('active_sprint')
                            ->where('start_sprint', '<=', $currentDate)
                            ->where('end_sprint', '>=', $currentDate)
                            ->first();
                
                // If a sprint was found, set it as active
                if ($sprint) {
                    $sprint->active_sprint = 1;
                    $sprint->save();
                } else {
                    // No active sprint found
                    return view('burndown.index', [
                        'project' => $project,
                        'hasActiveSprint' => false
                    ]);
                }
            }
            
            $sprint_id = $sprint->sprint_id;
        } else {
            $sprint = Sprint::where("sprint_id", $sprint_id)
                ->where(function($query) {
                    $query->where('active_sprint', 1)
                        ->orWhereNull('active_sprint');
                })
                ->firstOrFail();
                
            // If sprint has null active_sprint, set it as active
            if ($sprint->active_sprint === null) {
                $sprint->active_sprint = 1;
                $sprint->save();
            }
        }
        
        $tasks = Task::where('sprint_id', $sprint_id)->get();
        $statuses = Status::where('project_id', $proj_id)->get();
        
        $sprintName = $sprint->sprint_name;
        $startDate = Carbon::parse($sprint->start_sprint);
        $endDate = Carbon::parse($sprint->end_sprint);
        $currentDate = Carbon::now();
        
        // Get total sprint days (inclusive)
        $sprintDays = $startDate->diffInDays($endDate) + 1;
        
        // Calculate total work
        $totalWork = $tasks->count();
        
        // Ideal burndown line calculation
        $idealData = [];
        for ($i = 0; $i <= $sprintDays; $i++) {
            $idealData[$i] = max($totalWork - ($i * ($totalWork / $sprintDays)), 0);
        }
        
        // Actual burndown calculation
        $actualData = [];
        $remainingWork = $totalWork;
        
        for ($i = 0; $i <= $sprintDays; $i++) {
            $date = $startDate->copy()->addDays($i)->format('Y-m-d');
            
            // Stop counting beyond the current date
            if (Carbon::parse($date)->greaterThan($currentDate)) {
                break;
            }
            
            $completedTasks = $tasks->where('completion_date', $date)->count();
            $remainingWork -= $completedTasks;
            $actualData[$i] = max($remainingWork, 0);
        }
        
        return view('burndown.index', [
            'tasks' => $tasks, 
            'statuses' => $statuses, 
            'project' => $project, 
            'sprintName' => $sprintName, 
            'startDate' => $startDate, 
            'endDate' => $endDate,
            'idealData' => $idealData, 
            'actualData' => $actualData,
            'hasActiveSprint' => true,
            'sprint' => $sprint
        ]);
    }

    public function getBurndownDataForArchive($proj_id, $sprint_id)
    {
        $project = Project::where('id', $proj_id)->firstOrFail();
        $sprint = Sprint::findOrFail($sprint_id);
        $tasks = Task::where('sprint_id', $sprint_id)->get();
        
        $startDate = Carbon::parse($sprint->start_sprint);
        $endDate = Carbon::parse($sprint->end_sprint);
        $currentDate = Carbon::now();
        
        // Get total sprint days (inclusive)
        $sprintDays = $startDate->diffInDays($endDate) + 1;
        
        // Calculate total work
        $totalWork = $tasks->count();
        
        // Ideal burndown line calculation
        $idealData = [];
        for ($i = 0; $i <= $sprintDays; $i++) {
            $idealData[$i] = max($totalWork - ($i * ($totalWork / $sprintDays)), 0);
        }
        
        // Actual burndown calculation
        $actualData = [];
        $remainingWork = $totalWork;
        
        for ($i = 0; $i <= $sprintDays; $i++) {
            $date = $startDate->copy()->addDays($i)->format('Y-m-d');
            
            // For archive, we want all data up to the end date
            if (Carbon::parse($date)->greaterThan($endDate)) {
                break;
            }
            
            $completedTasks = $tasks->where('completion_date', $date)->count();
            $remainingWork -= $completedTasks;
            $actualData[$i] = max($remainingWork, 0);
        }
        
        return [
            'idealData' => $idealData,
            'actualData' => $actualData
        ];
    }
}