@php
    // Find the project once to use in permission checks
    $project = App\Project::find($project_id);
@endphp
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Stories</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            margin: 0;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .table {
            background-color: white;
            border-radius: 0.25rem;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .button-container {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        .success-alert {
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
            border-radius: 0.25rem;
        }
        .table-success {
            background-color: rgba(212, 237, 218, 0.5);
        }
        .table-danger {
            background-color: rgba(248, 215, 218, 0.5);
        }
        .status-form {
            display: flex;
            align-items: center;
        }
        .status-form select {
            flex-grow: 1;
            margin-right: 0.5rem;
        }
        .status-form button {
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        @if(session('success'))
        <div class="success-alert">
            {{ session('success') }}
        </div>
        @endif

        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">User Stories</h4>
                </div>

                <div class="table-responsive">
                    <table id="userstories" class="table table-bordered">
                        <thead>
                            <tr>
                                <th>User Story</th>
                                <th>Assigned To</th>
                                <th>Status</th>
                                <th>NFR Details</th>
                                <th>Task</th>
                                <th>Edit</th> 
                                <th>Delete</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($userstories as $userstory)
                                @php
                                    // Check if the user story has NFRs linked
                                    $hasNfr = isset($nfrData[$userstory->u_id]) && count($nfrData[$userstory->u_id]) > 0;
                                @endphp
                                <tr class="{{ $hasNfr ? 'table' : 'table' }}"> 
                                    <td>{{ $userstory->user_story }}</td>
                                    <td>
                                        @php
                                            // Decode user_names and sanitize
                                            $userNames = json_decode(trim($userstory->user_names, '"'), true);
                                        @endphp

                                        @if (is_array($userNames))
                                            @foreach($userNames as $user_show)
                                                - {{ $user_show }}<br>
                                            @endforeach
                                        @else
                                            <p>-</p>
                                        @endif
                                    </td>
                                    <td>
                                        @can('editStatus_userstory', $project)
                                        <select name="status_id" class="form-select form-select-sm status-select" data-userstory-id="{{ $userstory->u_id }}">
                                            @foreach($statuses as $status)
                                                <option value="{{ $status->id }}" {{ $userstory->status_id == $status->id ? 'selected' : '' }}>
                                                    {{ $status->title }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @else
                                        <span>{{ $statuses->where('id', $userstory->status_id)->first()->title ?? 'Unknown' }}</span>
                                        @endcan
                                    </td>
                                    <td>
                                        @if ($hasNfr)
                                            <a href="{{ route('userstory.viewDetails', [$userstory->u_id]) }}" class="btn btn-secondary">View Details</a>
                                        @else
                                            <p>No NFRs assigned</p>
                                        @endif
                                    </td>
                                    <td>
                                        @can('view_task', $project)
                                        <a href="{{ action('TaskController@index', $userstory['u_id']) }}" class="btn btn-primary">View</a>
                                        @endcan
                                    </td>
                                    <td>
                                        @can('edit_userstory', $project)
                                        <a href="{{ route('userstory.edit', [$userstory->u_id]) }}" class="btn btn-secondary">Edit</a>
                                        @endcan
                                    </td>
                                    <td>
                                        @can('delete_userstory', $project)
                                        <a href="{{ route('userstory.destroy', $userstory) }}" class="btn btn-danger" 
                                           onclick="return confirm('Are you sure you want to delete this User Story?');">
                                            Delete
                                        </a>
                                        @endcan
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7">No user stories added yet</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                <div class="button-container mt-4">
                    @can('add_userstory', $project)
                    <a href="{{ route('userstory.create', ['proj_id' => $project_id]) }}" class="btn btn-success">Create User Story</a>
                    @endcan
                </div>
                
                <div class="button-container mt-4">
                    <a href="{{ route('ucd.index', ['project_id' => $project_id]) }}" class="btn btn-success">UCD</a>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
          // Only run if user has permission to edit status (the select elements will exist)
          if (document.querySelectorAll('.status-select').length > 0) {
              // Select all status dropdowns
              const statusSelects = document.querySelectorAll('.status-select');
              
              // Add change event listener to each dropdown
              statusSelects.forEach(select => {
                  select.addEventListener('change', function() {
                  const userstoryId = this.getAttribute('data-userstory-id');
                  const statusId = this.value;
                  
                  // Create form data
                  const formData = new FormData();
                  formData.append('status_id', statusId);
                  formData.append('_token', '{{ csrf_token() }}');
                  formData.append('_method', 'PATCH');
                  
                  // Send AJAX request
                  fetch(`{{ url('userstory') }}/${userstoryId}/updateStatus`, {
                      method: 'POST',
                      body: formData
                  })
                  .then(response => {
                      if (!response.ok) {
                          throw new Error('Network response was not ok');
                      }
                      return response.json();
                  })
                  .then(data => {
                      // Show a temporary success message if needed
                      if (data.success) {
                          // Optional: Show a temporary success message
                          const messageContainer = document.createElement('div');
                          messageContainer.className = 'alert alert-success position-fixed top-0 end-0 m-3';
                          messageContainer.textContent = data.message;
                          document.body.appendChild(messageContainer);
                          
                          // Remove the message after 3 seconds
                          setTimeout(() => {
                              messageContainer.remove();
                          }, 3000);
                      }
                  })
                  .catch(error => {
                      console.error('Error updating status:', error);
                  });
              })  
            });
          }
      });
    </script>
</body>
</html>