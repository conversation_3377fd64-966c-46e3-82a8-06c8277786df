@php
    // Make sure we have a proper project object for permission checks
    $projectObj = $project instanceof \App\Project ? $project : \App\Project::find($project->id ?? null);
@endphp
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sprint Archives - {{ $project->proj_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
        }
        .archive-card {
            transition: transform 0.2s;
        }
        .archive-card:hover {
            transform: translateY(-2px);
        }
        .view-btn {
            width: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="mb-4">
            <i class="fas fa-archive"></i>
            Sprint Archives
        </h2>

        @if($archivedSprints->isEmpty())
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                No archived sprints found for this project.
            </div>
        @else
            <div class="row">
                @foreach($archivedSprints as $sprint)
                    <div class="col-md-6 mb-4">
                        <div class="card archive-card">
                            <div class="card-body">
                                <h5 class="card-title">{{ $sprint->sprint_name }}</h5>
                                <p class="card-text">
                                    <small class="text-muted">
                                        <i class="far fa-calendar-alt"></i>
                                        {{ \Carbon\Carbon::parse($sprint->start_sprint)->format('j M Y') }} -
                                        {{ \Carbon\Carbon::parse($sprint->end_sprint)->format('j M Y') }}
                                    </small>
                                </p>
                                <p class="card-text">{{ $sprint->sprint_desc }}</p>
                                <div class="d-flex justify-content-end gap-2">
                                    @can('viewKanbanArchive_sprintArchive', $projectObj)
                                    <a href="{{ route('sprint.viewArchiveKanban', ['sprint_id' => $sprint->sprint_id]) }}" 
                                       class="btn btn-primary view-btn">
                                        <i class="fas fa-columns"></i>
                                        Kanban
                                    </a>
                                    @endcan
                                    @can('viewBurndownArchive_sprintArchive', $projectObj)
                                    <a href="{{ route('sprint.viewArchiveBurndown', ['sprint_id' => $sprint->sprint_id]) }}" 
                                       class="btn btn-info view-btn">
                                        <i class="fas fa-chart-line"></i>
                                        Burndown
                                    </a>
                                    @endcan
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 