@extends('layouts.commonMaster')

@php
$isMenu = false;
@endphp

@php
/* Display elements */
$contentNavbar = false;
$containerNav = ($containerNav ?? 'container-fluid'); // Changed to full width
$isNavbar = ($isNavbar ?? true);
$isMenu = ($isMenu ?? true);
$isFlex = ($isFlex ?? false);
$isFooter = ($isFooter ?? true);

/* HTML Classes */
$navbarDetached = 'navbar-detached';

/* Content classes */
$container = ($container ?? 'container-fluid'); // Changed to full width

@endphp

@section('layoutContent')
<div class="layout-wrapper layout-content-navbar {{ $isMenu ? '' : 'layout-without-menu' }}">
  <div class="layout-container">

    <!-- Layout page -->
    <div class="layout-page">

      <!-- Content wrapper -->
      <div class="content-wrapper">

        <!-- Content -->
        @if ($isFlex)
        <div class="{{ $container }} d-flex align-items-stretch flex-grow-1 p-0">
        @else
        <div class="{{ $container }} flex-grow-1 container-p-y px-0"> {{-- Remove horizontal padding --}}
        @endif

          @yield('content')

        </div>
        <!-- / Content -->

        <div class="content-backdrop fade"></div>
      </div>
      <!--/ Content wrapper -->
    </div>
    <!-- / Layout page -->

  </div>

  @if ($isMenu)
  <!-- Overlay -->
  <div class="layout-overlay layout-menu-toggle"></div>
  @endif

  <!-- Drag Target Area To SlideIn Menu On Small Screens -->
  <div class="drag-target"></div>
</div>
<!-- / Layout wrapper -->
@endsection
