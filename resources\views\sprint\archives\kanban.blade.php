<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archived Kanban - {{ $sprint->sprint_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
        }
        .kanban-board {
            display: flex;
            gap: 1rem;
            overflow-x: auto;
            padding: 1rem;
        }
        .swim-lane {
            flex: 0 0 300px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem;
        }
        .lane-header {
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #eee;
        }
        .task-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        .archive-header {
            background: #fff;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .archive-info {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="archive-header">
            <h2 class="mb-2">{{ $sprint->sprint_name }} - Archived Kanban Board</h2>
            <div class="archive-info">
                <p class="mb-1">
                    <i class="fas fa-calendar"></i> 
                    Sprint Period: {{ \Carbon\Carbon::parse($sprint->start_sprint)->format('j M Y') }} - 
                    {{ \Carbon\Carbon::parse($sprint->end_sprint)->format('j M Y') }}
                </p>
                <p class="mb-1">
                    <i class="fas fa-archive"></i>
                    Archived on: {{ $archive->archived_at->format('j M Y, H:i') }}
                </p>
            </div>
        </div>

        <div class="kanban-board">
            @foreach($statuses as $status)
                <div class="swim-lane">
                    <div class="lane-header">
                        <h4>{{ $status->title }}</h4>
                    </div>
                    <div class="lane-content">
                        @if(isset($archive->kanban_state[$status->id]))
                            @foreach($archive->kanban_state[$status->id] as $task)
                                <div class="task-card">
                                    <h5>{{ $task['title'] }}</h5>
                                    <p class="mb-0">{{ $task['description'] }}</p>
                                    @if(isset($task['user_names']))
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                @php
                                                    $userNames = json_decode($task['user_names'], true);
                                                    $assignedUsers = is_array($userNames) && !empty($userNames) ? implode(', ', $userNames) : 'Unassigned';
                                                @endphp
                                                Assigned to: {{ $assignedUsers }}
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>
            @endforeach
        </div>

        <div class="mt-4">
            <a href="{{ route('sprint.archives', ['proj_id' => $project->id]) }}" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i>
                Back to Archives
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 