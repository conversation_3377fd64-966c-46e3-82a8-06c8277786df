<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('sprint_archives', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sprint_id');
            $table->json('kanban_state')->nullable(); // Store the kanban board state
            $table->json('burndown_data')->nullable(); // Store the burndown chart data
            $table->timestamp('archived_at');
            $table->timestamps();

            $table->foreign('sprint_id')->references('sprint_id')->on('sprint')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('sprint_archives');
    }
}; 