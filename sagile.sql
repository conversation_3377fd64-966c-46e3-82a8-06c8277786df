


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
SET NAMES utf8mb4;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table attachments
# ------------------------------------------------------------

DROP TABLE IF EXISTS `attachments`;

CREATE TABLE `attachments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `file_path` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table boards
# ------------------------------------------------------------

DROP TABLE IF EXISTS `boards`;

CREATE TABLE `boards` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `boardId` int NOT NULL,
  `totalTaskFilter` int NOT NULL,
  `tasksDoneFilter` int NOT NULL,
  `slug` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table bugscore
# ------------------------------------------------------------

DROP TABLE IF EXISTS `bugscore`;

CREATE TABLE `bugscore` (
  `id` int NOT NULL AUTO_INCREMENT,
  `project_id` int NOT NULL,
  `severity_weight` decimal(5,2) NOT NULL,
  `status_weight` decimal(5,2) NOT NULL,
  `due_weight` decimal(5,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table bugtrack
# ------------------------------------------------------------

DROP TABLE IF EXISTS `bugtrack`;

CREATE TABLE `bugtrack` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `project_id` bigint unsigned DEFAULT NULL,
  `title` varchar(191) NOT NULL,
  `description` text NOT NULL,
  `severity` varchar(191) NOT NULL DEFAULT 'medium',
  `status` varchar(191) NOT NULL DEFAULT 'open',
  `flow` varchar(191) DEFAULT NULL,
  `expected_results` text,
  `actual_results` text,
  `attachment` varchar(191) DEFAULT NULL,
  `assigned_to` bigint unsigned DEFAULT NULL,
  `reported_by` bigint unsigned NOT NULL,
  `due_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table burndownchart
# ------------------------------------------------------------

DROP TABLE IF EXISTS `burndownchart`;

CREATE TABLE `burndownchart` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `task_name` varchar(191) NOT NULL,
  `description` text,
  `story_points` smallint unsigned NOT NULL DEFAULT '0',
  `due_date` date DEFAULT NULL,
  `status` enum('Not Started','In Progress','Completed') NOT NULL DEFAULT 'Not Started',
  `user_name` varchar(191) NOT NULL,
  `task_id` bigint unsigned NOT NULL,
  `proj_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table calendar
# ------------------------------------------------------------

DROP TABLE IF EXISTS `calendar`;

CREATE TABLE `calendar` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `status` varchar(191) NOT NULL DEFAULT 'pending',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table charts
# ------------------------------------------------------------

DROP TABLE IF EXISTS `charts`;

CREATE TABLE `charts` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `boardId` int NOT NULL,
  `sprintname` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `storyPointsTotal` double NOT NULL,
  `tasksTotal` double NOT NULL,
  `tasksDone` double NOT NULL,
  `storyPointsDone` double NOT NULL,
  `startDate` date NOT NULL,
  `endDate` date NOT NULL,
  `sprintDay` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table cig
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cig`;

CREATE TABLE `cig` (
  `cig_id` int NOT NULL AUTO_INCREMENT,
  `sprint_id` bigint unsigned NOT NULL,
  `u_id` bigint unsigned NOT NULL,
  `general_nfr_id` bigint unsigned NOT NULL,
  `specific_nfr_id` int NOT NULL,
  `proj_id` bigint unsigned NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`cig_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table coding_standards
# ------------------------------------------------------------

DROP TABLE IF EXISTS `coding_standards`;

CREATE TABLE `coding_standards` (
  `codestand_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `codestand_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`codestand_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `coding_standards` WRITE;
/*!40000 ALTER TABLE `coding_standards` DISABLE KEYS */;

INSERT INTO `coding_standards` (`codestand_id`, `codestand_name`, `created_at`, `updated_at`) VALUES
	(1, 'simpleStandard', '2025-05-23 18:44:49', '2025-05-23 18:44:49');

/*!40000 ALTER TABLE `coding_standards` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table comments
# ------------------------------------------------------------

DROP TABLE IF EXISTS `comments`;

CREATE TABLE `comments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `forum_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `content` text NOT NULL,
  `parent_comment_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `comments` WRITE;
/*!40000 ALTER TABLE `comments` DISABLE KEYS */;

INSERT INTO `comments` (`id`, `forum_id`, `user_id`, `content`, `parent_comment_id`, `created_at`, `updated_at`) VALUES
	(1, 4, 1, 'comment 1', NULL, '2024-06-19 06:12:53', '2024-06-19 06:12:53');

/*!40000 ALTER TABLE `comments` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table defect_features
# ------------------------------------------------------------

DROP TABLE IF EXISTS `defect_features`;

CREATE TABLE `defect_features` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table diagram_components
# ------------------------------------------------------------

DROP TABLE IF EXISTS `diagram_components`;

CREATE TABLE `diagram_components` (
  `id` varchar(36) NOT NULL,
  `node_id` varchar(255) NOT NULL,
  `diagram_id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `version` varchar(50) DEFAULT NULL,
  `deletable` tinyint(1) DEFAULT '1',
  `created_by` varchar(255) DEFAULT NULL,
  `last_updated_by` varchar(255) DEFAULT NULL,
  `preconditions` json DEFAULT NULL,
  `postconditions` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_node_diagram` (`node_id`,`diagram_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `diagram_components` WRITE;
/*!40000 ALTER TABLE `diagram_components` DISABLE KEYS */;

INSERT INTO `diagram_components` (`id`, `node_id`, `diagram_id`, `name`, `description`, `version`, `deletable`, `created_by`, `last_updated_by`, `preconditions`, `postconditions`) VALUES
	('4681cb07-d1af-49f6-b785-7d573e34a140', 'actor_1', '30', 'Project Manager', 'This use case is ...', '1.0', 1, 'system', 'system', '[]', '[]'),
	('52fa5a00-95e9-4081-ae7b-977d4b187571', 'usecase_1', '37', 'perform UAT tests', 'This use case is ...', '1.0', 1, 'system', 'system', '[]', '[]'),
	('bdbfc950-4003-4495-9ca1-3404522ea0d5', 'usecase_1', '30', 'Eat', 'This use case is funny!', '1.2', 1, 'system', 'system', '[\"Precondition1\",\"Pre2\"]', '[\"Post1\"]'),
	('c1a81b47-7a00-4bde-b1ab-aa4cb247d288', 'usecase_2', '30', 'drinkibng', 'This use case is ...', '1.0', 1, 'system', 'system', '[\"asdf\"]', '[\"asdf\"]'),
	('cf3b7feb-c685-40bf-962b-f0acc7b64766', 'usecase_3', '30', 'full', 'This use case is asdf ...', '1.0', 1, 'system', 'system', '[]', '[]');

/*!40000 ALTER TABLE `diagram_components` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table diagram_usecase_specification
# ------------------------------------------------------------

DROP TABLE IF EXISTS `diagram_usecase_specification`;

CREATE TABLE `diagram_usecase_specification` (
  `id` varchar(36) NOT NULL,
  `usecase_id` varchar(36) NOT NULL,
  `type` varchar(50) NOT NULL DEFAULT 'NORMAL',
  `name` varchar(255) NOT NULL,
  `entry_point` varchar(255) DEFAULT NULL,
  `exit_point` varchar(255) DEFAULT NULL,
  `steps` varchar(4000) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `usecase_id` (`usecase_id`),
  CONSTRAINT `diagram_usecase_specification_ibfk_1` FOREIGN KEY (`usecase_id`) REFERENCES `diagram_components` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `diagram_usecase_specification` WRITE;
/*!40000 ALTER TABLE `diagram_usecase_specification` DISABLE KEYS */;

INSERT INTO `diagram_usecase_specification` (`id`, `usecase_id`, `type`, `name`, `entry_point`, `exit_point`, `steps`) VALUES
	('2be20534-b6f4-46bb-93d1-65ce4e73f78c', '4681cb07-d1af-49f6-b785-7d573e34a140', 'NORMAL', 'Normal flow', NULL, NULL, '[{\"id\":\"step-1\",\"description\":\"Project manager click user page\"},{\"id\":\"step-2\",\"description\":\"Project manager add user detail\"},{\"id\":\"step-3\",\"description\":\"User detail are stored in db\"},{\"id\":\"step-4\",\"description\":\"Project manager view status\"}]'),
	('7ab63927-b37d-4315-a1a3-d7587469088a', 'c1a81b47-7a00-4bde-b1ab-aa4cb247d288', 'NORMAL', 'Normal flow', NULL, NULL, '[{\"id\":\"step-1\",\"description\":\"asdfasdfasdf\"}]'),
	('87d21495-a42f-4249-bf1d-67a5322a8a80', 'cf3b7feb-c685-40bf-962b-f0acc7b64766', 'NORMAL', 'Normal flow', NULL, NULL, '[{\"id\":\"step-1\",\"description\":\"User add new information\"},{\"id\":\"step-2\",\"description\":\"User click add\"},{\"id\":\"step-3\",\"description\":\"User see content hi\"}]'),
	('ab51ed2e-3991-417e-bdff-c00020ef0b9c', 'bdbfc950-4003-4495-9ca1-3404522ea0d5', 'NORMAL', 'Normal flow', NULL, NULL, '[{\"id\":\"step-1\",\"description\":\"Step1\"},{\"id\":\"step-2\",\"description\":\"Step2\"}]'),
	('db8a1260-b33d-4343-9d08-c64d129d2507', '52fa5a00-95e9-4081-ae7b-977d4b187571', 'NORMAL', 'Normal flow', NULL, NULL, '[]');

/*!40000 ALTER TABLE `diagram_usecase_specification` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table diagrams
# ------------------------------------------------------------

DROP TABLE IF EXISTS `diagrams`;

CREATE TABLE `diagrams` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `project_id` varchar(36) NOT NULL,
  `diagram_element` json NOT NULL,
  `original_plantuml` text NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_project` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `diagrams` WRITE;
/*!40000 ALTER TABLE `diagrams` DISABLE KEYS */;

INSERT INTO `diagrams` (`id`, `name`, `project_id`, `diagram_element`, `original_plantuml`) VALUES
	('4136f0db-07c4-46bd-9860-9c31a26e0b2d', 'teatat', '37', '{\"edges\":[{\"id\":\"eactor_1-usecase_1\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecase_1\",\"selected\":false,\"sourceHandle\":\"right\",\"targetHandle\":\"left\"}],\"nodes\":[{\"id\":\"actor_1\",\"data\":{\"type\":\"actor\",\"label\":\"Project Manager\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":117,\"height\":148},\"position\":{\"x\":-101.67845371428916,\"y\":34.4083011429277},\"selected\":false},{\"id\":\"usecase_1\",\"data\":{\"type\":\"usecase\",\"label\":\"perform UAT tests\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":234.05468971425304,\"y\":59.23149542859035},\"selected\":true},{\"id\":\"package_1\",\"data\":{\"type\":\"package\",\"label\":\"teatat \",\"width\":300,\"height\":230},\"type\":\"package\",\"width\":250,\"height\":349,\"zIndex\":-11,\"dragging\":false,\"measured\":{\"width\":250,\"height\":349},\"position\":{\"x\":181.82908048457344,\"y\":-26.30801192364052},\"resizing\":false,\"selected\":false},{\"id\":\"usecase_1-1750787876951\",\"data\":{\"type\":\"usecase\",\"label\":\"Create New UAT use case\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":369.3808486044157,\"y\":179.1543134263884},\"selected\":false}]}', '\n@startuml\nleft to right direction\n\nactor \"Project Manager\"\n\nrectangle teatat {\n\tusecase \"perform UAT tests\"\n}\n\n\"Project Manager\" --> \"perform UAT tests\"\n\n@enduml'),
	('763df313-5f9f-4e65-8c1b-f24309d97c64', 'TestAddNewCount', '41', '{\"edges\":[{\"id\":\"eactor_1-usecase_1\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecase_1\",\"sourceHandle\":\"right\",\"targetHandle\":\"left\"},{\"id\":\"eactor_2-usecase_2\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_2\",\"target\":\"usecase_2\",\"sourceHandle\":\"left\",\"targetHandle\":\"right\"}],\"nodes\":[{\"id\":\"actor_1\",\"data\":{\"type\":\"actor\",\"label\":\"Project Manager\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":117,\"height\":148},\"position\":{\"x\":-100,\"y\":0}},{\"id\":\"actor_2\",\"data\":{\"type\":\"actor\",\"label\":\"Developer\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":80,\"height\":124},\"position\":{\"x\":600,\"y\":120}},{\"id\":\"usecase_1\",\"data\":{\"type\":\"usecase\",\"label\":\"Create new projects\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":250,\"y\":50}},{\"id\":\"usecase_2\",\"data\":{\"type\":\"usecase\",\"label\":\"Test User Story\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":250,\"y\":170}},{\"id\":\"package_1\",\"data\":{\"type\":\"package\",\"label\":\"TestAddNewCount \",\"width\":300,\"height\":350},\"type\":\"package\",\"height\":440,\"zIndex\":-11,\"measured\":{\"width\":300,\"height\":440},\"position\":{\"x\":200,\"y\":0}}]}', '\n@startuml\nleft to right direction\n\nactor \"Project Manager\"\nactor \"Developer\"\n\nrectangle TestAddNewCount {\n\tusecase \"Create new projects\"\n\tusecase \"Test User Story\"\n}\n\n\"Project Manager\" --> \"Create new projects\"\n\"Test User Story\" <-- \"Developer\"\n\n@enduml'),
	('fa9151b6-f7eb-4d78-ab69-3057f7a0f81c', 'PROJECT-TAUFIQ', '30', '{\"edges\":[{\"id\":\"eactor_1-usecase_2\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecase_2\",\"sourceHandle\":\"right\",\"targetHandle\":\"left\"},{\"id\":\"eactor_1-usecase_3\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecase_3\",\"sourceHandle\":\"right\",\"targetHandle\":\"left\"},{\"id\":\"eusecase_3-usecase_1-include\",\"data\":{\"type\":\"include\"},\"type\":\"include\",\"label\":\"«include»\",\"style\":{\"strokeDasharray\":\"5 5\"},\"source\":\"usecase_3\",\"target\":\"usecase_1\",\"markerEnd\":{\"type\":\"arrowclosed\",\"color\":\"#b1b1b7\",\"width\":20,\"height\":20},\"labelStyle\":{\"fill\":\"#000\",\"fontFamily\":\"monospace\"},\"sourceHandle\":\"right\",\"targetHandle\":\"left\"}],\"nodes\":[{\"id\":\"actor_1\",\"data\":{\"type\":\"actor\",\"label\":\"Project Manager\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":117,\"height\":148},\"position\":{\"x\":-100,\"y\":0}},{\"id\":\"usecase_1\",\"data\":{\"type\":\"usecase\",\"label\":\"eat\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":269.1096535376823,\"y\":49.06354515050168},\"selected\":false},{\"id\":\"usecase_2\",\"data\":{\"type\":\"usecase\",\"label\":\"drinkibng\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":250,\"y\":170},\"selected\":false},{\"id\":\"usecase_3\",\"data\":{\"type\":\"usecase\",\"label\":\"full\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":250,\"y\":290}},{\"id\":\"package_1\",\"data\":{\"type\":\"package\",\"label\":\"PROJECT-TAUFIQ \",\"width\":300,\"height\":470},\"type\":\"package\",\"height\":560,\"zIndex\":-11,\"measured\":{\"width\":300,\"height\":560},\"position\":{\"x\":200,\"y\":0}}]}', '\n@startuml\nleft to right direction\n\nactor \"Project Manager\"\n\nrectangle PROJECT-TAUFIQ {\n\tusecase \"eat\"\n\tusecase \"drink\"\n\tusecase \"full\"\n}\n\n\"Project Manager\" --> \"drink\"\n\"Project Manager\" --> \"full\"\n\"full\" .> \"eat\" : include\n\n@enduml');

/*!40000 ALTER TABLE `diagrams` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table failed_jobs
# ------------------------------------------------------------

DROP TABLE IF EXISTS `failed_jobs`;

CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table forum_favorites
# ------------------------------------------------------------

DROP TABLE IF EXISTS `forum_favorites`;

CREATE TABLE `forum_favorites` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `forum_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table forums
# ------------------------------------------------------------

DROP TABLE IF EXISTS `forums`;

CREATE TABLE `forums` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `category` varchar(191) NOT NULL,
  `assigned_users` longtext,
  `content` longtext NOT NULL,
  `private_forum` tinyint NOT NULL DEFAULT '0',
  `private_users` longtext,
  `image_urls` text,
  `user_id` bigint unsigned NOT NULL,
  `project_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `forums` WRITE;
/*!40000 ALTER TABLE `forums` DISABLE KEYS */;

INSERT INTO `forums` (`id`, `title`, `category`, `assigned_users`, `content`, `private_forum`, `private_users`, `image_urls`, `user_id`, `project_id`, `created_at`, `updated_at`) VALUES
	(1, 'forumnew', 'Category 2', NULL, 'forumcontent', 0, NULL, NULL, 1, 4, '2024-06-13 15:39:43', '2024-06-13 15:39:43'),
	(2, 'forum1', 'Category 1', NULL, 'content', 0, NULL, NULL, 1, 4, '2024-06-13 15:40:03', '2024-06-13 15:40:03'),
	(3, 'forum2', 'Category 3', NULL, 'content23', 0, NULL, 'https://buffer.com/library/content/images/size/w1200/2023/10/free-images.jpg', 1, 4, '2024-06-13 15:42:13', '2024-06-19 06:04:47'),
	(4, 'forumtitle', 'Category 2', NULL, 'forum is interesting', 0, NULL, 'https://buffer.com/library/content/images/size/w1200/2023/10/free-images.jpg', 1, 4, '2024-06-19 06:12:16', '2024-06-19 06:12:43');

/*!40000 ALTER TABLE `forums` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table generalnfr
# ------------------------------------------------------------

DROP TABLE IF EXISTS `generalnfr`;

CREATE TABLE `generalnfr` (
  `general_nfr_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `general_nfr` varchar(255) NOT NULL,
  `general_nfr_desc` varchar(255) DEFAULT NULL,
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`general_nfr_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `generalnfr` WRITE;
/*!40000 ALTER TABLE `generalnfr` DISABLE KEYS */;

INSERT INTO `generalnfr` (`general_nfr_id`, `general_nfr`, `general_nfr_desc`, `created_by`, `created_at`, `updated_at`) VALUES
	(1, 'testRequirement', 'asd', 4, '2025-05-23 11:55:57', '2025-05-23 11:55:57');

/*!40000 ALTER TABLE `generalnfr` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table mappings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `mappings`;

CREATE TABLE `mappings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ustory_id` int NOT NULL,
  `type_NFR` int NOT NULL,
  `id_NFR` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table migrations
# ------------------------------------------------------------

DROP TABLE IF EXISTS `migrations`;

CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(191) NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
	(1, '2014_10_12_100000_create_password_resets_table', 1),
	(2, '2016_06_01_000001_create_oauth_auth_codes_table', 1),
	(3, '2016_06_01_000002_create_oauth_access_tokens_table', 1),
	(4, '2016_06_01_000003_create_oauth_refresh_tokens_table', 1),
	(5, '2016_06_01_000004_create_oauth_clients_table', 1),
	(6, '2016_06_01_000005_create_oauth_personal_access_clients_table', 1),
	(7, '2016_12_06_110937_create_charts_table', 1),
	(8, '2017_03_30_173152_create_boards_table', 1),
	(9, '2019_03_16_084930_create_roles_table', 1),
	(10, '2019_03_27_154105_create_projects_table', 1),
	(11, '2019_05_05_171853_create_priorities_table', 1),
	(12, '2019_05_05_174636_create_security_features_table', 1),
	(13, '2019_05_07_143235_create_performance_features_table', 1),
	(14, '2019_05_09_031717_create_product_features_table', 1),
	(15, '2019_05_26_195719_create_defect_features_table', 1),
	(16, '2019_06_29_163059_create_mappings_table', 1),
	(17, '2019_08_19_000000_create_failed_jobs_table', 1),
	(18, '2020_05_27_040214_create_tasks_table', 1),
	(19, '2020_05_27_042541_create_statuses_table', 1),
	(20, '2020_08_09_024542_create_teams_table', 1),
	(21, '2020_08_18_123517_create_users_table', 1),
	(27, '2020_08_20_012325_create_attachments_table', 2),
	(28, '2020_08_23_090144_create_team_mappings_table', 2),
	(29, '2020_09_12_015732_create_sprint_table', 2),
	(30, '2020_09_14_083251_create_user_stories_table', 2),
	(31, '2020_09_17_133209_create_coding_standards_table', 2),
	(32, '2023_12_10_144643_tambahan', 3),
	(33, '2023_09_08_031714_create_comments_table', 4),
	(34, '2023_09_12_004537_create_forums_table', 4),
	(35, '2023_11_26_085500_create_burndownchart_table', 4),
	(36, '2023_12_10_143945_create_forum_favorites_table', 4),
	(37, '2023_12_13_094806_remove_hours_from_tasks_table', 4),
	(38, '2023_12_13_100711_add_ideal_hours_per_day_to_sprint_table', 4),
	(39, '2023_12_16_073958_add_actual_hours_per_day_to_sprint', 4),
	(40, '2024_01_01_070140_add_hours_spent_to_sprint_table', 5),
	(41, '2023_12_12_143947_create_permission_table', 6),
	(42, '2024_01_08_144450_add_proj_name_to_teams_table', 7),
	(43, '2024_01_09_053634_add_user_name_to_user_stories', 7),
	(44, '2024_01_09_060749_drop_user_name_from_tasks', 7),
	(45, '2024_01_09_060837_add_user_names_to_tasks', 7),
	(46, '2024_01_09_063232_remove_user_name_column_from_user_stories_table', 8),
	(47, '2024_01_09_065208_add_user_names_to_user_stories', 8),
	(48, '2024_01_10_063354_add_team_names_to_teams_table', 9),
	(49, '2024_01_11_143732_add_completion_date_to_tasks_table', 10),
	(50, '2024_01_24_090533_add_new_task_update_to_tasks_table', 11),
	(51, '2024_04_15_142912_create_calendar_table', 12),
	(52, '2024_04_21_171637_create_bugtrack_table', 12),
	(53, '2024_06_13_122955_add_project_id_to_bugtrack_table', 13),
	(54, '2024_06_13_123116_rename_image_urls_to_image_url_in_forums_table', 14),
	(55, '2024_06_29_142233_create_permissions_table', 14),
	(56, '2024_06_29_142347_create_role_user_table', 15),
	(57, '2024_06_29_142441_create_permission_role_table', 16),
	(58, '2024_06_29_142651_create_project_user_table', 17),
	(59, '2024_06_29_142528_create_forums_table', 17),
	(60, '2024_06_29_142623_create_security_feature_user_story_table', 17),
	(61, '2024_07_12_110436_add_due_date_to_bugtrack_table', 18),
	(62, '2024_07_12_162526_create_bugscore_table', 17),
	(63, '2024_07_16_031057_add_private_columns_to_forums_table', 19),
	(64, '2024_07_16_032612_add_parent_comment_id_to_comments_table', 19),
	(65, '2024_07_16_184532_add_status_to_calendar_events_table', 19),
	(66, '2024_07_16_201403_create_replies_table', 19),
	(67, '2024_07_16_205122_add_assigned_users_to_your_table_name', 19),
	(68, '2024_11_05_114308_create_generalnfr_table', 18),
	(69, '2024_11_06_182509_create_tvt_table', 18),
	(70, '2024_11_17_025244_create_user_story_general_nfr_table', 18),
	(71, '2025_05_15_234255_add_key_and_description_to_permission_table', 20),
	(72, '2025_05_16_011912_add_team_id_to_roles_table', 20),
	(73, '2024_03_21_000000_add_staff_id_to_users_table', 21),
	(74, '2024_03_22_000000_modify_user_identifiers', 22),
	(75, '2025_05_28_create_sprint_archives_table', 23),
	(76, '2025_06_15_190732_add_slug_to_projects_table', 24),
	(77, '2025_06_18_000001_add_invitation_status_to_teammappings', 25),
	(79, '2025_06_19_000000_add_project_id_to_roles_table', 26),
	(80, '2025_06_23_112047_add_project_id_to_teammappings_table', 27);

/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table nfr
# ------------------------------------------------------------

DROP TABLE IF EXISTS `nfr`;

CREATE TABLE `nfr` (
  `nfr_id` int NOT NULL AUTO_INCREMENT,
  `general_nfr_id` bigint unsigned NOT NULL,
  `specific_nfr` varchar(255) NOT NULL,
  `specific_nfr_desc` varchar(255) DEFAULT NULL,
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`nfr_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `nfr` WRITE;
/*!40000 ALTER TABLE `nfr` DISABLE KEYS */;

INSERT INTO `nfr` (`nfr_id`, `general_nfr_id`, `specific_nfr`, `specific_nfr_desc`, `created_by`, `created_at`, `updated_at`) VALUES
	(1, 1, 'testRequirement', 'asd', 4, '2025-05-23 11:55:57', '2025-05-23 11:55:57');

/*!40000 ALTER TABLE `nfr` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table oauth_access_tokens
# ------------------------------------------------------------

DROP TABLE IF EXISTS `oauth_access_tokens`;

CREATE TABLE `oauth_access_tokens` (
  `id` varchar(100) NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `client_id` bigint unsigned NOT NULL,
  `name` varchar(191) DEFAULT NULL,
  `scopes` text,
  `revoked` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `oauth_access_tokens` WRITE;
/*!40000 ALTER TABLE `oauth_access_tokens` DISABLE KEYS */;

INSERT INTO `oauth_access_tokens` (`id`, `user_id`, `client_id`, `name`, `scopes`, `revoked`, `created_at`, `updated_at`, `expires_at`) VALUES
	('23cb5161798ef472a08eeb431170be92ed7302d61e96f3223ed14a2436b04eef4b00c1a6a14de3aa', 29, 3, 'SAgileApp', '[]', 0, '2025-06-24 10:00:15', '2025-06-24 10:00:15', '2026-06-24 18:00:15'),
	('315f897d8143acd37f987ad1949460a47e08eaccf03d02754623b3b3a583567d4c955d26d224df18', 29, 3, 'SAgileApp', '[]', 0, '2025-06-24 10:02:33', '2025-06-24 10:02:33', '2026-06-24 18:02:33'),
	('6fd4d3d47128624db08c45e8ad021570d1104772f530b45dc207c0cc518ad28d4541157b016bf5e2', 29, 3, 'SAgileApp', '[]', 0, '2025-06-24 10:01:20', '2025-06-24 10:01:20', '2026-06-24 18:01:20');

/*!40000 ALTER TABLE `oauth_access_tokens` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table oauth_auth_codes
# ------------------------------------------------------------

DROP TABLE IF EXISTS `oauth_auth_codes`;

CREATE TABLE `oauth_auth_codes` (
  `id` varchar(100) NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `client_id` bigint unsigned NOT NULL,
  `scopes` text,
  `revoked` tinyint NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table oauth_clients
# ------------------------------------------------------------

DROP TABLE IF EXISTS `oauth_clients`;

CREATE TABLE `oauth_clients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `name` varchar(191) NOT NULL,
  `secret` varchar(100) DEFAULT NULL,
  `provider` varchar(191) DEFAULT NULL,
  `redirect` text NOT NULL,
  `personal_access_client` tinyint NOT NULL,
  `password_client` tinyint NOT NULL,
  `revoked` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `oauth_clients` WRITE;
/*!40000 ALTER TABLE `oauth_clients` DISABLE KEYS */;

INSERT INTO `oauth_clients` (`id`, `user_id`, `name`, `secret`, `provider`, `redirect`, `personal_access_client`, `password_client`, `revoked`, `created_at`, `updated_at`) VALUES
	(1, NULL, 'Laravel Personal Access Client', 'dc6RvYLq5DP0FmjIhc2CboVkn97paZ9jF6uDjW6C', NULL, 'http://localhost', 1, 0, 0, '2025-06-24 09:22:19', '2025-06-24 09:22:19'),
	(2, NULL, 'Laravel Password Grant Client', 'toAJ7uTHOthD4z4QvAqb8locJS3xSKTh61LGTvHi', 'users', 'http://localhost', 0, 1, 0, '2025-06-24 09:22:19', '2025-06-24 09:22:19'),
	(3, NULL, 'Laravel Personal Access Client', 'TgDuSlXBFW3W3l4XYX682j6I6IVMjIfbqXHzkngW', NULL, 'http://localhost', 1, 0, 0, '2025-06-24 09:55:56', '2025-06-24 09:55:56'),
	(4, NULL, 'Laravel Password Grant Client', 'Gs0blk7mLq8xbpI7s9ofyF6WkvZRXdMI4mupyjHV', 'users', 'http://localhost', 0, 1, 0, '2025-06-24 09:55:56', '2025-06-24 09:55:56');

/*!40000 ALTER TABLE `oauth_clients` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table oauth_personal_access_clients
# ------------------------------------------------------------

DROP TABLE IF EXISTS `oauth_personal_access_clients`;

CREATE TABLE `oauth_personal_access_clients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `oauth_personal_access_clients` WRITE;
/*!40000 ALTER TABLE `oauth_personal_access_clients` DISABLE KEYS */;

INSERT INTO `oauth_personal_access_clients` (`id`, `client_id`, `created_at`, `updated_at`) VALUES
	(1, 1, '2025-06-24 09:22:19', '2025-06-24 09:22:19'),
	(2, 3, '2025-06-24 09:55:56', '2025-06-24 09:55:56');

/*!40000 ALTER TABLE `oauth_personal_access_clients` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table oauth_refresh_tokens
# ------------------------------------------------------------

DROP TABLE IF EXISTS `oauth_refresh_tokens`;

CREATE TABLE `oauth_refresh_tokens` (
  `id` varchar(100) NOT NULL,
  `access_token_id` varchar(100) NOT NULL,
  `revoked` tinyint NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table password_resets
# ------------------------------------------------------------

DROP TABLE IF EXISTS `password_resets`;

CREATE TABLE `password_resets` (
  `my_row_id` bigint unsigned NOT NULL AUTO_INCREMENT /*!80023 INVISIBLE */,
  `email` varchar(191) NOT NULL,
  `token` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`my_row_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `password_resets` WRITE;
/*!40000 ALTER TABLE `password_resets` DISABLE KEYS */;

INSERT INTO `password_resets` (`my_row_id`, `email`, `token`, `created_at`) VALUES
	(1, '<EMAIL>', '$2y$10$dDcRddqJJlU4m.0A/0j4Jue8P.twN4P5HREwjvIX7S1vicoEm5FGG', '2025-05-27 15:37:02');

/*!40000 ALTER TABLE `password_resets` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table performance_features
# ------------------------------------------------------------

DROP TABLE IF EXISTS `performance_features`;

CREATE TABLE `performance_features` (
  `perfeature_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `perfeature_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`perfeature_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `performance_features` WRITE;
/*!40000 ALTER TABLE `performance_features` DISABLE KEYS */;

INSERT INTO `performance_features` (`perfeature_id`, `perfeature_name`, `created_at`, `updated_at`) VALUES
	(1, 'TestAddPerf', '2024-01-04 04:28:35', '2024-01-04 04:28:35');

/*!40000 ALTER TABLE `performance_features` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table permission
# ------------------------------------------------------------

DROP TABLE IF EXISTS `permission`;

CREATE TABLE `permission` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(191) NOT NULL,
  `description` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `permission` WRITE;
/*!40000 ALTER TABLE `permission` DISABLE KEYS */;

INSERT INTO `permission` (`id`, `key`, `description`, `created_at`, `updated_at`) VALUES
	(1, 'view_kanban', 'Allows access to the kanban board', NULL, NULL),
	(2, 'view_burndown', 'Allows viewing of the burndown chart for tracking sprint progress', NULL, NULL),
	(3, 'view_backlog', 'Allows access to the product backlog items', NULL, NULL),
	(4, 'view_userstory', 'Allows viewing of user stories and their details', NULL, NULL),
	(5, 'view_forum', 'Allows access to the project discussion forum', NULL, NULL),
	(6, 'view_bugtracking', 'Allows viewing of reported bugs and tracking their status', NULL, NULL),
	(7, 'view_status', 'Allows viewing the statuses available in the project', NULL, NULL),
	(8, 'view_details', 'Allows viewing of project details', NULL, NULL),
	(9, 'view_roles', 'Allows viewing the list of users', NULL, NULL),
	(10, 'addLane_kanban', 'Allows addition of a lane in kanban board', NULL, NULL),
	(11, 'addTask_kanban', 'Allows addition of a task using kanban board', NULL, NULL),
	(12, 'editLane_kanban', 'Allows the editing of a lane name in kanban board', NULL, NULL),
	(13, 'deleteLane_kanban', 'Allows the deletion of a lane in kanban board', NULL, NULL),
	(14, 'deleteTask_kanban', 'Allows the deletion of a task in kanban board', NULL, NULL),
	(15, 'addComment_kanban', 'Allows the addition of a comment on a task in kanban board', NULL, NULL),
	(16, 'addUserStory_backlog', 'Allows the creation of userstory from backlog', NULL, NULL),
	(17, 'beginSprint_backlog', 'Allows the starting of a sprint from the backlog', NULL, NULL),
	(18, 'addToSprint_backlog', 'Allows the addition of items into the current sprint from the backlog', NULL, NULL),
	(19, 'endSprint_backlog', 'Allows the ending of current sprint from the backlog', NULL, NULL),
	(20, 'add_userstory', 'Allows creation of user story', NULL, NULL),
	(21, 'edit_userstory', 'Allows the editing of user story', NULL, NULL),
	(22, 'delete_userstory', 'Allows the deletion of a user story', NULL, NULL),
	(23, 'editStatus_userstory', 'Allows updating the user story status', NULL, NULL),
	(24, 'add_task', 'Allows creation of new tasks under user story', NULL, NULL),
	(25, 'edit_task', 'Allows editing of a task', NULL, NULL),
	(26, 'delete_task', 'Allows deletion of a task', NULL, NULL),
	(27, 'viewCalendar_task', 'Allows viewing the task calendar', NULL, NULL),
	(28, 'viewComments_task', 'Allows viewing the comments page from task', NULL, NULL),
	(29, 'add_roles', 'Allows creation of a role', NULL, NULL),
	(30, 'edit_roles', 'Allows permission editing of a role', NULL, NULL),
	(31, 'delete_roles', 'Allows deletion of a role', NULL, NULL),
	(32, 'add_status', 'Allows creation of a status', NULL, NULL),
	(33, 'edit_status', 'Allows editing status name', NULL, NULL),
	(34, 'delete_status', 'Allows deletion of a status', NULL, NULL),
	(35, 'edit_details', 'Allows editing of project details', NULL, NULL),
	(36, 'delete_details', 'Allows deletion of project', NULL, NULL),
	(37, 'view_sprintArchive', 'Allows access to the sprint archive', NULL, NULL),
	(38, 'viewKanbanArchive_sprintArchive', 'Allows viewing the kanban archive', NULL, NULL),
	(39, 'viewBurndownArchive_sprintArchive', 'Allows viewing the burndown archive', NULL, NULL),
	(40, 'updateTaskStatus_kanban', 'Allows updating task status by dragging and dropping in the Kanban board', NULL, NULL),
	(41, 'view_task', 'Allows viewing task list from user story', NULL, NULL),
	(42, 'share_details', 'Allows sharing project for public view', NULL, NULL),
	(43, 'editTask_kanban', 'Allows the editing of a task using kanban board', NULL, NULL),
	(44, 'updateUserRole_roles', 'Allows updating user role in the project', NULL, NULL);

/*!40000 ALTER TABLE `permission` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table permission_role
# ------------------------------------------------------------

DROP TABLE IF EXISTS `permission_role`;

CREATE TABLE `permission_role` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `permission_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=487 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `permission_role` WRITE;
/*!40000 ALTER TABLE `permission_role` DISABLE KEYS */;

INSERT INTO `permission_role` (`id`, `permission_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(1, 1, 1, NULL, NULL),
	(2, 2, 1, NULL, NULL),
	(3, 5, 1, NULL, NULL),
	(4, 6, 1, NULL, NULL),
	(5, 1, 2, NULL, NULL),
	(7, 3, 2, NULL, NULL),
	(8, 4, 2, NULL, NULL),
	(10, 6, 2, NULL, NULL),
	(11, 7, 2, NULL, NULL),
	(13, 9, 2, NULL, NULL),
	(14, 2, 2, NULL, NULL),
	(15, 1, 5, NULL, NULL),
	(16, 2, 5, NULL, NULL),
	(31, 30, 2, NULL, NULL),
	(32, 37, 2, NULL, NULL),
	(47, 29, 2, NULL, NULL),
	(48, 31, 2, NULL, NULL),
	(49, 16, 6, NULL, NULL),
	(50, 17, 6, NULL, NULL),
	(51, 3, 6, NULL, NULL),
	(52, 6, 6, NULL, NULL),
	(53, 2, 6, NULL, NULL),
	(54, 12, 6, NULL, NULL),
	(55, 1, 6, NULL, NULL),
	(56, 30, 6, NULL, NULL),
	(57, 9, 6, NULL, NULL),
	(58, 39, 6, NULL, NULL),
	(59, 38, 6, NULL, NULL),
	(60, 27, 6, NULL, NULL),
	(61, 28, 6, NULL, NULL),
	(62, 23, 6, NULL, NULL),
	(63, 4, 6, NULL, NULL),
	(65, 15, 2, NULL, NULL),
	(66, 10, 2, NULL, NULL),
	(67, 11, 2, NULL, NULL),
	(68, 18, 2, NULL, NULL),
	(69, 16, 2, NULL, NULL),
	(70, 17, 2, NULL, NULL),
	(71, 19, 2, NULL, NULL),
	(72, 13, 2, NULL, NULL),
	(73, 14, 2, NULL, NULL),
	(74, 12, 2, NULL, NULL),
	(75, 41, 2, NULL, NULL),
	(76, 20, 2, NULL, NULL),
	(77, 22, 2, NULL, NULL),
	(78, 21, 2, NULL, NULL),
	(79, 23, 2, NULL, NULL),
	(80, 32, 2, NULL, NULL),
	(81, 33, 2, NULL, NULL),
	(82, 34, 2, NULL, NULL),
	(83, 24, 2, NULL, NULL),
	(84, 26, 2, NULL, NULL),
	(85, 25, 2, NULL, NULL),
	(86, 27, 2, NULL, NULL),
	(87, 28, 2, NULL, NULL),
	(88, 39, 2, NULL, NULL),
	(89, 36, 2, NULL, NULL),
	(90, 35, 2, NULL, NULL),
	(91, 42, 2, NULL, NULL),
	(92, 38, 2, NULL, NULL),
	(93, 43, 2, NULL, NULL),
	(94, 40, 2, NULL, NULL),
	(95, 8, 2, NULL, NULL),
	(96, 5, 2, NULL, NULL),
	(109, 2, 14, NULL, NULL),
	(110, 1, 14, NULL, NULL),
	(111, 41, 14, NULL, NULL),
	(112, 4, 14, NULL, NULL),
	(113, 18, 15, NULL, NULL),
	(114, 39, 15, NULL, NULL),
	(115, 38, 15, NULL, NULL),
	(116, 18, 16, NULL, NULL),
	(117, 16, 16, NULL, NULL),
	(118, 18, 17, NULL, NULL),
	(119, 16, 17, NULL, NULL),
	(120, 18, 3, NULL, NULL),
	(121, 16, 3, NULL, NULL),
	(122, 9, 18, NULL, NULL),
	(123, 16, 18, NULL, NULL),
	(124, 9, 21, NULL, NULL),
	(125, 30, 21, NULL, NULL),
	(126, 18, 21, NULL, NULL),
	(127, 16, 21, NULL, NULL),
	(128, 17, 21, NULL, NULL),
	(129, 19, 21, NULL, NULL),
	(131, 2, 21, NULL, NULL),
	(132, 36, 21, NULL, NULL),
	(133, 35, 21, NULL, NULL),
	(134, 42, 21, NULL, NULL),
	(135, 8, 21, NULL, NULL),
	(136, 5, 21, NULL, NULL),
	(137, 15, 21, NULL, NULL),
	(138, 10, 21, NULL, NULL),
	(139, 11, 21, NULL, NULL),
	(140, 13, 21, NULL, NULL),
	(141, 14, 21, NULL, NULL),
	(142, 12, 21, NULL, NULL),
	(143, 43, 21, NULL, NULL),
	(144, 40, 21, NULL, NULL),
	(145, 1, 21, NULL, NULL),
	(146, 29, 21, NULL, NULL),
	(147, 31, 21, NULL, NULL),
	(148, 37, 21, NULL, NULL),
	(149, 39, 21, NULL, NULL),
	(150, 38, 21, NULL, NULL),
	(151, 32, 21, NULL, NULL),
	(152, 34, 21, NULL, NULL),
	(153, 33, 21, NULL, NULL),
	(154, 7, 21, NULL, NULL),
	(155, 24, 21, NULL, NULL),
	(156, 26, 21, NULL, NULL),
	(157, 25, 21, NULL, NULL),
	(158, 41, 21, NULL, NULL),
	(159, 27, 21, NULL, NULL),
	(160, 28, 21, NULL, NULL),
	(161, 20, 21, NULL, NULL),
	(162, 22, 21, NULL, NULL),
	(163, 21, 21, NULL, NULL),
	(164, 23, 21, NULL, NULL),
	(165, 4, 21, NULL, NULL),
	(166, 3, 21, NULL, NULL),
	(167, 1, 22, NULL, NULL),
	(168, 2, 22, NULL, NULL),
	(170, 4, 22, NULL, NULL),
	(171, 5, 22, NULL, NULL),
	(172, 6, 22, NULL, NULL),
	(173, 7, 22, NULL, NULL),
	(174, 8, 22, NULL, NULL),
	(175, 9, 22, NULL, NULL),
	(176, 10, 22, NULL, NULL),
	(177, 11, 22, NULL, NULL),
	(178, 12, 22, NULL, NULL),
	(179, 13, 22, NULL, NULL),
	(180, 14, 22, NULL, NULL),
	(181, 15, 22, NULL, NULL),
	(182, 16, 22, NULL, NULL),
	(183, 17, 22, NULL, NULL),
	(184, 18, 22, NULL, NULL),
	(186, 20, 22, NULL, NULL),
	(187, 21, 22, NULL, NULL),
	(188, 22, 22, NULL, NULL),
	(189, 23, 22, NULL, NULL),
	(190, 24, 22, NULL, NULL),
	(191, 25, 22, NULL, NULL),
	(192, 26, 22, NULL, NULL),
	(193, 27, 22, NULL, NULL),
	(194, 28, 22, NULL, NULL),
	(195, 29, 22, NULL, NULL),
	(196, 30, 22, NULL, NULL),
	(197, 31, 22, NULL, NULL),
	(198, 32, 22, NULL, NULL),
	(199, 33, 22, NULL, NULL),
	(200, 34, 22, NULL, NULL),
	(201, 35, 22, NULL, NULL),
	(202, 36, 22, NULL, NULL),
	(203, 37, 22, NULL, NULL),
	(204, 38, 22, NULL, NULL),
	(205, 39, 22, NULL, NULL),
	(206, 40, 22, NULL, NULL),
	(207, 41, 22, NULL, NULL),
	(208, 42, 22, NULL, NULL),
	(209, 43, 22, NULL, NULL),
	(210, 1, 23, NULL, NULL),
	(211, 2, 23, NULL, NULL),
	(212, 3, 23, NULL, NULL),
	(213, 4, 23, NULL, NULL),
	(214, 5, 23, NULL, NULL),
	(215, 6, 23, NULL, NULL),
	(216, 7, 23, NULL, NULL),
	(217, 8, 23, NULL, NULL),
	(218, 9, 23, NULL, NULL),
	(219, 10, 23, NULL, NULL),
	(220, 11, 23, NULL, NULL),
	(221, 12, 23, NULL, NULL),
	(222, 13, 23, NULL, NULL),
	(223, 14, 23, NULL, NULL),
	(224, 15, 23, NULL, NULL),
	(225, 16, 23, NULL, NULL),
	(226, 17, 23, NULL, NULL),
	(227, 18, 23, NULL, NULL),
	(228, 19, 23, NULL, NULL),
	(229, 20, 23, NULL, NULL),
	(230, 21, 23, NULL, NULL),
	(231, 22, 23, NULL, NULL),
	(232, 23, 23, NULL, NULL),
	(233, 24, 23, NULL, NULL),
	(234, 25, 23, NULL, NULL),
	(235, 26, 23, NULL, NULL),
	(236, 27, 23, NULL, NULL),
	(237, 28, 23, NULL, NULL),
	(238, 37, 23, NULL, NULL),
	(239, 38, 23, NULL, NULL),
	(240, 39, 23, NULL, NULL),
	(241, 40, 23, NULL, NULL),
	(242, 41, 23, NULL, NULL),
	(243, 43, 23, NULL, NULL),
	(244, 1, 24, NULL, NULL),
	(245, 2, 24, NULL, NULL),
	(246, 3, 24, NULL, NULL),
	(247, 4, 24, NULL, NULL),
	(248, 5, 24, NULL, NULL),
	(249, 6, 24, NULL, NULL),
	(250, 7, 24, NULL, NULL),
	(251, 8, 24, NULL, NULL),
	(252, 9, 24, NULL, NULL),
	(253, 10, 24, NULL, NULL),
	(254, 11, 24, NULL, NULL),
	(255, 12, 24, NULL, NULL),
	(256, 13, 24, NULL, NULL),
	(257, 14, 24, NULL, NULL),
	(258, 15, 24, NULL, NULL),
	(259, 16, 24, NULL, NULL),
	(260, 17, 24, NULL, NULL),
	(261, 18, 24, NULL, NULL),
	(262, 19, 24, NULL, NULL),
	(263, 20, 24, NULL, NULL),
	(264, 21, 24, NULL, NULL),
	(265, 22, 24, NULL, NULL),
	(266, 23, 24, NULL, NULL),
	(267, 24, 24, NULL, NULL),
	(268, 25, 24, NULL, NULL),
	(269, 26, 24, NULL, NULL),
	(270, 27, 24, NULL, NULL),
	(271, 28, 24, NULL, NULL),
	(272, 29, 24, NULL, NULL),
	(273, 30, 24, NULL, NULL),
	(274, 31, 24, NULL, NULL),
	(275, 32, 24, NULL, NULL),
	(276, 33, 24, NULL, NULL),
	(277, 34, 24, NULL, NULL),
	(278, 35, 24, NULL, NULL),
	(279, 36, 24, NULL, NULL),
	(280, 37, 24, NULL, NULL),
	(281, 38, 24, NULL, NULL),
	(282, 39, 24, NULL, NULL),
	(283, 40, 24, NULL, NULL),
	(284, 41, 24, NULL, NULL),
	(285, 42, 24, NULL, NULL),
	(286, 43, 24, NULL, NULL),
	(287, 1, 25, NULL, NULL),
	(288, 2, 25, NULL, NULL),
	(289, 3, 25, NULL, NULL),
	(290, 4, 25, NULL, NULL),
	(291, 5, 25, NULL, NULL),
	(292, 6, 25, NULL, NULL),
	(293, 7, 25, NULL, NULL),
	(294, 8, 25, NULL, NULL),
	(295, 9, 25, NULL, NULL),
	(296, 10, 25, NULL, NULL),
	(297, 11, 25, NULL, NULL);
INSERT INTO `permission_role` (`id`, `permission_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(298, 12, 25, NULL, NULL),
	(299, 13, 25, NULL, NULL),
	(300, 14, 25, NULL, NULL),
	(301, 15, 25, NULL, NULL),
	(302, 16, 25, NULL, NULL),
	(303, 17, 25, NULL, NULL),
	(304, 18, 25, NULL, NULL),
	(305, 19, 25, NULL, NULL),
	(306, 20, 25, NULL, NULL),
	(307, 21, 25, NULL, NULL),
	(308, 22, 25, NULL, NULL),
	(309, 23, 25, NULL, NULL),
	(310, 24, 25, NULL, NULL),
	(311, 25, 25, NULL, NULL),
	(312, 26, 25, NULL, NULL),
	(313, 27, 25, NULL, NULL),
	(314, 28, 25, NULL, NULL),
	(315, 37, 25, NULL, NULL),
	(316, 38, 25, NULL, NULL),
	(317, 39, 25, NULL, NULL),
	(318, 40, 25, NULL, NULL),
	(319, 41, 25, NULL, NULL),
	(320, 43, 25, NULL, NULL),
	(321, 19, 26, NULL, NULL),
	(322, 3, 26, NULL, NULL),
	(323, 44, 22, NULL, NULL),
	(324, 19, 22, NULL, NULL),
	(325, 3, 22, NULL, NULL),
	(326, 44, 24, NULL, NULL),
	(327, 1, 27, NULL, NULL),
	(328, 2, 27, NULL, NULL),
	(329, 3, 27, NULL, NULL),
	(330, 4, 27, NULL, NULL),
	(331, 5, 27, NULL, NULL),
	(332, 6, 27, NULL, NULL),
	(333, 7, 27, NULL, NULL),
	(334, 8, 27, NULL, NULL),
	(335, 9, 27, NULL, NULL),
	(336, 10, 27, NULL, NULL),
	(337, 11, 27, NULL, NULL),
	(338, 12, 27, NULL, NULL),
	(339, 13, 27, NULL, NULL),
	(340, 14, 27, NULL, NULL),
	(341, 15, 27, NULL, NULL),
	(342, 16, 27, NULL, NULL),
	(345, 19, 27, NULL, NULL),
	(346, 20, 27, NULL, NULL),
	(347, 21, 27, NULL, NULL),
	(348, 22, 27, NULL, NULL),
	(349, 23, 27, NULL, NULL),
	(350, 24, 27, NULL, NULL),
	(351, 25, 27, NULL, NULL),
	(352, 26, 27, NULL, NULL),
	(353, 27, 27, NULL, NULL),
	(354, 28, 27, NULL, NULL),
	(355, 29, 27, NULL, NULL),
	(356, 30, 27, NULL, NULL),
	(357, 31, 27, NULL, NULL),
	(358, 32, 27, NULL, NULL),
	(359, 33, 27, NULL, NULL),
	(360, 34, 27, NULL, NULL),
	(361, 35, 27, NULL, NULL),
	(362, 36, 27, NULL, NULL),
	(363, 37, 27, NULL, NULL),
	(364, 38, 27, NULL, NULL),
	(365, 39, 27, NULL, NULL),
	(366, 40, 27, NULL, NULL),
	(367, 41, 27, NULL, NULL),
	(368, 42, 27, NULL, NULL),
	(369, 43, 27, NULL, NULL),
	(370, 1, 28, NULL, NULL),
	(371, 2, 28, NULL, NULL),
	(372, 3, 28, NULL, NULL),
	(373, 4, 28, NULL, NULL),
	(374, 5, 28, NULL, NULL),
	(375, 6, 28, NULL, NULL),
	(376, 7, 28, NULL, NULL),
	(377, 8, 28, NULL, NULL),
	(378, 9, 28, NULL, NULL),
	(379, 10, 28, NULL, NULL),
	(380, 11, 28, NULL, NULL),
	(381, 12, 28, NULL, NULL),
	(382, 13, 28, NULL, NULL),
	(383, 14, 28, NULL, NULL),
	(384, 15, 28, NULL, NULL),
	(385, 16, 28, NULL, NULL),
	(386, 17, 28, NULL, NULL),
	(387, 18, 28, NULL, NULL),
	(388, 19, 28, NULL, NULL),
	(389, 20, 28, NULL, NULL),
	(390, 21, 28, NULL, NULL),
	(391, 22, 28, NULL, NULL),
	(392, 23, 28, NULL, NULL),
	(393, 24, 28, NULL, NULL),
	(394, 25, 28, NULL, NULL),
	(395, 26, 28, NULL, NULL),
	(396, 27, 28, NULL, NULL),
	(397, 28, 28, NULL, NULL),
	(398, 37, 28, NULL, NULL),
	(399, 38, 28, NULL, NULL),
	(400, 39, 28, NULL, NULL),
	(401, 40, 28, NULL, NULL),
	(402, 41, 28, NULL, NULL),
	(403, 43, 28, NULL, NULL),
	(404, 44, 27, NULL, NULL),
	(405, 17, 27, NULL, NULL),
	(406, 18, 27, NULL, NULL),
	(407, 1, 29, NULL, NULL),
	(408, 2, 29, NULL, NULL),
	(409, 3, 29, NULL, NULL),
	(410, 4, 29, NULL, NULL),
	(411, 5, 29, NULL, NULL),
	(412, 6, 29, NULL, NULL),
	(413, 7, 29, NULL, NULL),
	(414, 8, 29, NULL, NULL),
	(415, 9, 29, NULL, NULL),
	(416, 10, 29, NULL, NULL),
	(417, 11, 29, NULL, NULL),
	(418, 12, 29, NULL, NULL),
	(419, 13, 29, NULL, NULL),
	(420, 14, 29, NULL, NULL),
	(421, 15, 29, NULL, NULL),
	(422, 16, 29, NULL, NULL),
	(423, 17, 29, NULL, NULL),
	(424, 18, 29, NULL, NULL),
	(425, 19, 29, NULL, NULL),
	(426, 20, 29, NULL, NULL),
	(427, 21, 29, NULL, NULL),
	(428, 22, 29, NULL, NULL),
	(429, 23, 29, NULL, NULL),
	(430, 24, 29, NULL, NULL),
	(431, 25, 29, NULL, NULL),
	(432, 26, 29, NULL, NULL),
	(433, 27, 29, NULL, NULL),
	(434, 28, 29, NULL, NULL),
	(435, 29, 29, NULL, NULL),
	(436, 30, 29, NULL, NULL),
	(437, 31, 29, NULL, NULL),
	(438, 32, 29, NULL, NULL),
	(439, 33, 29, NULL, NULL),
	(440, 34, 29, NULL, NULL),
	(441, 35, 29, NULL, NULL),
	(442, 36, 29, NULL, NULL),
	(443, 37, 29, NULL, NULL),
	(444, 38, 29, NULL, NULL),
	(445, 39, 29, NULL, NULL),
	(447, 41, 29, NULL, NULL),
	(448, 42, 29, NULL, NULL),
	(449, 43, 29, NULL, NULL),
	(450, 1, 30, NULL, NULL),
	(451, 2, 30, NULL, NULL),
	(452, 3, 30, NULL, NULL),
	(453, 4, 30, NULL, NULL),
	(454, 5, 30, NULL, NULL),
	(455, 6, 30, NULL, NULL),
	(456, 7, 30, NULL, NULL),
	(457, 8, 30, NULL, NULL),
	(458, 9, 30, NULL, NULL),
	(459, 10, 30, NULL, NULL),
	(460, 11, 30, NULL, NULL),
	(461, 12, 30, NULL, NULL),
	(462, 13, 30, NULL, NULL),
	(463, 14, 30, NULL, NULL),
	(464, 15, 30, NULL, NULL),
	(465, 16, 30, NULL, NULL),
	(466, 17, 30, NULL, NULL),
	(467, 18, 30, NULL, NULL),
	(468, 19, 30, NULL, NULL),
	(469, 20, 30, NULL, NULL),
	(470, 21, 30, NULL, NULL),
	(471, 22, 30, NULL, NULL),
	(472, 23, 30, NULL, NULL),
	(473, 24, 30, NULL, NULL),
	(474, 25, 30, NULL, NULL),
	(475, 26, 30, NULL, NULL),
	(476, 27, 30, NULL, NULL),
	(477, 28, 30, NULL, NULL),
	(478, 37, 30, NULL, NULL),
	(479, 38, 30, NULL, NULL),
	(480, 39, 30, NULL, NULL),
	(481, 40, 30, NULL, NULL),
	(482, 41, 30, NULL, NULL),
	(483, 43, 30, NULL, NULL),
	(484, 44, 29, NULL, NULL),
	(486, 40, 29, NULL, NULL);

/*!40000 ALTER TABLE `permission_role` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table priorities
# ------------------------------------------------------------

DROP TABLE IF EXISTS `priorities`;

CREATE TABLE `priorities` (
  `prio_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `prio_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`prio_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table product_features
# ------------------------------------------------------------

DROP TABLE IF EXISTS `product_features`;

CREATE TABLE `product_features` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `profeature_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table project_user
# ------------------------------------------------------------

DROP TABLE IF EXISTS `project_user`;

CREATE TABLE `project_user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `project_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `attachment_access` tinyint NOT NULL DEFAULT '1',
  `project_access` tinyint NOT NULL DEFAULT '1',
  `sprint_access` tinyint NOT NULL DEFAULT '1',
  `forum_access` tinyint NOT NULL DEFAULT '1',
  `userstory_access` tinyint NOT NULL DEFAULT '1',
  `secfeature_access` tinyint NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `project_user` WRITE;
/*!40000 ALTER TABLE `project_user` DISABLE KEYS */;

INSERT INTO `project_user` (`id`, `project_id`, `user_id`, `attachment_access`, `project_access`, `sprint_access`, `forum_access`, `userstory_access`, `secfeature_access`, `created_at`, `updated_at`) VALUES
	(1, 2, 4, 1, 1, 1, 1, 1, 1, NULL, NULL),
	(2, 4, 4, 1, 1, 1, 1, 1, 1, NULL, NULL),
	(3, 6, 4, 1, 1, 1, 1, 1, 1, NULL, NULL),
	(5, 8, 4, 1, 1, 1, 1, 1, 1, NULL, NULL),
	(7, 10, 8, 1, 1, 1, 1, 1, 1, '2025-03-20 12:47:26', '2025-03-20 12:47:26'),
	(8, 11, 7, 1, 1, 1, 1, 1, 1, '2025-03-25 14:04:15', '2025-03-25 14:04:15'),
	(10, 13, 4, 1, 1, 1, 1, 1, 1, '2025-05-22 15:29:11', '2025-05-22 15:29:11'),
	(22, 25, 21, 1, 1, 1, 1, 1, 1, '2025-05-28 14:28:37', '2025-05-28 14:28:37'),
	(23, 26, 4, 1, 1, 1, 1, 1, 1, '2025-06-06 07:42:25', '2025-06-06 07:42:25'),
	(24, 27, 25, 1, 1, 1, 1, 1, 1, '2025-06-08 07:23:57', '2025-06-08 07:23:57'),
	(25, 28, 25, 1, 1, 1, 1, 1, 1, '2025-06-09 14:27:49', '2025-06-09 14:27:49'),
	(26, 29, 27, 1, 1, 1, 1, 1, 1, '2025-06-11 15:33:28', '2025-06-11 15:33:28'),
	(27, 30, 27, 1, 1, 1, 1, 1, 1, '2025-06-11 15:33:59', '2025-06-11 15:33:59'),
	(28, 31, 27, 1, 1, 1, 1, 1, 1, '2025-06-12 10:00:41', '2025-06-12 10:00:41'),
	(29, 32, 4, 1, 1, 1, 1, 1, 1, '2025-06-12 10:06:42', '2025-06-12 10:06:42'),
	(30, 33, 24, 1, 1, 1, 1, 1, 1, '2025-06-12 10:07:18', '2025-06-12 10:07:18'),
	(31, 34, 4, 1, 1, 1, 1, 1, 1, '2025-06-16 04:20:54', '2025-06-16 04:20:54'),
	(32, 35, 29, 1, 1, 1, 1, 1, 1, '2025-06-18 07:15:59', '2025-06-18 07:15:59'),
	(33, 36, 29, 1, 1, 1, 1, 1, 1, '2025-06-19 08:05:21', '2025-06-19 08:05:21'),
	(34, 37, 29, 1, 1, 1, 1, 1, 1, '2025-06-21 14:29:38', '2025-06-21 14:29:38'),
	(35, 38, 29, 1, 1, 1, 1, 1, 1, '2025-06-23 03:50:36', '2025-06-23 03:50:36'),
	(36, 39, 29, 1, 1, 1, 1, 1, 1, '2025-06-23 03:52:42', '2025-06-23 03:52:42'),
	(37, 40, 29, 1, 1, 1, 1, 1, 1, '2025-06-24 03:10:49', '2025-06-24 03:10:49'),
	(38, 41, 25, 1, 1, 1, 1, 1, 1, '2025-06-25 10:44:30', '2025-06-25 10:44:30');

/*!40000 ALTER TABLE `project_user` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table projects
# ------------------------------------------------------------

DROP TABLE IF EXISTS `projects`;

CREATE TABLE `projects` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_name` varchar(191) NOT NULL,
  `proj_name` varchar(100) NOT NULL,
  `proj_desc` varchar(500) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `shareable_slug` varchar(191) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `projects_shareable_slug_unique` (`shareable_slug`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `projects` WRITE;
/*!40000 ALTER TABLE `projects` DISABLE KEYS */;

INSERT INTO `projects` (`id`, `team_name`, `proj_name`, `proj_desc`, `start_date`, `end_date`, `shareable_slug`, `created_at`, `updated_at`) VALUES
	(2, 'Testers', 'Lets', 'This is a description test for the size of the description limit', '2023-01-08', '2026-11-06', NULL, NULL, '2025-04-18 09:35:12'),
	(3, 'a', 'Project Demo', 'Project Demo Testing Creation Kanban', '2024-01-03', '2024-08-29', NULL, '2024-01-03 01:30:51', '2024-01-03 01:30:51'),
	(6, 'Testers', 'TestProj', 'test', '2024-01-16', '2026-03-04', NULL, '2024-01-16 11:50:22', '2025-05-27 22:53:47'),
	(8, 'Testers', 'Test1234', 'Jeevan', '2024-06-14', '2026-01-29', NULL, '2024-06-13 12:28:54', '2025-05-27 22:45:55'),
	(10, 'testTeamAdmin', 'testAdminAccess', 'admin Access to other projects', '2025-03-20', '2025-05-29', 'testadminaccess-NterZdex', '2025-03-20 12:47:26', '2025-06-15 13:35:04'),
	(11, 'testTeamAdmin', 'testAd', 'testaDADd', '2025-03-25', '2025-08-28', NULL, '2025-03-25 14:04:15', '2025-03-25 14:04:15'),
	(13, 'Testers', 'ProjectFlow', 'Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from begin', '2025-05-22', '2026-06-25', 'projectflow-MEbYmBJl', '2025-05-22 15:29:11', '2025-06-18 15:45:48'),
	(26, 'Testers', 'UATtest', 'This is a test for a UAT scope', '2025-06-06', '2026-09-01', NULL, '2025-06-06 07:42:25', '2025-06-06 07:42:25'),
	(28, 'uatTestTeam1', 'UATProjectTest', 'This is a project to perform user acceptance testing on SAgile\'s modules and their features.', '2025-06-09', '2026-07-09', 'uatprojecttest-yawT1EIZ', '2025-06-09 14:27:49', '2025-06-15 14:14:00'),
	(29, 'TestTeam', 'ASDF', 'ASDF', '2025-06-12', '2026-05-26', NULL, '2025-06-11 15:33:28', '2025-06-11 15:33:28'),
	(31, 'new_team', 'TAUFIQ-PROJECT', 'asdf', '2025-06-12', '2025-06-20', NULL, '2025-06-12 10:00:41', '2025-06-12 10:00:41'),
	(33, 'new taufiq team', 'TAUFIQ123', 'TESTING SAGILE DIAG EDITOR', '2025-06-12', '2025-06-19', NULL, '2025-06-12 10:07:18', '2025-06-12 10:07:18'),
	(34, 'Testers', 'VoltView', 'VoltView is a comprehensive project management system designed to help teams plan, organise, and track projects efficiently.', '2025-06-16', '2026-03-26', 'voltview-Dy2cN0Rz', '2025-06-16 04:20:54', '2025-06-16 04:21:02'),
	(35, 'TestMails', 'TestProjInviAccess', 'adsadadasd', '2025-06-18', '2025-12-04', NULL, '2025-06-18 07:15:59', '2025-06-18 07:15:59'),
	(37, 'Team2Delete', 'teatat', 'adasdd', '2025-06-21', '2025-10-10', NULL, '2025-06-21 14:29:38', '2025-06-21 14:29:38'),
	(38, 'Team2Delete', 'TestRoleProjectCreation', 'Role Project Creation, proper teammapping', '2025-06-23', '2025-12-02', NULL, '2025-06-23 03:50:32', '2025-06-23 03:50:32'),
	(39, 'Team2Delete', 'TestCreationLogic', 'adasdasdasd', '2025-06-23', '2026-03-12', NULL, '2025-06-23 03:52:38', '2025-06-23 03:52:38'),
	(40, 'TestNewProcess', 'TestNewLogic', 'test project assignments', '2025-06-24', '2025-10-30', 'testnewlogic-iFSlL9N1', '2025-06-24 03:10:39', '2025-06-24 10:04:49'),
	(41, 'uatTestTeam1', 'TestAddNewCount', 'asdasdadasdasd', '2025-06-25', '2025-11-28', 'testaddnewcount-9SArCtQz', '2025-06-25 10:44:25', '2025-06-26 03:11:53');

/*!40000 ALTER TABLE `projects` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table replies
# ------------------------------------------------------------

DROP TABLE IF EXISTS `replies`;

CREATE TABLE `replies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `comment_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table roles
# ------------------------------------------------------------

DROP TABLE IF EXISTS `roles`;

CREATE TABLE `roles` (
  `role_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `role_name` varchar(191) NOT NULL,
  `team_id` bigint unsigned DEFAULT NULL,
  `project_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;

INSERT INTO `roles` (`role_id`, `role_name`, `team_id`, `project_id`, `created_at`, `updated_at`) VALUES
	(1, 'Developer', NULL, NULL, '2023-09-08 04:06:00', '2025-04-24 14:45:32'),
	(2, 'Project Manager', NULL, NULL, NULL, NULL),
	(3, 'Developer', NULL, 13, '2023-09-08 04:06:00', '2025-04-24 14:45:32'),
	(18, 'Project Manager', NULL, 13, '2025-06-21 11:21:06', '2025-06-21 11:21:06'),
	(20, 'Developer', NULL, 36, NULL, NULL),
	(21, 'Developers', NULL, 37, NULL, NULL),
	(22, 'Project Manager', NULL, 38, '2025-06-23 03:50:33', '2025-06-23 03:50:33'),
	(23, 'Developer', NULL, 38, '2025-06-23 03:50:33', '2025-06-23 03:50:33'),
	(24, 'Project Manager', NULL, 39, '2025-06-23 03:52:38', '2025-06-23 03:52:38'),
	(25, 'Developer', NULL, 39, '2025-06-23 03:52:38', '2025-06-23 03:52:38'),
	(26, 'Yahoo', NULL, 39, '2025-06-23 05:28:26', '2025-06-23 05:28:26'),
	(27, 'Project Manager', NULL, 40, '2025-06-24 03:10:40', '2025-06-24 03:10:40'),
	(28, 'Developer', NULL, 40, '2025-06-24 03:10:40', '2025-06-24 03:10:40'),
	(29, 'Project Manager', NULL, 41, '2025-06-25 10:44:25', '2025-06-25 10:44:25'),
	(30, 'Developer', NULL, 41, '2025-06-25 10:44:25', '2025-06-25 10:44:25');

/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table security_feature_user_story
# ------------------------------------------------------------

DROP TABLE IF EXISTS `security_feature_user_story`;

CREATE TABLE `security_feature_user_story` (
  `my_row_id` bigint unsigned NOT NULL AUTO_INCREMENT /*!80023 INVISIBLE */,
  `secfeature_id` bigint unsigned NOT NULL,
  `user_story_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`my_row_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table security_features
# ------------------------------------------------------------

DROP TABLE IF EXISTS `security_features`;

CREATE TABLE `security_features` (
  `secfeature_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `secfeature_name` varchar(191) NOT NULL,
  `secfeature_desc` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`secfeature_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `security_features` WRITE;
/*!40000 ALTER TABLE `security_features` DISABLE KEYS */;

INSERT INTO `security_features` (`secfeature_id`, `secfeature_name`, `secfeature_desc`, `created_at`, `updated_at`) VALUES
	(1, 'TestAddSec', 'Security Description Test', '2024-01-04 04:28:57', '2024-01-04 04:28:57');

/*!40000 ALTER TABLE `security_features` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table sprint
# ------------------------------------------------------------

DROP TABLE IF EXISTS `sprint`;

CREATE TABLE `sprint` (
  `sprint_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sprint_name` varchar(100) NOT NULL,
  `sprint_desc` varchar(300) NOT NULL,
  `start_sprint` date NOT NULL,
  `end_sprint` date NOT NULL,
  `active_sprint` tinyint DEFAULT NULL,
  `proj_name` varchar(191) NOT NULL,
  `users_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`sprint_id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `sprint` WRITE;
/*!40000 ALTER TABLE `sprint` DISABLE KEYS */;

INSERT INTO `sprint` (`sprint_id`, `sprint_name`, `sprint_desc`, `start_sprint`, `end_sprint`, `active_sprint`, `proj_name`, `users_name`, `created_at`, `updated_at`) VALUES
	(1, 'Sprint 1', 'Sprint to create manage sprint progress', '2023-12-03', '2023-12-09', NULL, 'Lets', 'ammar-', '2023-12-12 16:17:20', '2025-03-25 18:09:23'),
	(2, 'Sprint 2', 'SprintDesc2', '2023-05-08', '2023-12-20', NULL, 'Lets', 'ammar-', '2023-12-20 01:54:27', '2024-01-03 01:31:33'),
	(3, 'asdasd', 'Sprint to create manage sprint progress', '2023-12-03', '2023-12-17', NULL, 'Lets', '4', '2023-12-24 14:56:01', '2025-03-20 14:51:40'),
	(4, 'Test Sprint 3', 'Test make username include', '2023-12-03', '2023-12-26', NULL, 'Lets', 'Ammar', '2023-12-24 14:58:43', '2025-03-20 14:51:44'),
	(5, 'Test Sprint 4', 'Test make username not name', '2023-12-01', '2023-12-22', NULL, 'Lets', 'ammar-', '2023-12-24 14:59:50', '2025-03-20 14:52:12'),
	(6, 'Test for validation editing', 'Test for current date before sprint date', '2024-01-03', '2024-01-23', NULL, 'Lets', 'ammar-', '2024-01-02 09:01:30', '2025-03-20 14:52:14'),
	(7, 'Tasuhrfhqeryhqr', 'qW234HG', '2023-02-07', '2023-11-01', NULL, 'Lets', 'ammar-', '2024-01-08 14:54:25', '2025-03-20 14:52:16'),
	(8, 'test', 'Sprint to create manage sprint progress', '2024-01-09', '2024-01-31', NULL, 'Lets', 'ammar-', '2024-01-09 05:29:53', '2025-03-20 14:52:18'),
	(11, 'ddqwdqwdqwedqw', 'qdqdqw', '2024-01-16', '2024-02-13', NULL, 'TestProj', 'ammar-', '2024-01-16 11:50:41', '2025-03-20 14:51:56'),
	(13, 'final presentation', 'good', '2024-01-14', '2024-01-28', NULL, 'Lets', 'ammar-', '2024-01-17 00:42:57', '2025-03-20 14:52:21'),
	(14, 'wqddqqdqw', 'cwdfqwfdw', '2024-01-22', '2024-02-05', NULL, 'Lets', 'ammar-', '2024-01-30 06:52:21', '2025-03-25 18:10:03'),
	(15, 'New Burndown Test', 'Test New Burndown Implementation', '2025-03-23', '2025-04-13', 0, 'Lets', 'ammar-', '2025-03-26 23:56:04', '2025-03-26 23:57:59'),
	(16, 'UI Update Test', 'Testing new UI Burndown & Kanban Implementation', '2025-04-14', '2025-04-29', 2, 'Lets', 'ammar-', '2025-04-17 23:40:48', '2025-05-27 23:26:55'),
	(20, 'TestSprintFlow', 'new Sagile sprint flow test', '2025-05-22', '2025-06-05', 2, 'ProjectFlow', 'ammar-', '2025-05-22 15:31:35', '2025-05-27 23:17:14'),
	(21, 'Test Sprint Flow', 'TestNewSprint', '2025-05-27', '2025-06-10', 2, 'Test1234', 'ammar-', '2025-05-27 22:46:12', '2025-05-27 23:26:13'),
	(22, 'newSprintTest', 'asdasdsadas', '2025-05-27', '2025-06-10', 2, 'TestProj', 'ammar-', '2025-05-27 22:54:27', '2025-05-27 23:12:07'),
	(23, 'newSprint Testing', 'asdasd', '2025-05-28', '2025-06-11', 2, 'ProjectFlow', 'ammar-', '2025-05-28 10:51:18', '2025-05-28 10:55:18'),
	(24, 'testSprintNewas', 'asdasdasd', '2025-05-28', '2025-06-11', 2, 'ProjectFlow', 'ammar-', '2025-05-28 10:55:33', '2025-05-28 11:14:30'),
	(25, 'TestArchive', 'TestCompleteSprintArchive', '2025-05-28', '2025-06-11', 2, 'ProjectFlow', 'ammar-', '2025-05-28 11:31:36', '2025-06-18 10:40:26'),
	(26, 'TestSprintDEmo', 'TestSprintDEmo', '2025-05-30', '2025-06-13', 2, 'Test1234', 'ammar-', '2025-05-30 04:07:25', '2025-05-30 04:07:51'),
	(28, 'TestSprint123', 'asdasd', '2025-06-11', '2025-06-25', 1, 'Test1234', 'ammar-', '2025-06-11 06:28:08', '2025-06-11 06:28:08'),
	(29, 'UAT1_Sprint', 'This is a sprint to test UAT', '2025-06-12', '2025-06-26', 2, 'UATProjectTest', 'UAT_1', '2025-06-12 15:14:45', '2025-06-15 10:34:49'),
	(30, 'TestSharingDifference', 'adasdad', '2025-06-15', '2025-06-29', 1, 'UATProjectTest', 'UAT_1', '2025-06-15 11:20:42', '2025-06-15 11:20:42'),
	(31, 'Sprint 1', 'Sprint 1 focuses on building the basic task creation, assignment, status tracking, and progress metrics for VoltView.', '2025-06-12', '2025-07-07', 1, 'VoltView', 'ammar-', '2025-06-16 04:32:19', '2025-06-16 04:32:19'),
	(32, 'Sprint 1', 'Test sprint', '2025-06-18', '2025-07-02', 1, 'ProjectFlow', 'ammar-', '2025-06-18 15:53:34', '2025-06-18 15:53:34'),
	(33, 'NewSprintTestAddNewCount', 'This is a test', '2025-06-25', '2025-07-09', 1, 'TestAddNewCount', 'ammarjmldnout', '2025-06-25 14:26:32', '2025-06-25 14:26:32');

/*!40000 ALTER TABLE `sprint` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table sprint_archives
# ------------------------------------------------------------

DROP TABLE IF EXISTS `sprint_archives`;

CREATE TABLE `sprint_archives` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sprint_id` bigint unsigned NOT NULL,
  `kanban_state` longtext,
  `burndown_data` longtext,
  `archived_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `sprint_archives` WRITE;
/*!40000 ALTER TABLE `sprint_archives` DISABLE KEYS */;

INSERT INTO `sprint_archives` (`id`, `sprint_id`, `kanban_state`, `burndown_data`, `created_at`, `updated_at`) VALUES
	(1, 24, '{\"82\":[],\"83\":[],\"84\":[{\"id\":77,\"title\":\"NewTaskForDB2\",\"description\":\"test task\",\"order\":1,\"status_id\":\"84\",\"userstory_id\":24,\"sprint_id\":24,\"proj_id\":13,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-05-22T07:31:18.000000Z\",\"updated_at\":\"2025-05-28T02:55:33.000000Z\",\"user_names\":\"[\\\"ammar-\\\"]\",\"newTask_update\":\"2025-05-22\"}],\"85\":[]}', '{\"idealData\":[1,0.9333333333333333,0.8666666666666667,0.8,0.7333333333333334,0.6666666666666667,0.6,0.5333333333333333,0.4666666666666667,0.4,0.33333333333333337,0.2666666666666667,0.19999999999999996,0.1333333333333333,0.06666666666666665,0],\"actualData\":[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-05-28 11:14:30', '2025-05-28 11:14:30'),
	(2, 26, '{\"53\":[{\"id\":79,\"title\":\"TaskTesting123-1\",\"description\":\"test task\",\"order\":0,\"status_id\":\"53\",\"userstory_id\":26,\"sprint_id\":26,\"proj_id\":8,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-05-27T22:40:01.000000Z\",\"updated_at\":\"2025-05-30T04:07:25.000000Z\",\"user_names\":\"[\\\"ammar-\\\"]\",\"newTask_update\":\"2025-05-27\"},{\"id\":80,\"title\":\"TaskTesting123-2\",\"description\":\"TaskTesting123-2\",\"order\":0,\"status_id\":\"53\",\"userstory_id\":26,\"sprint_id\":26,\"proj_id\":8,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-05-27T22:40:13.000000Z\",\"updated_at\":\"2025-05-30T04:07:36.000000Z\",\"user_names\":\"[\\\"ammar-\\\"]\",\"newTask_update\":\"2025-05-27\"}],\"54\":[],\"55\":[],\"56\":[]}', '{\"idealData\":[2,1.8666666666666667,1.7333333333333334,1.6,1.4666666666666668,1.3333333333333335,1.2,1.0666666666666667,0.9333333333333333,0.8,0.6666666666666667,0.5333333333333334,0.3999999999999999,0.2666666666666666,0.1333333333333333,0],\"actualData\":[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2]}', '2025-05-30 04:07:50', '2025-05-30 04:07:50'),
	(3, 29, '{\"142\":[],\"143\":[],\"144\":[{\"id\":85,\"title\":\"UAT Task2 Update\",\"description\":\"This is an update to UAT Task2\",\"order\":1,\"status_id\":\"144\",\"userstory_id\":31,\"sprint_id\":29,\"proj_id\":28,\"start_date\":\"2025-06-14\",\"end_date\":\"2025-06-21\",\"completion_date\":null,\"created_at\":\"2025-06-12T15:21:57.000000Z\",\"updated_at\":\"2025-06-13T01:56:18.000000Z\",\"user_names\":\"[\\\"UAT_1\\\"]\",\"newTask_update\":\"2025-06-13\"}],\"145\":[{\"id\":83,\"title\":\"Task_UAT1\",\"description\":\"This is a task to test the UAT for SAgile\'s task update\",\"order\":1,\"status_id\":\"145\",\"userstory_id\":31,\"sprint_id\":29,\"proj_id\":28,\"start_date\":null,\"end_date\":null,\"completion_date\":\"2025-06-15\",\"created_at\":\"2025-06-10T04:59:59.000000Z\",\"updated_at\":\"2025-06-15T09:32:16.000000Z\",\"user_names\":\"[\\\"UAT_1\\\"]\",\"newTask_update\":\"2025-06-12\"}]}', '{\"idealData\":[2,1.8666666666666667,1.7333333333333334,1.6,1.4666666666666668,1.3333333333333335,1.2,1.0666666666666667,0.9333333333333333,0.8,0.6666666666666667,0.5333333333333334,0.3999999999999999,0.2666666666666666,0.1333333333333333,0],\"actualData\":[2,2,2,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-06-15 10:34:49', '2025-06-15 10:34:49'),
	(4, 25, '{\"82\":[],\"83\":[],\"84\":[],\"85\":[{\"id\":77,\"title\":\"NewTaskForDB2\",\"description\":\"test task\",\"order\":1,\"status_id\":\"85\",\"userstory_id\":24,\"sprint_id\":25,\"proj_id\":13,\"start_date\":null,\"end_date\":null,\"completion_date\":\"2025-06-18\",\"created_at\":\"2025-05-22T15:31:18.000000Z\",\"updated_at\":\"2025-06-18T10:40:14.000000Z\",\"user_names\":\"[\\\"ammar-\\\"]\",\"newTask_update\":\"2025-05-22\"}]}', '{\"idealData\":[1,0.9333333333333333,0.8666666666666667,0.8,0.7333333333333334,0.6666666666666667,0.6,0.5333333333333333,0.4666666666666667,0.4,0.33333333333333337,0.2666666666666667,0.19999999999999996,0.1333333333333333,0.06666666666666665,0],\"actualData\":[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-06-18 10:40:26', '2025-06-18 10:40:26');

/*!40000 ALTER TABLE `sprint_archives` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table statuses
# ------------------------------------------------------------

DROP TABLE IF EXISTS `statuses`;

CREATE TABLE `statuses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `order` smallint NOT NULL DEFAULT '0',
  `project_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=205 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `statuses` WRITE;
/*!40000 ALTER TABLE `statuses` DISABLE KEYS */;

INSERT INTO `statuses` (`id`, `title`, `slug`, `order`, `project_id`, `created_at`, `updated_at`) VALUES
	(1, 'To-do', 'to-do', 1, 1, NULL, NULL),
	(2, 'backlog', 'backlog', 1, 2, NULL, NULL),
	(4, 'doing', 'doing', 3, 2, NULL, NULL),
	(9, 'test code', 'test code', 4, 2, NULL, NULL),
	(11, 'done', 'done', 5, 2, NULL, NULL),
	(14, 'Backlog', 'backlog', 1, 3, NULL, NULL),
	(15, 'Up Next', 'up-next', 2, 3, NULL, NULL),
	(16, 'In Progress', 'in-progress', 3, 3, NULL, NULL),
	(17, 'Done', 'done', 4, 3, NULL, NULL),
	(18, 'Backlog', 'backlog', 1, 4, NULL, NULL),
	(19, 'Up Next', 'up-next', 2, 4, NULL, NULL),
	(20, 'In Progress', 'in-progress', 3, 4, NULL, NULL),
	(21, 'Done', 'done', 4, 4, NULL, NULL),
	(22, 'Backlog', 'backlog', 1, 5, NULL, NULL),
	(23, 'Up Next', 'up-next', 2, 5, NULL, NULL),
	(24, 'In Progress', 'in-progress', 3, 5, NULL, NULL),
	(25, 'Done', 'done', 4, 5, NULL, NULL),
	(40, 'Backlog', 'backlog', 1, 6, NULL, NULL),
	(41, 'Up Next', 'up-next', 2, 6, NULL, NULL),
	(42, 'In Progress', 'in-progress', 3, 6, NULL, NULL),
	(43, 'Done', 'done', 4, 6, NULL, NULL),
	(45, 'test', 'test', 6, 6, NULL, NULL),
	(46, 'Backlog', 'backlog', 1, 7, NULL, NULL),
	(47, 'Up Next', 'up-next', 2, 7, NULL, NULL),
	(48, 'In Progress', 'in-progress', 3, 7, NULL, NULL),
	(49, 'Done', 'done', 4, 7, NULL, NULL),
	(53, 'Backlog', 'backlog', 1, 8, NULL, NULL),
	(54, 'Up Next', 'up-next', 2, 8, NULL, NULL),
	(55, 'In Progress', 'in-progress', 3, 8, NULL, NULL),
	(56, 'Done', 'done', 4, 8, NULL, NULL),
	(57, 'Backlog', 'backlog', 1, 9, NULL, NULL),
	(58, 'Up Next', 'up-next', 2, 9, NULL, NULL),
	(59, 'In Progress', 'in-progress', 3, 9, NULL, NULL),
	(60, 'Done', 'done', 4, 9, NULL, NULL),
	(61, 'Backlog', 'backlog', 1, 10, NULL, NULL),
	(62, 'Up Next', 'up-next', 2, 10, NULL, NULL),
	(63, 'In Progress', 'in-progress', 3, 10, NULL, NULL),
	(64, 'Done', 'done', 4, 10, NULL, NULL),
	(65, 'Backlog', 'backlog', 1, 11, NULL, NULL),
	(66, 'Up Next', 'up-next', 2, 11, NULL, NULL),
	(67, 'In Progress', 'in-progress', 3, 11, NULL, NULL),
	(68, 'Done', 'done', 4, 11, NULL, NULL),
	(69, 'Backlog', 'backlog', 1, 12, NULL, NULL),
	(70, 'Up Next', 'up-next', 2, 12, NULL, NULL),
	(71, 'In Progress', 'in-progress', 3, 12, NULL, NULL),
	(72, 'Done', 'done', 4, 12, NULL, NULL),
	(82, 'Backlog', 'backlog', 1, 13, NULL, NULL),
	(83, 'Up Next', 'up-next', 2, 13, NULL, NULL),
	(84, 'In Progress', 'in-progress', 3, 13, NULL, NULL),
	(85, 'Done', 'done', 4, 13, NULL, NULL),
	(86, 'Backlog', 'backlog', 1, 14, NULL, NULL),
	(87, 'Up Next', 'up-next', 2, 14, NULL, NULL),
	(88, 'In Progress', 'in-progress', 3, 14, NULL, NULL),
	(89, 'Done', 'done', 4, 14, NULL, NULL),
	(90, 'Backlog', 'backlog', 1, 15, NULL, NULL),
	(91, 'Up Next', 'up-next', 2, 15, NULL, NULL),
	(92, 'In Progress', 'in-progress', 3, 15, NULL, NULL),
	(93, 'Done', 'done', 4, 15, NULL, NULL),
	(94, 'Backlog', 'backlog', 1, 16, NULL, NULL),
	(95, 'Up Next', 'up-next', 2, 16, NULL, NULL),
	(96, 'In Progress', 'in-progress', 3, 16, NULL, NULL),
	(97, 'Done', 'done', 4, 16, NULL, NULL),
	(98, 'Backlog', 'backlog', 1, 17, NULL, NULL),
	(99, 'Up Next', 'up-next', 2, 17, NULL, NULL),
	(100, 'In Progress', 'in-progress', 3, 17, NULL, NULL),
	(101, 'Done', 'done', 4, 17, NULL, NULL),
	(102, 'Backlog', 'backlog', 1, 18, NULL, NULL),
	(103, 'Up Next', 'up-next', 2, 18, NULL, NULL),
	(104, 'In Progress', 'in-progress', 3, 18, NULL, NULL),
	(105, 'Done', 'done', 4, 18, NULL, NULL),
	(106, 'Backlog', 'backlog', 1, 19, NULL, NULL),
	(107, 'Up Next', 'up-next', 2, 19, NULL, NULL),
	(108, 'In Progress', 'in-progress', 3, 19, NULL, NULL),
	(109, 'Done', 'done', 4, 19, NULL, NULL),
	(110, 'Backlog', 'backlog', 1, 20, NULL, NULL),
	(111, 'Up Next', 'up-next', 2, 20, NULL, NULL),
	(112, 'In Progress', 'in-progress', 3, 20, NULL, NULL),
	(113, 'Done', 'done', 4, 20, NULL, NULL),
	(114, 'Backlog', 'backlog', 1, 21, NULL, NULL),
	(115, 'Up Next', 'up-next', 2, 21, NULL, NULL),
	(116, 'In Progress', 'in-progress', 3, 21, NULL, NULL),
	(117, 'Done', 'done', 4, 21, NULL, NULL),
	(118, 'Backlog', 'backlog', 1, 22, NULL, NULL),
	(119, 'Up Next', 'up-next', 2, 22, NULL, NULL),
	(120, 'In Progress', 'in-progress', 3, 22, NULL, NULL),
	(121, 'Done', 'done', 4, 22, NULL, NULL),
	(122, 'Backlog', 'backlog', 1, 23, NULL, NULL),
	(123, 'Up Next', 'up-next', 2, 23, NULL, NULL),
	(124, 'In Progress', 'in-progress', 3, 23, NULL, NULL),
	(125, 'Done', 'done', 4, 23, NULL, NULL),
	(126, 'Backlog', 'backlog', 1, 24, NULL, NULL),
	(127, 'Up Next', 'up-next', 2, 24, NULL, NULL),
	(128, 'In Progress', 'in-progress', 3, 24, NULL, NULL),
	(129, 'Done', 'done', 4, 24, NULL, NULL),
	(130, 'Backlog', 'backlog', 1, 25, NULL, NULL),
	(131, 'Up Next', 'up-next', 2, 25, NULL, NULL),
	(132, 'In Progress', 'in-progress', 3, 25, NULL, NULL),
	(133, 'Done', 'done', 4, 25, NULL, NULL),
	(134, 'Backlog', 'backlog', 1, 26, NULL, NULL),
	(135, 'Up Next', 'up-next', 2, 26, NULL, NULL),
	(136, 'In Progress', 'in-progress', 3, 26, NULL, NULL),
	(137, 'Done', 'done', 4, 26, NULL, NULL),
	(138, 'Backlog', 'backlog', 1, 27, NULL, NULL),
	(139, 'Up Next', 'up-next', 3, 27, NULL, NULL),
	(140, 'In Progress', 'in-progress', 2, 27, NULL, NULL),
	(141, 'Done', 'done', 4, 27, NULL, NULL),
	(142, 'Backlog', 'backlog', 1, 28, NULL, NULL),
	(143, 'Up Next', 'up-next', 2, 28, NULL, NULL),
	(144, 'In Progress', 'in-progress', 3, 28, NULL, NULL),
	(145, 'Done', 'done', 4, 28, NULL, NULL),
	(146, 'Backlog', 'backlog', 1, 29, NULL, NULL),
	(147, 'Up Next', 'up-next', 2, 29, NULL, NULL),
	(148, 'In Progress', 'in-progress', 3, 29, NULL, NULL),
	(149, 'Done', 'done', 4, 29, NULL, NULL),
	(150, 'Backlog', 'backlog', 1, 30, NULL, NULL),
	(151, 'Up Next', 'up-next', 2, 30, NULL, NULL),
	(152, 'In Progress', 'in-progress', 3, 30, NULL, NULL),
	(153, 'Done', 'done', 4, 30, NULL, NULL),
	(154, 'Backlog', 'backlog', 1, 31, NULL, NULL),
	(155, 'Up Next', 'up-next', 2, 31, NULL, NULL),
	(156, 'In Progress', 'in-progress', 3, 31, NULL, NULL),
	(157, 'Done', 'done', 4, 31, NULL, NULL),
	(158, 'Backlog', 'backlog', 1, 32, NULL, NULL),
	(159, 'Up Next', 'up-next', 2, 32, NULL, NULL),
	(160, 'In Progress', 'in-progress', 3, 32, NULL, NULL),
	(161, 'Done', 'done', 4, 32, NULL, NULL),
	(162, 'Backlog', 'backlog', 1, 33, NULL, NULL),
	(163, 'Up Next', 'up-next', 2, 33, NULL, NULL),
	(164, 'In Progress', 'in-progress', 3, 33, NULL, NULL),
	(165, 'Done', 'done', 4, 33, NULL, NULL),
	(170, 'Backlog', 'backlog', 1, 34, NULL, NULL),
	(171, 'Up Next', 'up-next', 2, 34, NULL, NULL),
	(172, 'In Progress', 'in-progress', 3, 34, NULL, NULL),
	(173, 'Done', 'done', 4, 34, NULL, NULL),
	(174, 'Backlog', 'backlog', 1, 35, NULL, NULL),
	(175, 'Up Next', 'up-next', 2, 35, NULL, NULL),
	(176, 'In Progress', 'in-progress', 3, 35, NULL, NULL),
	(177, 'Done', 'done', 4, 35, NULL, NULL),
	(178, 'Backlog', 'backlog', 1, 36, NULL, NULL),
	(179, 'Up Next', 'up-next', 2, 36, NULL, NULL),
	(180, 'In Progress', 'in-progress', 3, 36, NULL, NULL),
	(181, 'Done', 'done', 4, 36, NULL, NULL),
	(185, 'Backlog', 'backlog', 1, 37, NULL, NULL),
	(186, 'Up Next', 'up-next', 2, 37, NULL, NULL),
	(187, 'In Progress', 'in-progress', 3, 37, NULL, NULL),
	(188, 'Done', 'done', 4, 37, NULL, NULL),
	(189, 'Backlog', 'backlog', 1, 38, NULL, NULL),
	(190, 'Up Next', 'up-next', 2, 38, NULL, NULL),
	(191, 'In Progress', 'in-progress', 3, 38, NULL, NULL),
	(192, 'Done', 'done', 4, 38, NULL, NULL),
	(193, 'Backlog', 'backlog', 1, 39, NULL, NULL),
	(194, 'Up Next', 'up-next', 2, 39, NULL, NULL),
	(195, 'In Progress', 'in-progress', 3, 39, NULL, NULL),
	(196, 'Done', 'done', 4, 39, NULL, NULL),
	(197, 'Backlog', 'backlog', 1, 40, NULL, NULL),
	(198, 'Up Next', 'up-next', 2, 40, NULL, NULL),
	(199, 'In Progress', 'in-progress', 3, 40, NULL, NULL),
	(200, 'Done', 'done', 4, 40, NULL, NULL),
	(201, 'Backlog', 'backlog', 1, 41, NULL, NULL),
	(202, 'Up Next', 'up-next', 2, 41, NULL, NULL),
	(203, 'In Progress', 'in-progress', 3, 41, NULL, NULL),
	(204, 'Done', 'done', 4, 41, NULL, NULL);

/*!40000 ALTER TABLE `statuses` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table taskcomment
# ------------------------------------------------------------

DROP TABLE IF EXISTS `taskcomment`;

CREATE TABLE `taskcomment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` bigint unsigned NOT NULL,
  `comment` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `assigned_to` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `taskcomment` WRITE;
/*!40000 ALTER TABLE `taskcomment` DISABLE KEYS */;

INSERT INTO `taskcomment` (`id`, `task_id`, `comment`, `created_by`, `assigned_to`, `updated_at`, `created_at`) VALUES
	(3, 77, 'test comment filter', 'passUser', '[\"ammar-\"]', '2025-05-22 16:20:01', '2025-05-22 16:20:01'),
	(4, 77, 'newComment', 'ammar-', '[\"ammar-\"]', '2025-05-22 16:20:25', '2025-05-22 16:20:25');

/*!40000 ALTER TABLE `taskcomment` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table tasks
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tasks`;

CREATE TABLE `tasks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `description` text,
  `order` smallint NOT NULL DEFAULT '0',
  `status_id` varchar(191) NOT NULL,
  `userstory_id` int unsigned NOT NULL,
  `sprint_id` int unsigned NOT NULL,
  `proj_id` int unsigned NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `completion_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_names` longtext,
  `newTask_update` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=103 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `tasks` WRITE;
/*!40000 ALTER TABLE `tasks` DISABLE KEYS */;

INSERT INTO `tasks` (`id`, `title`, `description`, `order`, `status_id`, `userstory_id`, `sprint_id`, `proj_id`, `start_date`, `end_date`, `completion_date`, `created_at`, `updated_at`, `user_names`, `newTask_update`) VALUES
	(44, 'adada', 'addsad', 1, '9', 5, 1, 2, '2023-12-04', '2023-12-07', NULL, '2024-01-14 08:34:28', '2025-03-26 23:25:38', '[\"ammar-\"]', '2024-01-30'),
	(46, 'adadal', 'adasd', 2, '9', 5, 1, 2, '2023-12-05', '2023-12-08', NULL, '2024-01-14 08:37:59', '2025-03-26 23:25:38', 'null', NULL),
	(48, 'ccassa', 'dasdas', 1, '42', 10, 0, 6, NULL, NULL, NULL, '2024-01-16 11:55:10', '2025-05-27 23:06:55', '[\"ammar-\"]', NULL),
	(51, 'adadas', 'adasdasdas', 2, '4', 5, 1, 2, '2023-12-03', '2023-12-07', NULL, '2024-01-16 15:45:04', '2025-03-26 23:25:33', '[\"ammar-\"]', NULL),
	(53, 'task 1', '1', 1, '11', 12, 16, 2, '2024-01-15', '2024-01-16', '2025-04-23', '2024-01-17 00:44:41', '2025-04-23 23:53:59', 'null', '2024-01-30'),
	(54, 'task 2', '2', 2, '11', 12, 16, 2, '2024-01-17', '2024-01-18', '2025-04-27', '2024-01-17 00:45:06', '2025-04-27 22:12:18', 'null', '2024-01-30'),
	(55, 'task 3', '3', 3, '11', 12, 16, 2, '2024-01-18', '2024-01-19', '2025-04-23', '2024-01-17 00:45:31', '2025-04-27 22:12:18', 'null', '2024-01-30'),
	(58, 'TaskTest3', 'asdsad', 1, '9', 13, 14, 2, '2024-01-28', '2024-02-02', NULL, '2024-01-30 06:53:59', '2025-03-20 12:27:52', 'null', '2024-02-02'),
	(59, 'TaskTest10', 'eqeeqw', 1, '11', 13, 14, 2, '2024-01-28', '2024-02-02', NULL, '2024-01-30 06:54:13', '2025-03-25 18:10:28', '[\"ammar-\"]', '2024-02-02'),
	(61, 'Task1ForTest', 'Test Task', 4, '11', 17, 16, 2, '2025-03-24', '2025-04-05', '2025-04-23', '2025-03-26 23:57:13', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-03-26'),
	(62, 'TestTask2', 'dasd', 5, '11', 17, 16, 2, '2025-03-24', '2025-03-31', '2025-04-23', '2025-03-26 23:57:37', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-03-26'),
	(63, 'task3', 'aasdasd', 6, '11', 17, 16, 2, '2025-03-26', '2025-03-29', '2025-03-27', '2025-03-26 23:58:25', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-03-26'),
	(64, 'task4', 'weqe', 7, '11', 17, 16, 2, '2025-03-24', '2025-03-29', '2025-03-29', '2025-03-26 23:58:33', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-03-26'),
	(65, 'task5', 'daddd', 8, '11', 17, 16, 2, '2025-03-24', '2025-03-31', '2025-03-27', '2025-03-26 23:58:44', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-03-26'),
	(66, 'newTaskPS', 'This is a description', 9, '11', 17, 16, 2, '2025-03-24', '2025-04-06', '2025-04-17', '2025-04-09 16:26:30', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-04-09'),
	(75, 'newTask creation for Task page', 'task', 1, '4', 2, 0, 2, NULL, NULL, NULL, '2025-04-24 14:32:07', '2025-05-27 23:26:55', '[\"ammar-\"]', '2025-04-24'),
	(76, 'NewTaskForDB', 'new task creation with remade DB', 1, '85', 24, 0, 13, NULL, NULL, '2025-05-27', '2025-05-22 15:31:01', '2025-05-27 23:17:14', '[\"ammar-\"]', '2025-05-22'),
	(77, 'NewTaskForDB2', 'test task', 1, '85', 24, 32, 13, NULL, NULL, '2025-06-19', '2025-05-22 15:31:18', '2025-06-19 03:06:05', '[\"ammar-\"]', '2025-05-22'),
	(78, 'newTaskBacklog', 'description', 0, '82', 25, 32, 13, NULL, NULL, NULL, '2025-05-22 16:24:18', '2025-06-19 03:32:24', '[\"ammar-\"]', '2025-05-22'),
	(79, 'TaskTesting123-1', 'test task', 1, '53', 26, 28, 8, NULL, NULL, NULL, '2025-05-27 22:40:01', '2025-06-11 06:28:26', '[\"ammar-\"]', '2025-05-27'),
	(80, 'TaskTesting123-2', 'TaskTesting123-2', 0, '53', 26, 0, 8, NULL, NULL, NULL, '2025-05-27 22:40:13', '2025-05-30 04:07:51', '[\"ammar-\"]', '2025-05-27'),
	(81, 'TestTASK123123', 'asdsqd', 0, '40', 27, 0, 6, NULL, NULL, NULL, '2025-05-27 22:54:18', '2025-05-27 23:04:18', '[\"ammar-\"]', '2025-05-27'),
	(83, 'Task_UAT1', 'This is a task to test the UAT for SAgile\'s task update', 1, '145', 31, 30, 28, NULL, NULL, '2025-06-16', '2025-06-10 04:59:59', '2025-06-16 09:33:53', '[\"UAT_1\"]', '2025-06-12'),
	(85, 'UAT Task2 Update', 'This is an update to UAT Task2', 1, '143', 31, 30, 28, NULL, NULL, NULL, '2025-06-12 15:21:57', '2025-06-16 09:34:18', '[\"UAT_1\"]', '2025-06-13'),
	(89, 'Design the task creation form', 'Create a user-friendly form with fields for task title, detailed description, assignee, priority, and deadline to capture all essential task details.', 1, '173', 32, 31, 34, NULL, NULL, '2025-06-16', '2025-06-16 04:28:47', '2025-06-16 04:33:00', '[\"testUser2\"]', '2025-06-16'),
	(90, 'Implement task assignment logic', 'Develop backend logic that links each task to a selected user account, ensuring tasks are correctly assigned and appear in the user’s task list.', 2, '170', 32, 31, 34, NULL, NULL, NULL, '2025-06-16 04:28:58', '2025-06-19 03:35:17', 'null', '2025-06-16'),
	(91, 'Develop a task list view', 'Build an interface that displays all tasks in a list or table format, with filtering and sorting options by project and assignee.', 0, '170', 32, 0, 34, NULL, NULL, NULL, '2025-06-16 04:29:09', '2025-06-16 04:29:09', 'null', '2025-06-16'),
	(92, 'Test task creation and assignment workflows', 'Perform end-to-end testing to ensure tasks can be created, assigned, edited, and removed without errors.', 0, '170', 32, 0, 34, NULL, NULL, NULL, '2025-06-16 04:29:23', '2025-06-16 04:29:23', 'null', '2025-06-16'),
	(93, 'Create a task status field', 'Add a status field to tasks with predefined values like “To Do”, “In Progress”, and “Done” to reflect the task’s current state.', 2, '173', 33, 31, 34, NULL, NULL, '2025-06-14', '2025-06-16 04:29:51', '2025-06-16 04:33:46', '[\"passUser\",\"TestRandomUser\"]', '2025-06-16'),
	(94, 'Display real-time status updates', 'Make sure that any status change is instantly reflected on the project’s dashboard and task list for accurate progress tracking.', 0, '170', 33, 0, 34, NULL, NULL, NULL, '2025-06-16 04:30:05', '2025-06-16 04:30:05', '[\"passUser\"]', '2025-06-16'),
	(95, 'Develop metrics and data fetching', 'Implement backend queries to calculate total tasks, completed tasks, pending tasks, and upcoming deadlines for each project.', 1, '170', 34, 31, 34, NULL, NULL, NULL, '2025-06-16 04:30:20', '2025-06-19 03:34:36', 'null', '2025-06-16'),
	(96, 'Add filtering and sorting options', 'Provide filtering by sprint, assignee, or time period so managers can focus on specific project segments.', 0, '170', 34, 0, 34, NULL, NULL, NULL, '2025-06-16 04:30:30', '2025-06-16 04:30:30', '[\"testUser2\"]', '2025-06-16'),
	(97, 'newTaskTestingNew', 'asdadadd', 0, '198', 42, 0, 40, NULL, NULL, NULL, '2025-06-24 16:08:36', '2025-06-24 16:12:53', '[\"ammarjmldnout\"]', '2025-06-25'),
	(98, 'Task1Creation', 'test Task 1 creation', 1, '201', 43, 33, 41, '2025-06-26', '2025-07-08', NULL, '2025-06-25 14:24:35', '2025-06-26 02:53:14', 'null', '2025-06-26'),
	(99, 'Task2Creation', 'This is a test', 0, '201', 43, 0, 41, NULL, NULL, NULL, '2025-06-25 14:24:56', '2025-06-25 14:24:56', '[\"ammarjmldnout\"]', '2025-06-25'),
	(100, 'TestTaskCreation', 'This is a test', 1, '204', 44, 33, 41, NULL, NULL, '2025-06-26', '2025-06-25 14:26:05', '2025-06-26 02:35:16', '[\"ammarjmldnout\"]', '2025-06-25'),
	(101, 'Creation Test', 'testing the new addition', 2, '201', 44, 33, 41, '2025-06-26', '2025-07-08', NULL, '2025-06-25 15:59:44', '2025-06-26 02:53:14', 'null', '2025-06-26'),
	(102, 'test0Add', 'adasadasds', 1, '202', 44, 33, 41, '2025-06-27', '2025-07-08', NULL, '2025-06-25 16:23:07', '2025-06-26 02:53:14', '[\"ammarjmldnout\"]', '2025-06-26');

/*!40000 ALTER TABLE `tasks` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table teammappings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `teammappings`;

CREATE TABLE `teammappings` (
  `teammapping_id` int unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(191) NOT NULL,
  `role_name` varchar(191) NOT NULL,
  `team_name` varchar(191) NOT NULL,
  `project_id` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `invitation_status` enum('pending','accepted','declined') NOT NULL DEFAULT 'pending',
  `invitation_token` varchar(191) DEFAULT NULL,
  PRIMARY KEY (`teammapping_id`),
  UNIQUE KEY `teammappings_invitation_token_unique` (`invitation_token`)
) ENGINE=InnoDB AUTO_INCREMENT=91 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `teammappings` WRITE;
/*!40000 ALTER TABLE `teammappings` DISABLE KEYS */;

INSERT INTO `teammappings` (`teammapping_id`, `username`, `role_name`, `team_name`, `project_id`, `created_at`, `updated_at`, `invitation_status`, `invitation_token`) VALUES
	(7, 'ammar-', 'Project Manager', 'Testers', NULL, '2024-01-10 06:25:31', '2025-06-21 09:57:41', 'accepted', NULL),
	(8, 'ammar-', 'Project Manager', 'g4ew[ioj', NULL, '2024-01-18 03:38:12', '2025-06-18 07:31:27', 'accepted', NULL),
	(9, 'testUser2', 'Project Manager', 'testTeamAdmin', NULL, NULL, '2025-06-19 03:08:43', 'accepted', NULL),
	(10, 'passUser', 'Developer', 'Testers', NULL, '2025-05-22 15:28:27', '2025-05-22 15:28:27', 'pending', NULL),
	(11, 'testuser', 'Student', 'A12345_Test User', NULL, '2025-05-26 19:29:53', '2025-05-26 19:29:53', 'pending', NULL),
	(29, 'testUser2', 'Developer', 'Testers', NULL, '2025-05-30 03:52:27', '2025-06-19 03:08:35', 'accepted', NULL),
	(30, 'TestRandomUser', 'Project Manager', 'Testers', NULL, '2025-05-30 13:55:04', '2025-05-30 13:55:04', 'pending', NULL),
	(35, 'UAT_1', 'Team Manager', 'uatTestTeam1', NULL, '2025-06-08 11:00:34', '2025-06-19 16:27:29', 'accepted', NULL),
	(36, 'taufiq', 'Project Manager', 'new_team', NULL, '2025-06-12 10:00:19', '2025-06-22 08:15:01', 'accepted', NULL),
	(37, 'ammar-', 'Project Manager', 'TestBugTeam', NULL, '2025-06-12 10:06:21', '2025-06-18 07:31:32', 'accepted', NULL),
	(38, 'TestRandomUser', 'Project Manager', 'new taufiq team', NULL, '2025-06-12 10:06:38', '2025-06-12 10:06:38', 'pending', NULL),
	(51, 'ammar-', 'Project Manager', 'TestNewCreation', NULL, '2025-06-18 07:32:16', '2025-06-18 07:32:27', 'accepted', NULL),
	(59, 'ammarjmldnout', 'Project Manager', 'Team2Delete', 38, '2025-06-18 07:48:32', '2025-06-23 15:33:15', 'accepted', NULL),
	(61, 'ammarjmldnout', 'Team Manager', 'TestNewProcess', NULL, '2025-06-23 17:06:48', '2025-06-23 17:06:48', 'accepted', NULL),
	(64, 'ammar-', 'Yahoo', 'Team2Delete', NULL, '2025-06-23 17:31:06', '2025-06-23 17:31:17', 'accepted', 'n3KpjXnP1LvvbvHZWKL8QPVk3vSVCOQb'),
	(65, 'ammarjmldnout', 'Team Manager', 'Team2Delete', NULL, '2025-06-23 17:31:06', '2025-06-23 17:31:17', 'accepted', ''),
	(66, 'ammarjmldnout', 'Project Manager', 'TestNewProcess', 40, '2025-06-24 03:10:49', '2025-06-24 03:43:55', 'accepted', NULL),
	(70, 'ammarjmldnout', 'Developers', 'Team2Delete', 37, '2025-06-24 03:43:15', '2025-06-24 03:43:15', 'accepted', NULL),
	(71, 'ammarjmldnout', 'Developer', 'Team2Delete', 36, '2025-06-24 03:54:18', '2025-06-24 03:54:18', 'accepted', NULL),
	(72, 'ammarjmldnout', 'Yahoo', 'Team2Delete', 39, '2025-06-24 04:02:39', '2025-06-24 04:02:39', 'accepted', NULL),
	(78, 'ammarjmldnout', 'Team Manager', 'NewTestTeam', NULL, '2025-06-24 15:47:59', '2025-06-24 15:47:59', 'accepted', NULL),
	(79, 'ammar-', 'Team Member', 'NewTestTeam', NULL, '2025-06-24 15:48:09', '2025-06-24 15:48:26', 'accepted', 'nV45usSlI0PaqRVWrdwDGlBnJlozzQSM'),
	(80, 'ammar-', 'Team Member', 'TestNewProcess', NULL, '2025-06-25 06:15:44', '2025-06-25 06:16:12', 'accepted', 'IJCzggEQh7W9uqseEIcADxeVGCfF1tz5'),
	(81, 'ammar-', 'Developer', 'TestNewProcess', 40, '2025-06-25 06:16:38', '2025-06-25 06:16:38', 'accepted', NULL),
	(82, 'ammarjmldnout', 'Team Member', 'uatTestTeam1', NULL, '2025-06-25 10:42:18', '2025-06-25 10:43:44', 'accepted', '9a7wzdwV1xrpqczBn6aquc5iw5lac4O7'),
	(84, 'UAT_1', 'Project Manager', 'uatTestTeam1', 41, '2025-06-25 10:44:30', '2025-06-25 10:44:30', 'accepted', NULL),
	(87, 'ammarjmldnout', 'Project Manager', 'uatTestTeam1', 41, '2025-06-25 10:45:04', '2025-06-25 10:45:04', 'accepted', NULL),
	(90, 'ammar-', 'Team Member', 'uatTestTeam1', NULL, '2025-06-25 10:55:27', '2025-06-25 10:56:11', 'accepted', '0MOdMJT0Ur25QDh9Ww8Yeb0rPUF2FuVO');

/*!40000 ALTER TABLE `teammappings` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table teams
# ------------------------------------------------------------

DROP TABLE IF EXISTS `teams`;

CREATE TABLE `teams` (
  `team_id` int unsigned NOT NULL AUTO_INCREMENT,
  `team_name` varchar(191) NOT NULL,
  `proj_name` varchar(191) NOT NULL DEFAULT '',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `team_names` longtext,
  PRIMARY KEY (`team_id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `teams` WRITE;
/*!40000 ALTER TABLE `teams` DISABLE KEYS */;

INSERT INTO `teams` (`team_id`, `team_name`, `proj_name`, `created_at`, `updated_at`, `team_names`) VALUES
	(3, 'Testers', '', '2024-01-10 06:25:31', '2024-01-10 06:25:31', NULL),
	(4, 'g4ew[ioj', '', '2024-01-18 03:38:12', '2024-01-18 03:38:12', NULL),
	(5, 'TestTeam', '', '2024-10-10 16:40:05', '2024-10-10 16:40:05', NULL),
	(6, 'newTeam', '', '2024-10-10 16:45:42', '2024-10-10 16:45:42', NULL),
	(7, 'testTeamAdmin', '', '2025-03-20 12:41:14', '2025-03-20 12:41:14', NULL),
	(8, 'testAddTea', '', '2025-03-25 14:04:32', '2025-03-25 14:04:32', NULL),
	(21, 'uatTestTeam1', '', '2025-06-08 11:00:34', '2025-06-08 11:00:34', NULL),
	(22, 'new_team', '', '2025-06-12 10:00:19', '2025-06-12 10:00:19', NULL),
	(23, 'TestBugTeam', '', '2025-06-12 10:06:21', '2025-06-12 10:06:21', NULL),
	(24, 'new taufiq team', '', '2025-06-12 10:06:38', '2025-06-12 10:06:38', NULL),
	(26, 'TestNewCreation', '', '2025-06-18 07:32:16', '2025-06-18 07:32:16', NULL),
	(28, 'Team2Delete', '', '2025-06-18 07:48:31', '2025-06-18 07:48:31', NULL),
	(31, 'TestNewProcess', '', '2025-06-23 17:06:48', '2025-06-23 17:06:48', NULL),
	(35, 'NewTestTeam', '', '2025-06-24 15:47:58', '2025-06-24 15:47:58', NULL);

/*!40000 ALTER TABLE `teams` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table tvt
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tvt`;

CREATE TABLE `tvt` (
  `tvt_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sprint` varchar(255) NOT NULL,
  `backlog_panel` varchar(255) NOT NULL,
  `user_story_panel` varchar(255) NOT NULL,
  `fr` varchar(255) NOT NULL,
  `nfr` varchar(255) DEFAULT NULL,
  `qaw` varchar(255) DEFAULT NULL,
  `project_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`tvt_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table user_role
# ------------------------------------------------------------

DROP TABLE IF EXISTS `user_role`;

CREATE TABLE `user_role` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `user_role` WRITE;
/*!40000 ALTER TABLE `user_role` DISABLE KEYS */;

INSERT INTO `user_role` (`id`, `user_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(1, 4, 0, NULL, NULL);

/*!40000 ALTER TABLE `user_role` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table user_stories
# ------------------------------------------------------------

DROP TABLE IF EXISTS `user_stories`;

CREATE TABLE `user_stories` (
  `u_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_story` varchar(255) NOT NULL,
  `means` varchar(191) NOT NULL,
  `prio_story` varchar(191) NOT NULL,
  `status_id` varchar(191) NOT NULL,
  `sprint_id` varchar(191) NOT NULL,
  `proj_id` varchar(191) NOT NULL,
  `perfeature_id` varchar(191) NOT NULL,
  `secfeature_id` varchar(191) NOT NULL,
  `general_nfr_id` varchar(255) NOT NULL,
  `specific_nfr_id` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_names` longtext,
  PRIMARY KEY (`u_id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `user_stories` WRITE;
/*!40000 ALTER TABLE `user_stories` DISABLE KEYS */;

INSERT INTO `user_stories` (`u_id`, `user_story`, `means`, `prio_story`, `status_id`, `sprint_id`, `proj_id`, `perfeature_id`, `secfeature_id`, `general_nfr_id`, `specific_nfr_id`, `created_at`, `updated_at`, `user_names`) VALUES
	(2, 'As a Developer, I am able to 1231 so that I can adadd', '1231', '0', '4', '0', '2', 'null', 'null', '', '', '2023-12-20 01:54:58', '2025-05-27 23:26:55', NULL),
	(5, 'As a Developer, I am able to dassadasdas so that I can adadsd', 'dassadasdas', '0', '4', '1', '2', 'null', 'null', '', '', '2024-01-08 15:18:22', '2024-01-08 15:18:22', NULL),
	(6, 'As a Developer, I am able to sadsad so that I can sad', 'sadsad', '0', '4', '8', '2', 'null', '[\"TestAddSec\"]', '', '', '2024-01-09 14:43:40', '2024-01-09 14:43:40', '[\"Shushma\",\"ammar-\",\"periyaa1\"]'),
	(7, 'As a Developer, I am able to dassda so that I can adasd', 'dassda', '0', '2', '4', '2', 'null', '[\"TestAddSec\"]', '', '', '2024-01-10 02:23:21', '2024-01-10 02:23:21', '[\"Shushma\",\"periyaa1\"]'),
	(8, 'As a Developer, I am able to dedev', 'dedev', '0', '4', '4', '2', '[\"TestAddPerf\"]', 'null', '', '', '2024-01-10 02:24:24', '2024-01-10 02:24:24', '[\"jeevan\",\"ammar-\"]'),
	(9, 'As a Developer, I am able to makan so that I can burger', 'makan', '0', '4', '8', '2', '[\"TestAddPerf\"]', '[\"TestAddSec\"]', '', '', '2024-01-10 02:32:42', '2024-01-10 02:32:42', 'null'),
	(10, 'As a Project Manager, I am able to dev', 'dev', '0', '41', '0', '6', 'null', 'null', '', '', '2024-01-16 11:50:53', '2025-05-27 23:06:55', '[\"ammar-\"]'),
	(12, 'As a Project Manager, I am able to us1 so that I can 1', 'us1', '0', '11', '16', '2', '[\"TestAddPerf\"]', '[\"TestAddSec\"]', '', '', '2024-01-17 00:43:29', '2025-05-27 23:26:49', '[\"ammar-\"]'),
	(13, 'As a Project Manager, I am able to adasdq so that I can asda', 'adasdq', '0', '2', '14', '2', 'null', '[\"TestAddSec\"]', '', '', '2024-01-30 06:53:14', '2024-01-30 06:53:14', '[\"ammar-\"]'),
	(14, 'As a Project Manager, I am able to add roles so that I can manage project access', 'eyah', '0', '2', '1', '2', 'null', '[\"TestAddSec\"]', '', '', '2024-10-10 19:07:55', '2024-10-10 19:07:55', '[\"ammar-\"]'),
	(15, 'As a Project Manager, I am able to dev so that I can manage project access', 'dev', '0', '2', '0', '2', 'null', 'null', '', '', '2024-10-10 19:23:21', '2024-10-10 19:23:21', NULL),
	(17, 'As a Project Manager, I am able to Manage', 'Manage', '0', '2', '16', '2', 'null', 'null', '[]', '[]', '2025-03-26 23:56:33', '2025-04-24 09:20:30', '[\"ammar-\"]'),
	(20, 'As a Project Manager, I am able to create backlog user stories', 'create backlog user stories', '0', '18', '0', '4', 'null', 'null', '[]', '[]', '2025-04-23 23:14:56', '2025-04-23 23:14:56', '[\"ammar-\"]'),
	(21, 'As a Project Manager, I am able to create new user story from backlog', 'create new user story from backlog', '0', '18', '0', '4', 'null', 'null', '[]', '[]', '2025-04-23 23:36:22', '2025-04-23 23:36:22', '[\"ammar-\"]'),
	(22, 'As a Project Manager, I am able to use story test', 'use story test', '0', '70', '0', '12', 'null', 'null', '[]', '[]', '2025-04-24 10:10:16', '2025-04-27 12:35:25', '[\"ammar-\"]'),
	(24, 'As a Project Manager, I am able to create tasks from US', 'create tasks from US', '0', '83', '32', '13', 'null', 'null', '[]', '[]', '2025-05-22 15:29:36', '2025-06-18 15:53:34', '[\"ammar-\"]'),
	(25, 'As a Developer, I am able to test new tasks', 'test new tasks', '0', '84', '32', '13', 'null', 'null', '[]', '[]', '2025-05-22 16:23:58', '2025-06-19 03:32:24', '[\"ammar-\"]'),
	(26, 'As a Project Manager, I am able to testFlow', 'testFlow', '0', '53', '28', '8', 'null', 'null', '[1]', '{\"1\":[\"1\"]}', '2025-05-27 22:39:42', '2025-06-11 06:28:08', '[\"ammar-\"]'),
	(27, 'As a Project Manager, I am able to testasdasd', 'testasdasd', '0', '40', '0', '6', 'null', 'null', '[]', '[]', '2025-05-27 22:53:57', '2025-05-27 23:04:18', '[\"ammar-\"]'),
	(28, 'As a Project Manager, I am able to adaasdasdasdasd', 'adaasdasdasdasd', '0', '40', '0', '6', 'null', 'null', '[]', '[]', '2025-05-27 22:57:27', '2025-05-27 22:57:27', '[\"ammar-\"]'),
	(29, 'As a Project Manager, I am able to adas', 'adas', '0', '43', '0', '6', 'null', 'null', '[]', '[]', '2025-05-27 23:05:34', '2025-06-19 08:50:12', 'null'),
	(31, 'As a Project Manager, I am able to perform UAT tests so that I can validate SAgile module features', 'perform UAT tests', '0', '144', '30', '28', 'null', 'null', '[]', '[]', '2025-06-10 02:28:14', '2025-06-15 11:20:42', 'null'),
	(32, 'As a Project Manager, I am able to create and assign tasks to team members so that I can organise project work efficiently.', 'create and assign tasks to team members', '0', '170', '31', '34', 'null', 'null', '[]', '[]', '2025-06-16 04:27:19', '2025-06-16 04:32:19', '[\"ammar-\"]'),
	(33, 'As a Developer, I am able to update the status of my assigned tasks.', 'update the status of my assigned tasks.', '0', '170', '31', '34', 'null', 'null', '[]', '[]', '2025-06-16 04:27:34', '2025-06-16 04:32:19', 'null'),
	(34, 'As a Project Manager, I am able to view a project dashboard to monitor overall progress.', 'view a project dashboard to monitor overall progress.', '0', '170', '31', '34', 'null', 'null', '[]', '[]', '2025-06-16 04:27:45', '2025-06-16 04:32:19', 'null'),
	(41, 'As a Project Manager, I am able to perform UAT tests so that I can manage project access', 'perform UAT tests', '0', '185', '0', '37', 'null', 'null', '[]', '[]', '2025-06-24 05:30:47', '2025-06-24 05:30:47', '[\"ammarjmldnout\"]'),
	(42, 'As a Project Manager, I am able to testNewCreate so that I can asdasd', 'testNewCreate', '0', '197', '0', '40', 'null', 'null', '[]', '[]', '2025-06-24 14:54:50', '2025-06-24 14:54:50', '[\"ammarjmldnout\"]'),
	(43, 'As a Project Manager, I am able to Create new projects', 'Create new projects', '0', '201', '33', '41', 'null', 'null', '[]', '[]', '2025-06-25 13:47:07', '2025-06-25 14:26:32', '[\"ammarjmldnout\"]'),
	(44, 'As a Developer, I am able to Test User Story so that I can uat it', 'Test User Story', '0', '201', '33', '41', 'null', 'null', '[]', '[]', '2025-06-25 14:25:48', '2025-06-25 14:26:32', '[\"UAT_1\",\"ammarjmldnout\"]');

/*!40000 ALTER TABLE `user_stories` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table user_story_general_nfr
# ------------------------------------------------------------

DROP TABLE IF EXISTS `user_story_general_nfr`;

CREATE TABLE `user_story_general_nfr` (
  `user_story_id` bigint unsigned NOT NULL,
  `general_nfr_id` bigint unsigned NOT NULL,
  `specific_nfr_id` int NOT NULL,
  PRIMARY KEY (`user_story_id`,`general_nfr_id`,`specific_nfr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `user_story_general_nfr` WRITE;
/*!40000 ALTER TABLE `user_story_general_nfr` DISABLE KEYS */;

INSERT INTO `user_story_general_nfr` (`user_story_id`, `general_nfr_id`, `specific_nfr_id`) VALUES
	(26, 1, 1);

/*!40000 ALTER TABLE `user_story_general_nfr` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table users
# ------------------------------------------------------------

DROP TABLE IF EXISTS `users`;

CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `username` varchar(191) DEFAULT NULL,
  `identifier` varchar(191) DEFAULT NULL,
  `country` varchar(191) NOT NULL,
  `email` varchar(191) NOT NULL,
  `is_lecturer` tinyint NOT NULL DEFAULT '0',
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(191) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;

INSERT INTO `users` (`id`, `name`, `username`, `identifier`, `country`, `email`, `is_lecturer`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
	(1, 'jeevan', 'jeevan', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$JRPDtIDLnuMCs7jb6h8WL.xbBz/YxctPX6fQznFcJGvDLteLjHYhy', 'xFmPPTQfJIsRefbD2k8xDSBLhgAcV1tRhK23T3H3ZFHRxoSexc0dUC2bSQhr', '2023-09-08 04:02:56', '2023-09-08 04:02:56'),
	(3, 'periyaa', 'periyaa1', NULL, 'Afghanistan', '<EMAIL>', 0, NULL, '$2y$10$Pl7aHWuNzGtr430UBDvAu.rqe3xDCrulTDWUUoluc7nG.GLS9o4Cu', NULL, '2023-11-06 06:14:09', '2023-11-06 06:14:09'),
	(4, 'Ammar', 'ammar-', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$mFZFJ7SPGl1DADnzxu8uiuU4CMwnrUZDX7yslyVRmRiCd0BSJKxGW', '8IE3YgeWYYS16esM4Y4nQiEZKU3ZyaZWmHsDrt1WO3KCOg4DcpbDhJ9B4Oi2', '2023-12-12 16:16:33', '2023-12-12 16:16:33'),
	(5, 'testUser', 'passUser', NULL, 'Afghanistan', '<EMAIL>', 0, NULL, '$2y$10$XDELxRxjcfTV9FyNFPxkzegRRYjBKWcnHlUWBsy1EAAHmhpkSX/C.', NULL, '2024-10-10 15:58:59', '2024-10-10 15:58:59'),
	(6, 'newUser', 'newUser', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$c9p0RmS8L6Wladsd2nsWFuu5BDrVWkV2p9qyNxl4umxwSBbzLaMF.', NULL, '2024-10-10 16:20:47', '2024-10-10 16:20:47'),
	(7, 'admin', 'admin', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$n0noKkBk26yCc2pOjOgqkeunsvJ1Ux2iYrWDhx6Iqddebklyk5kKC', NULL, '2025-03-20 12:08:49', '2025-03-20 12:08:49'),
	(8, 'testUser2', 'testUser2', NULL, 'Afghanistan', '<EMAIL>', 0, NULL, '$2y$10$VhGJWPSHp5zoCNDE5A8jX.e5LLDpvW8JTDxpUAP8U4033.YqWQnBq', NULL, '2025-03-20 12:40:58', '2025-03-20 12:40:58'),
	(9, 'Test User', 'testuser', 'A12345', 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$qFnNZlSTp.zLH5WyDYDi1eX.hjAlLwJE/8Yo857f4ElphFg6o38iS', 'cDI1Ih1WNfOGhlh1Xf4IzkYvNXipm30oHcuC7ZAt6GJXVkQRydmsmbhgBeSI', '2025-05-22 23:31:57', '2025-05-22 23:31:57'),
	(13, 'testLecturer', 'TestLecturer', 'STAFF123', 'Malaysia', '<EMAIL>', 1, NULL, '$2y$10$KHxXUx52mkohnIjEzwelmuZSf1b16FjrJ4IQsfPGmYdTVkRp9WS22', 'EksqMedXSUb85daSqaZW99WfYDaQhT7zBJpjUDdjNvsIBcYwmjIfCeJOtNu2', '2025-05-25 17:08:12', '2025-05-28 14:29:11'),
	(14, 'testUserSSOO', 'testUSERSSO', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$oPPVsUUqk3Ka9RIf9tYrzewqMsiYoWk76oDA7B38cLVVMV1EuR4K2', 'bNcdsds52GrPGaktHgXHvxSCg8HBasYkMNdAEX1faYBSqbS4Qvrut2umjKiS', '2025-05-25 19:28:12', '2025-05-25 19:31:11'),
	(24, 'TestRandomUser', 'TestRandomUser', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$tlXT.yi.9pI/hXV3DOPZFeK5pf95Y.uypqrDeGU3hmjl5yd0MmB9m', NULL, '2025-05-30 13:53:06', '2025-05-30 13:53:06'),
	(25, 'UAT_TestUser1', 'UAT_1', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$XYhv8VgQJieeQ/ph3399t.3kVJxc8Aal8T3MJMAUoxX81OhwDY.QG', NULL, '2025-06-08 06:43:10', '2025-06-08 06:43:10'),
	(26, 'uatTeamMember', 'uatTeamMember', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$pYkfSMnTkbzDg1/e2hU72OEH5zAJM3N8fud3mqSl/CsZZFC.vj9Oe', NULL, '2025-06-08 10:26:53', '2025-06-08 10:26:53'),
	(27, 'ASDF ASDF', 'taufiq', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$05UBnYB09BwQowOBCqvEXeTFgYlKrz57szeKWF87vYaKRl4cftij6', NULL, '2025-06-11 15:31:34', '2025-06-11 15:31:34'),
	(28, 'fypagile', 'PlaceholderPSM', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$32K8pWo4DlE29AOjsY3AHeEk.kJhX8HzVdwCcW7ARMrfyHcpKBXIO', NULL, '2025-06-16 04:23:14', '2025-06-16 04:23:14'),
	(29, 'MailRecipAmmarjmldn', 'ammarjmldnout', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$cA1q9cHfv2cH9etsxFlhMOJfL683I5GWWL0EM8ixmhzi2Os2qU1ae', NULL, '2025-06-18 03:15:32', '2025-06-18 03:15:32');

/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;



/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;


