<script>
// Enhanced Kanban functionality JavaScript - Updated for new structure
document.addEventListener("DOMContentLoaded", () => {
    
    // Function to handle common logic for handling AJAX responses
    function handleAjaxResponse(response) {
        console.log(response.message);
        if (response.reload) {
            location.reload();
        }
    }

    // Function to show notification/toast
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;
        
        // Add styles for notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 1001;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Function to change the lane name
    function changeLaneName(lane, newName) {
        const heading = lane.querySelector(".heading");
        heading.innerText = newName;

        // Get the status ID associated with the lane
        const statusId = lane.dataset.statusId;

        // Make an AJAX request to update the lane name in the database
        fetch(kanbanUpdateStatusRoute, {
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: JSON.stringify({
                statusId: statusId,
                newName: newName,
            }),
        })
            .then(response => response.json())
            .then(data => {
                console.log('After AJAX request to update lane name');
                console.log(data);
                showNotification(data.message, 'success');
                handleAjaxResponse(data);
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Failed to update lane name', 'error');
            });
    }

    // Logic for putting task above other task - drag&drop logic
    const insertAboveTask = (zone, mouseY) => {
        const els = zone.querySelectorAll(".task:not(.is-dragging)");

        let closestTask = null;
        let closestOffset = Number.NEGATIVE_INFINITY;

        els.forEach((task) => {
            const { top } = task.getBoundingClientRect();
            const offset = mouseY - top;

            if (offset < 0 && offset > closestOffset) {
                closestOffset = offset;
                closestTask = task;
            }
        });

        return closestTask;
    };

    // Get DOM elements
    const addLaneBtn = document.getElementById("add-lane-btn");
    const renameBtns = document.querySelectorAll(".rename-btn");
    const deleteBtns = document.querySelectorAll(".delete-btn");
    const saveBtn = document.getElementById("save-btn");

    // Listener for rename button
    renameBtns.forEach((btn) => {
        btn.addEventListener("click", (e) => {
            e.stopPropagation();
            const currentName = btn.closest(".swim-lane").querySelector(".heading").innerText;
            const newName = prompt("Enter new name for the lane:", currentName);

            if (newName !== null && newName.trim() !== '') {
                const lane = btn.closest(".swim-lane");
                changeLaneName(lane, newName.trim());
            }
        });
    });

    // Listener for delete button
    deleteBtns.forEach((btn) => {
        btn.addEventListener("click", (e) => {
            e.stopPropagation();
            const lane = btn.closest(".swim-lane");
            const laneTitle = lane.querySelector(".heading").innerText;
            const laneId = lane.dataset.statusId;

            // Show confirmation dialog
            if (confirm(`Are you sure you want to delete the "${laneTitle}" lane? This action cannot be undone.`)) {
                // Make an AJAX request to delete the lane and update task statuses
                fetch(kanbanDeleteStatusRoute, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        laneId: laneId,
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    console.log('After AJAX request to delete lane');
                    console.log(data);

                    // Check if the deletion was successful before removing the lane from the UI
                    if (data.success) {
                        // Add fade out animation
                        lane.style.transition = 'all 0.3s ease';
                        lane.style.opacity = '0';
                        lane.style.transform = 'scale(0.95)';
                        
                        setTimeout(() => {
                            lane.remove();
                        }, 300);
                        
                        showNotification(data.message, 'success');
                    } else {
                        showNotification(data.error, 'error');
                    }

                    handleAjaxResponse(data);
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Failed to delete lane', 'error');
                });
            }
        });
    });

    // Listener for delete task button
    document.querySelectorAll('.delete-task-btn').forEach((btn) => {
        btn.addEventListener("click", (e) => {
            e.stopPropagation();
            const taskContainer = btn.closest(".task");
            const taskTitle = taskContainer.querySelector(".task-title").innerText;
            const taskId = taskContainer.dataset.taskId;

            // Show a confirmation alert before proceeding with deletion
            const confirmDelete = window.confirm(`Are you sure you want to delete "${taskTitle}"? This action cannot be undone.`);

            if (confirmDelete) {
                // Make an AJAX request to delete the task
                fetch(kanbanDeleteTaskRoute, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        taskId: taskId,
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    console.log('After AJAX request to delete task');
                    console.log(data);

                    // Check if the deletion was successful before removing the task from the UI
                    if (data.success) {
                        // Add fade out animation
                        taskContainer.style.transition = 'all 0.3s ease';
                        taskContainer.style.opacity = '0';
                        taskContainer.style.transform = 'scale(0.95)';
                        
                        setTimeout(() => {
                            taskContainer.remove();
                        }, 300);
                        
                        showNotification('Task deleted successfully', 'success');
                    } else {
                        console.error(data.error);
                        showNotification(data.error || 'Failed to delete task', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Failed to delete task', 'error');
                });
            } else {
                console.log("Task deletion was canceled.");
            }
        });
    });

    // Listener for save button - triggered through task movement
    if (saveBtn) {
        saveBtn.addEventListener("click", () => {
            console.log("Save button clicked");

            // Iterate through all lanes and tasks to gather their positions
            const positions = [];

            document.querySelectorAll(".swim-lane").forEach((lane, laneIndex) => {
                const laneId = lane.dataset.statusId;

                lane.querySelectorAll(".task").forEach((task, taskIndex) => {
                    const taskId = task.dataset.taskId;

                    positions.push({
                        taskId: taskId,
                        statusId: laneId,
                        position: taskIndex + 1, // Add 1 to make positions 1-based
                    });
                });
            });

            console.log("Task positions to save:", positions);

            // Make an AJAX request to save the task positions in the database
            fetch(kanbanUpdateTaskStatusRoute, {
                method: 'PUT',
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    positions: positions,
                }),
            })
            .then(response => response.json())
            .then(data => {
                // Handle the response from the controller method
                console.log('After AJAX request to save task positions');
                console.log(data);
                showNotification('Task positions updated', 'success');
                handleAjaxResponse(data);
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Failed to update task positions', 'error');
            });
        });
    }

    // Task Dragging Logic
    const draggables = document.querySelectorAll(".task");
    const droppables = document.querySelectorAll(".swim-lane");

    draggables.forEach((task) => {
        task.addEventListener("dragstart", (e) => {
            task.classList.add("is-dragging");
            e.dataTransfer.effectAllowed = 'move';
        });

        task.addEventListener("dragend", (e) => {
            task.classList.remove("is-dragging");
            
            // Remove drag-over class from all lanes
            droppables.forEach(lane => {
                lane.classList.remove("drag-over");
            });

            // Trigger the click event on the save button after the task is dropped
            if (saveBtn) {
                saveBtn.click();
            }
        });
    });

    droppables.forEach((zone) => {
        zone.addEventListener("dragover", (e) => {
            e.preventDefault();
            zone.classList.add("drag-over");

            const bottomTask = insertAboveTask(zone.querySelector('.lane-content'), e.clientY);
            const curTask = document.querySelector(".is-dragging");

            if (curTask) {
                const laneContent = zone.querySelector('.lane-content');
                
                // Check if the task actually needs to be moved to prevent unnecessary DOM manipulation
                if (!bottomTask) {
                    // Only append if it's not already the last child
                    if (laneContent.lastElementChild !== curTask) {
                        laneContent.appendChild(curTask);
                    }
                } else {
                    // Only insert if it's not already in the correct position
                    if (bottomTask.previousElementSibling !== curTask) {
                        laneContent.insertBefore(curTask, bottomTask);
                    }
                }
            }
        });

        zone.addEventListener("dragleave", (e) => {
            // Only remove drag-over if we're actually leaving the zone
            if (!zone.contains(e.relatedTarget)) {
                zone.classList.remove("drag-over");
            }
        });

        zone.addEventListener("drop", (e) => {
            e.preventDefault();
            zone.classList.remove("drag-over");
        });
    });

    // Add event listener for adding a new lane
    if (addLaneBtn) {
        addLaneBtn.addEventListener("click", () => {
            const newLaneName = prompt("Enter the name for the new lane:");
            if (newLaneName !== null && newLaneName.trim() !== '') {

                const dataToSend = {
                    statusName: newLaneName.trim(),
                    sprintID: sprintId,
                    project_id: projectId
                };

                // Make an AJAX request to call the controller method
                fetch(kanbanCreateStatusRoute, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify(dataToSend),
                })
                .then(response => response.json())
                .then(data => {
                    // Handle the response from the controller method
                    console.log('After AJAX request');
                    console.log(data);
                    showNotification(data.message, 'success');
                    handleAjaxResponse(data);
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Failed to create new lane', 'error');
                });
            }
        });
    }    // Listener for edit task button
    document.querySelectorAll('.edit-task-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const taskCard = this.closest('.task');
            const taskId = taskCard.getAttribute('data-task-id');
            window.location.href = kanbanUpdateTaskPageRoute.replace(':taskId', taskId);
        });
    });

    // Show/Hide comments functionality
    document.querySelectorAll('.show-more-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const commentsContainer = this.closest('.comments-section');
            const hiddenComments = commentsContainer.querySelectorAll('.hidden-comment');
            const showLessBtn = commentsContainer.querySelector('.show-less-btn');
            
            hiddenComments.forEach(comment => {
                comment.style.display = 'block';
                comment.classList.remove('hidden-comment');
            });
            
            this.style.display = 'none';
            showLessBtn.style.display = 'inline-block';
        });
    });

    document.querySelectorAll('.show-less-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const commentsContainer = this.closest('.comments-section');
            const allComments = commentsContainer.querySelectorAll('.comment-wrapper');
            const showMoreBtn = commentsContainer.querySelector('.show-more-btn');
            
            // Hide comments beyond the first 2
            allComments.forEach((comment, index) => {
                if (index > 1) {
                    comment.style.display = 'none';
                    comment.classList.add('hidden-comment');
                }
            });
            
            this.style.display = 'none';
            showMoreBtn.style.display = 'inline-block';
        });
    });

    // Filter comments by creator
    document.querySelectorAll('.filter-created-by-select').forEach(select => {
        select.addEventListener('change', function(e) {
            e.stopPropagation();
            const taskId = this.dataset.taskId;
            const selectedCreator = this.value;
            const commentsContainer = document.getElementById(`comments-container-${taskId}`);
            const comments = commentsContainer.querySelectorAll('.comment-wrapper');
            
            comments.forEach(comment => {
                const createdBy = comment.getAttribute('filter-created-by');
                
                if (selectedCreator === 'all' || createdBy === selectedCreator) {
                    comment.style.display = 'block';
                } else {
                    comment.style.display = 'none';
                }
            });
        });
    });

    // Prevent form submission on enter key in comment inputs
    document.querySelectorAll('input, textarea').forEach(input => {
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
            }
        });
    });

    // Handle responsive design adjustments
    function handleResize() {
        const kanbanBoard = document.querySelector('.kanban-board');
        const swimLanes = document.querySelectorAll('.swim-lane');
        
        if (window.innerWidth <= 768) {
            kanbanBoard.style.flexDirection = 'column';
            swimLanes.forEach(lane => {
                lane.style.minWidth = '100%';
                lane.style.maxWidth = '100%';
            });
        } else {
            kanbanBoard.style.flexDirection = 'row';
            swimLanes.forEach(lane => {
                lane.style.minWidth = '320px';
                lane.style.maxWidth = '320px';
            });
        }
    }

    // Initial resize check
    handleResize();
    
    // Listen for window resize
    window.addEventListener('resize', handleResize);

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + N to add new lane
        if ((e.ctrlKey || e.metaKey) && e.key === 'n' && addLaneBtn) {
            e.preventDefault();
            addLaneBtn.click();
        }
        
        // Escape key to close modals
        if (e.key === 'Escape') {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.style.display = 'none';
            });
        }
    });

    console.log('Enhanced Kanban board initialized successfully');
});
</script>