@include('inc.kanbanStyle')

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Kanban Board - {{ $sprint->sprint_name ?? 'No Active Sprint' }}</title>
    
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
</head>

<body>
    <div class="kanban-container">
        @if(!$hasActiveSprint)
            <div class="no-sprint-container">
                <h3><i class="fas fa-calendar-times"></i> No Active Sprint</h3>
                <p>There is currently no active sprint for this project.</p>
            </div>
        @else
            <div class="kanban-header">
                <h1 class="kanban-title">
                    <i class="fas fa-tasks"></i>
                    {{ $sprint->sprint_name }}
                </h1>
                <div class="kanban-actions">
                    @if ($isSprintOverdue)
                        <div class="overdue-alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            Sprint is overdue!
                        </div>
                    @endif
                    @can('addLane_kanban', $project)
                    <button id="add-lane-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add Lane
                    </button>
                    @endcan
                </div>
            </div>

            <button id="save-btn" style="display: none">Save</button>

            <div class="kanban-board">
                @foreach ($statuses as $status)
                    <?php $taskList = $tasksByStatus[$status->id] ?? []; ?>
                    <div class="swim-lane" data-status-id="{{ $status->id }}">
                        <div class="lane-header">
                            <h3 class="lane-title heading">{{ $status->title }}</h3>
                            <div class="lane-actions">
                                @can('editLane_kanban', $project)
                                <button type="button" class="btn-icon rename-btn" title="Rename Lane">
                                    <i class="fas fa-edit"></i>
                                </button>
                                @endcan
                                @can('deleteLane_kanban', $project)
                                <button type="button" class="btn-icon delete-btn" title="Delete Lane">
                                    <i class="fas fa-trash"></i>
                                </button>
                                @endcan
                            </div>
                        </div>

                        <div class="lane-content">
                            @can('addTask_kanban', $project)
                            <form action="{{ route('kanban.createTask', [], false) }}" method="post" class="add-task-form">
                                @csrf
                                <input type="hidden" name="sprintId" value="{{ $sprint->sprint_id }}">
                                <input type="hidden" name="statusId" class="status-id-input" value="{{ $status->id }}">
                                <button type="submit" class="btn btn-success btn-sm new-submit-btn">
                                    <i class="fas fa-plus"></i>
                                    Add Task
                                </button>
                            </form>
                            @endcan

                            @foreach ($taskList as $task)                                
                                <div class="task-card task {{ $task->isOverdue ? 'overdue-task' : '' }} 
                                                           {{ Auth::user()->can('updateTaskStatus_kanban', $project) ? '' : 'non-draggable' }}" 
                                     draggable="{{ Auth::user()->can('updateTaskStatus_kanban', $project) ? 'true' : 'false' }}" 
                                     data-task-id="{{ $task->id }}">
                                    
                                    @if ($task->isOverdue)
                                        <div class="overdue-badge">
                                            <i class="fas fa-clock"></i> Overdue
                                        </div>
                                    @endif

                                    <div class="task-header">
                                        <h4 class="task-title">{{ $task->title }}</h4>
                                        @can('editTask_kanban', $project)
                                            <button type="button" class="edit-task-btn" title="Edit Task">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        @endcan
                                        @can('deleteTask_kanban', $project)
                                            <button type="button" class="delete-task-btn" title="Delete Task">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        @endcan
                                    </div>

                                    <div class="filter-section">
                                        <div class="filter-row">
                                            <label class="filter-label" for="filter_created_by_{{ $task->id }}">Filter:</label>
                                            <select id="filter_created_by_{{ $task->id }}" 
                                                    class="form-select filter-created-by-select" 
                                                    data-task-id="{{ $task->id }}">
                                                <option value="all">All Creators</option>
                                                @foreach ($task->comments->unique('created_by') as $comment)
                                                    <option value="{{ $comment->created_by }}">{{ $comment->created_by }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div class="filter-row">
                                            <label class="filter-label" for="sort_date_{{ $task->id }}">Sort:</label>
                                            <form method="GET" 
                                                  action="{{ route('sprint.kanbanPage', ['proj_id' => $project->id, 'sprint_id' => $sprint->sprint_id]) }}" 
                                                  class="sort-comments-form" 
                                                  id="sort-comments-form-{{ $task->id }}"
                                                  style="flex: 1;">
                                                <select name="sort_date" 
                                                        id="sort_date_{{ $task->id }}" 
                                                        class="form-select" 
                                                        onchange="this.form.submit()">
                                                    <option value="desc" {{ request('sort_date') == 'desc' ? 'selected' : '' }}>Newest First</option>
                                                    <option value="asc" {{ request('sort_date') == 'asc' ? 'selected' : '' }}>Oldest First</option>
                                                </select>
                                            </form>
                                        </div>
                                    </div>
                                    @if (!$task->isOverdue && !$isSprintOverdue)
                                        @can('addComment_kanban', $project)
                                        <div class="task-actions">
                                            <button type="button" 
                                                    class="btn btn-primary btn-sm add-comment-btn" 
                                                    data-task-id="{{ $task->id }}">
                                                <i class="fas fa-comment-plus"></i>
                                                Add Comment
                                            </button>
                                        </div>
                                        @endcan
                                    @endif

                                    <div class="comments-section" id="comments-container-{{ $task->id }}">
                                        @if ($task->comments->isEmpty())
                                            <div class="comment-item">
                                                <div class="comment-content">No comments yet.</div>
                                            </div>
                                        @else
                                            @foreach ($task->comments as $index => $comment)
                                                <div class="comment-item comment-wrapper {{ $index > 1 ? 'hidden-comment' : '' }}" 
                                                     filter-created-by="{{ $comment->created_by }}" 
                                                     data-created-date="{{ $comment->created_at }}">
                                                    <div class="comment-content">
                                                        {{ $comment->comment }}
                                                        @if ($comment->updated_at && $comment->updated_at != $comment->created_at)
                                                            <span class="edited-indicator">(edited)</span>
                                                        @endif
                                                    </div>
                                                    
                                                    <div class="comment-meta">
                                                        <div class="comment-info">
                                                            <span>{{ \Carbon\Carbon::parse($comment->created_at)->format('M d, Y') }}</span>
                                                            <span>By: {{ $comment->created_by }}</span>
                                                        </div>
                                                        
                                                        @if (!$task->isOverdue && !$isSprintOverdue && $comment->created_by === auth()->user()->username)
                                                            <div class="comment-actions">
                                                                <button type="button" 
                                                                        class="btn-icon edit-comment-btn" 
                                                                        data-comment-id="{{ $comment->id }}"
                                                                        title="Edit Comment">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <form action="{{ route('tasks.deleteComment', $comment->id) }}" 
                                                                      method="POST" 
                                                                      style="display:inline;">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" 
                                                                            class="btn-icon delete-comment-btn"
                                                                            title="Delete Comment">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </form>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            @endforeach

                                            @if ($task->comments->count() > 2)
                                                <button class="show-toggle-btn show-more-btn">
                                                    <i class="fas fa-chevron-down"></i> Show More
                                                </button>
                                                <button class="show-toggle-btn show-less-btn" style="display: none;">
                                                    <i class="fas fa-chevron-up"></i> Show Less
                                                </button>
                                            @endif
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        @endif
        </div>
        <style>
        /* Style for non-draggable tasks */
        .task-card.non-draggable {
            cursor: default;
            opacity: 0.9;
            border-left: 3px solid #888;
        }
        
        .task-card.non-draggable::after {
            content: "";
            position: absolute;
            top: 5px;
            right: 30px;
            font-size: 14px;
        }

        /* Task header styles */
        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;  /* Changed to center alignment */
            margin-bottom: 10px;
            position: relative;
            gap: 8px;  /* Added gap between elements */
        }

        .task-title {
            margin: 0;
            flex: 1;
            padding-right: 8px;  /* Reduced padding */
            font-size: 0.95rem;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>

    <!-- Edit Comment Modal -->
    <div id="editCommentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Edit Comment</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="form-group">
                <textarea id="editCommentInput" class="form-control" rows="4" placeholder="Enter your comment..."></textarea>
            </div>
            <button id="saveCommentBtn" class="btn btn-primary">
                <i class="fas fa-save"></i>
                Save Changes
            </button>
        </div>
    </div>

    <!-- Create Comment Modal -->
    <div id="createCommentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Add Comment</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="form-group">
                <textarea id="createCommentInput" class="form-control" rows="4" placeholder="Enter your comment..."></textarea>
            </div>
            <button id="saveCreateCommentBtn" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Add Comment
            </button>
        </div>
    </div>    <script>
        // Define variables used in the separated scripts
        const csrfToken = '{{ csrf_token() }}';
        const projectId = "{{ $project->id ?? '' }}";
        const sprintId = "{{ $sprint->sprint_id ?? '' }}";
        
        // Define all the route variables needed in the scripts
        const kanbanUpdateStatusRoute = '{{ route("kanban.updateStatus") }}';
        const kanbanDeleteStatusRoute = '{{ route("kanban.deleteStatus") }}';
        const kanbanDeleteTaskRoute = '{{ route("kanban.deleteTask") }}';
        const kanbanUpdateTaskStatusRoute = '{{ route("kanban.updateTaskStatus") }}';
        const kanbanCreateStatusRoute = '{{ route("kanban.createStatus") }}';
        const kanbanUpdateTaskPageRoute = '{{ route("kanban.updateTaskPage", ["taskId" => ":taskId"]) }}';
        
        // Permission flags for JavaScript usage
        const canUpdateTaskStatus = {{ Auth::user()->can('updateTaskStatus_kanban', $project) ? 'true' : 'false' }};

        // Function to calculate and send kanban height to parent
        function updateKanbanHeight() {
            const kanbanBoard = document.querySelector('.kanban-board');
            const kanbanContainer = document.querySelector('.kanban-container');
            if (kanbanBoard && kanbanContainer) {
                const lanes = kanbanBoard.querySelectorAll('.swim-lane');
                let maxHeight = 0;
                
                // Calculate the maximum height needed
                lanes.forEach(lane => {
                    const laneContent = lane.querySelector('.lane-content');
                    const totalHeight = Array.from(laneContent.children).reduce((sum, child) => sum + child.offsetHeight, 0);
                    maxHeight = Math.max(maxHeight, totalHeight + 330); // Add padding for header and margins
                });

                // Add height of the header and any margins
                const totalHeight = maxHeight + kanbanContainer.offsetTop + 100;

                // Send message to parent frame
                window.parent.postMessage({
                    type: 'kanbanHeight',
                    height: totalHeight
                }, '*');
            }
        }        // Call on initial load and after any content changes
        window.addEventListener('load', updateKanbanHeight);
        
        // Create a MutationObserver to watch for changes in the kanban board
        const observer = new MutationObserver(updateKanbanHeight);
        
        // Start observing the kanban board for changes
        window.addEventListener('load', () => {
            const kanbanBoard = document.querySelector('.kanban-board');
            if (kanbanBoard) {
                observer.observe(kanbanBoard, {
                    childList: true,
                    subtree: true,
                    attributes: true
                });
            }
            
            // Add disabled message for non-draggable tasks
            if (!canUpdateTaskStatus) {
                const nonDraggableTasks = document.querySelectorAll('.task-card.non-draggable');
                nonDraggableTasks.forEach(task => {
                    task.title = "You don't have permission to move tasks";
                    
                    // Override the dragstart event to prevent dragging
                    task.addEventListener('dragstart', (e) => {
                        e.preventDefault();
                        return false;
                    });
                });
            }
        });
    </script>

    <!-- Include Separated JavaScript Files -->
    @include('inc.kanban.kanban-script-js')
    @include('inc.kanban.comments-script-js')
</body>
</html>