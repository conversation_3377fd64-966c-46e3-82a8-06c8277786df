<?php

namespace App\Traits;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

trait HasCachedPermissions
{
    /**
     * Cached permissions for the current context
     * @var array|null
     */
    public $cachedPermissions;
    
    /**
     * Team role mapping data
     * @var array|null
     */
    public $teamRoleMap;
    
    /**
     * Current project context
     * @var object|null
     */
    public $currentProject;
    
    /**
     * Current role name
     * @var string|null
     */
    public $currentRoleName;
    
    /**
     * Current role ID
     * @var int|null
     */
    public $currentRoleId;
    
    /**
     * Current team name
     * @var string|null
     */
    public $currentTeamName;
    
    /**
     * Queried roles cache to avoid duplicate database queries
     * @var array
     */
    public $queriedRoles = [];

    /**
     * Check if the user has a specific permission for the current project context
     *
     * @param string $permission
     * @param string|null $teamName
     * @param int|null $projectId
     * @return bool
     */
    public function hasPermission($permission, $teamName = null, $projectId = null)
    {
        // Admin always has access
        if ($this->isAdmin()) {
            return true;
        }
        
        // If team name is provided, check for that specific team
        if ($teamName) {
            return $this->hasPermissionForTeamProject($permission, $teamName, $projectId);
        }
        
        // Otherwise check current context
        if (isset($this->cachedPermissions)) {
            return in_array($permission, $this->cachedPermissions);
        }
        
        return false;
    }
    
    /**
     * Get all cached permissions for the user in the current project context
     *
     * @param string|null $teamName
     * @param int|null $projectId
     * @return array
     */
    public function getPermissions($teamName = null, $projectId = null)
    {
        if ($teamName) {
            $teamData = $this->getTeamDataForProject($teamName, $projectId);
            return $teamData['permissions'] ?? [];
        }
        
        return $this->cachedPermissions ?? [];
    }
    
    /**
     * Get the current team name
     *
     * @return string|null
     */
    public function getCurrentTeam()
    {
        return $this->currentTeamName ?? null;
    }
    
    /**
     * Get the current role name
     *
     * @param string|null $teamName
     * @param int|null $projectId
     * @return string|null
     */
    public function getCurrentRole($teamName = null, $projectId = null)
    {
        if ($teamName) {
            $teamData = $this->getTeamDataForProject($teamName, $projectId);
            return $teamData['role_name'] ?? null;
        }
        
        return $this->currentRoleName ?? null;
    }
    
    /**
     * Get all teams and roles for this user
     *
     * @return array
     */
    public function getAllTeams()
    {
        if (isset($this->teamRoleMap)) {
            return array_keys($this->teamRoleMap);
        }
        
        return [];
    }    /**
     * Get the project ID for a given team
     *
     * @param string $teamName
     * @return int|array|null Returns int for single project, array for multiple projects, null if not found
     */
    public function getProjectIdForTeam($teamName)
    {
        if (isset($this->teamRoleMap[$teamName])) {
            $teamData = $this->teamRoleMap[$teamName];
            
            // Handle multiple projects case
            if (isset($teamData['multiple_projects']) && $teamData['multiple_projects']) {
                // Return array of project IDs
                return array_keys($teamData['projects']);
            } else {
                // Single project case
                return $teamData['project_id'] ?? null;
            }
        }
        
        return null;
    }
    
    /**
     * Get all project IDs for a given team (always returns array)
     *
     * @param string $teamName
     * @return array
     */
    public function getAllProjectIdsForTeam($teamName)
    {
        if (isset($this->teamRoleMap[$teamName])) {
            $teamData = $this->teamRoleMap[$teamName];
            
            // Handle multiple projects case
            if (isset($teamData['multiple_projects']) && $teamData['multiple_projects']) {
                return array_keys($teamData['projects']);
            } else {
                // Single project case
                $projectId = $teamData['project_id'] ?? null;
                return $projectId ? [$projectId] : [];
            }
        }
        
        return [];
    }
    
    /**
     * Get team data for a specific project
     *
     * @param string $teamName
     * @param int|null $projectId
     * @return array|null
     */
    protected function getTeamDataForProject($teamName, $projectId = null)
    {
        if (!isset($this->teamRoleMap[$teamName])) {
            return null;
        }
        
        $teamData = $this->teamRoleMap[$teamName];
        
        // Handle multiple projects case
        if (isset($teamData['multiple_projects']) && $teamData['multiple_projects']) {
            if ($projectId && isset($teamData['projects'][$projectId])) {
                return $teamData['projects'][$projectId];
            }
            return null;
        } else {
            // Single project case (backward compatibility)
            if ($projectId && isset($teamData['project_id']) && $teamData['project_id'] == $projectId) {
                return $teamData;
            } elseif (!$projectId) {
                // If no project ID specified, return the single project data
                return $teamData;
            }
            return null;
        }
    }
    
    /**
     * Check if user has permission for specific team and project
     *
     * @param string $permission
     * @param string $teamName
     * @param int $projectId
     * @return bool
     */
    public function hasPermissionForTeamProject($permission, $teamName, $projectId)
    {
        // Admin always has access
        if ($this->isAdmin()) {
            return true;
        }
        
        $teamData = $this->getTeamDataForProject($teamName, $projectId);
        if ($teamData && isset($teamData['permissions'])) {
            return in_array($permission, $teamData['permissions']);
        }
        
        return false;
    }
    
    /**
     * Get all teams and their associated projects for this user
     *
     * @return array
     */
    public function getAllTeamsWithProjects()
    {
        if (!isset($this->teamRoleMap)) {
            return [];
        }
        
        $result = [];
        foreach ($this->teamRoleMap as $teamName => $teamData) {
            if (isset($teamData['multiple_projects']) && $teamData['multiple_projects']) {
                $result[$teamName] = [
                    'projects' => array_keys($teamData['projects']),
                    'roles' => []
                ];
                
                foreach ($teamData['projects'] as $projectId => $projectData) {
                    $result[$teamName]['roles'][$projectId] = $projectData['role_name'];
                }
            } else {
                $result[$teamName] = [
                    'projects' => [$teamData['project_id']],
                    'roles' => [$teamData['project_id'] => $teamData['role_name']]
                ];
            }
        }
        
        return $result;
    }
    
    protected function getCachedAdminStatus()
    {
        return Cache::remember('user_admin_status_' . $this->id, 600, function () {
            return DB::table('user_role')
                    ->where('user_id', $this->id)
                    ->where('role_id', 0)
                    ->exists();
        });
    }

    protected function clearPermissionsCache()
    {
        Cache::forget('user_admin_status_' . $this->id);
        Cache::forget('user_roles_permissions_' . $this->id);
    }
    
    /**
     * Clear all cached permission data for this user
     */
    public function clearAllPermissionCache()
    {
        $this->clearPermissionsCache();
        
        // Clear runtime cached data
        unset($this->teamRoleMap);
        unset($this->cachedPermissions);
        unset($this->currentProject);
        unset($this->currentRoleName);
        unset($this->currentRoleId);
        unset($this->currentTeamName);
        unset($this->queriedRoles);
    }
}
