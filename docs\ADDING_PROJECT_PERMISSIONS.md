# How to Add Project-Level Permission Access Controls

Follow these steps to add new permission access controls (project-level) to the system:

## 1. Add the Permission Key and Description to the Seeder
- Open `database/seeders/PermissionsSeeder.php`.
- Add a new entry to the permissions array with a unique `key` and a `description`.
- **Format:** Permission keys should follow the pattern `function_module` (e.g., `view_tasks`).
- **Note:** The permissions are kept in a specific order for consistency. Please maintain the order as shown in the file.

## 2. Add the Permission to the Database
- Insert the new permission key and description into the `permission` table.
- This can be done manually or by running the seeder.
- Note: There is currently no unique constraint on the key, so avoid duplicates.

## 3. Use the Permission in Blade Views
- To restrict access to a section or feature, use the `@can` directive in your Blade templates:

  ```blade
  @can('permission_key', $project)
      <!-- Content visible only to users with this permission for the project -->
  @endcan
  ```
- **What is `$project`?**
  - The `$project` variable refers to the specific project instance for which you are checking permissions. It is typically a Project model object representing the current project. Passing `$project` to the `@can` directive ensures that permission checks are performed in the context of that particular project, not globally.
- The `@can` directive checks if the current user has the specified permission for the given project context.
- Example:
  ```blade
  @can('view_tasks', $project)
      <div>Task List</div>
  @endcan
  ```

## 4. (Optional) Register the Permission in Gate Logic
- If custom logic is needed, ensure the permission is registered in the application's Gate definitions (see `AppServiceProvider`).

---

**Summary:**
- Add the permission to the seeder and/or database.
- Use the `@can` directive with the permission key and project argument in Blade views to enforce access control.
- Keep permission keys consistent, descriptive, and in the correct order.
