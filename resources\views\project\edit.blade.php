@extends('layouts.app2')

@include('inc.navbar')
@include('inc.style')

@section('content')
@include('inc.title')
    <form action="{{route('projects.update', $project)}}" method="post" class="mt-4">
    @csrf
            
    <div class="mb-3">
        <label for="proj_name_input" class="form-label">Project Title :</label>
        <input type="text" name="proj_name" id="proj_name_input" class="form-control" readonly value="{{$project->proj_name}}" maxlength="100" onkeyup="updateCharCount('proj_name_input', 'name_char_count', 100)">
        <small class="text-muted"><span id="name_char_count">0</span>/100 characters</small>
    </div>
    <div class="mb-3">
        <label for="proj_desc_input" class="form-label">Description :</label>
        <textarea name="proj_desc" id="proj_desc_input" class="form-control @error('proj_desc') is-invalid @enderror" rows="4" maxlength="500" onkeyup="updateCharCount('proj_desc_input', 'desc_char_count', 500)">{{$project->proj_desc}}</textarea>
        <small class="text-muted"><span id="desc_char_count">0</span>/500 characters</small>
        @error('proj_desc')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>
    <div class="mb-3">
        <label for="start_date_input" class="form-label">Start Date :</label>
        <input type="date" name="start_date" id="start_date_input" class="form-control @error('start_date') is-invalid @enderror" value="{{$project->start_date}}">
        @error('start_date')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>
    <div class="mb-3">
        <label for="end_date_input" class="form-label">Completion Date :</label>
        <input type="date" name="end_date" id="end_date_input" class="form-control @error('end_date') is-invalid @enderror" value="{{$project->end_date}}">
        @error('end_date')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>
        
    <button type="submit" class="btn btn-success">Update</button>
    <a href="{{ route('projects.index') }}" class="btn btn-secondary">Cancel</a>
    
    </form>
    
@endsection


<script>
function updateCharCount(inputId, counterId, maxLength) {
    const input = document.getElementById(inputId);
    const counter = document.getElementById(counterId);
    const currentLength = input.value.length;
    counter.textContent = currentLength;
    
    if (currentLength >= maxLength) {
        counter.style.color = 'red';
    } else {
        counter.style.color = '';
    }
}

// Initialize character counts on page load
document.addEventListener('DOMContentLoaded', function() {
    updateCharCount('proj_name_input', 'name_char_count', 100);
    updateCharCount('proj_desc_input', 'desc_char_count', 500);
});
</script>