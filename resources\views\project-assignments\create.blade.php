@extends('layouts.app2')

@include('inc.breadcrumbStyle')

@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header Section with Breadcrumb -->
            <div class="mb-4">
                <div class="d-flex align-items-center">
                    <h1 class="mb-0"><a href="{{ route('project-assignments.index') }}" class="breadcrumb-link">Project Assignments</a></h1>
                    <h1 class="mb-0"><span class="breadcrumb-separator">/</span></h1>
                    <h1 class="mb-0"><a href="{{ route('project-assignments.show', $project->id) }}" class="breadcrumb-link">{{ $project->proj_name }}</a></h1>
                    <h1 class="mb-0"><span class="breadcrumb-separator">/</span></h1>
                    <h1 class="mb-0 breadcrumb-current">Assign Member</h1>
                </div>
                <hr class="my-3" style="border-top: 2px solid #e3e6f0;">
                <div class="mt-2">
                    <h2 class="h3 mb-0">Assign Member to Project</h2>
                    <p class="text-muted">Project: <strong>{{ $project->proj_name }}</strong></p>
                </div>
            </div>

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                Assign Team Member
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('project-assignments.store') }}" method="POST">
                                @csrf
                                <input type="hidden" name="project_id" value="{{ $project->id }}">

                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-1"></i>
                                        Select Team Member
                                    </label>
                                    <select name="username" id="username" class="form-select @error('username') is-invalid @enderror" required>
                                        <option value="">Choose a team member...</option>
                                        @foreach($teamMembers as $member)
                                            <option value="{{ $member->username }}" {{ old('username') == $member->username ? 'selected' : '' }}>
                                                {{ $member->username }} ({{ $member->role_name }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('username')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($teamMembers->count() == 0)
                                        <div class="form-text text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            No available team members to assign to this project.
                                        </div>
                                    @endif
                                </div>

                                <div class="mb-3">
                                    <label for="role_name" class="form-label">
                                        <i class="fas fa-user-tag me-1"></i>
                                        Project Role
                                    </label>
                                    <select name="role_name" id="role_name" class="form-select @error('role_name') is-invalid @enderror" required>
                                        <option value="">Choose a project role...</option>
                                        @foreach($roles as $role)
                                            <option value="{{ $role->role_name }}" {{ old('role_name') == $role->role_name ? 'selected' : '' }}>
                                                {{ $role->role_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('role_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-success" {{ $teamMembers->count() == 0 ? 'disabled' : '' }}>
                                        <i class="fas fa-plus me-1"></i>
                                        Assign to Project
                                    </button>
                                    <a href="{{ route('project-assignments.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Back to Projects
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                Project Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Project Name</small>
                                <div class="fw-bold">{{ $project->proj_name }}</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Team</small>
                                <div class="fw-bold">{{ $project->team_name }}</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Description</small>
                                <div>{{ $project->proj_desc ?: 'No description available' }}</div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Duration</small>
                                <div>
                                    {{ $project->start_date ? \Carbon\Carbon::parse($project->start_date)->format('M d, Y') : 'TBD' }} 
                                    - 
                                    {{ $project->end_date ? \Carbon\Carbon::parse($project->end_date)->format('M d, Y') : 'TBD' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($teamMembers->count() > 0)
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-users me-1"></i>
                                    Available Team Members
                                </h6>
                            </div>
                            <div class="card-body">
                                @foreach($teamMembers as $member)
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <div class="fw-bold">{{ $member->username }}</div>
                                            <small class="text-muted">Team Role: {{ $member->role_name }}</small>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}
</style>
@endsection
