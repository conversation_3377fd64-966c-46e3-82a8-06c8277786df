@extends('layouts/contentNavbarLayout')

@section('content')
    @php
        function generateHue($string) {
            // Create a hash of the string
            $hash = md5($string);
            // Take first 4 characters of hash and convert to number between 0 and 360 (hue)
            // 4 char of hash treated as hex value and converted to decimal
            // decimals modulu by 360 to ensure valid hue in HSL colour format
            return hexdec(substr($hash, 0, 4)) % 360;
        }

        function generateGradient($string) {
            $hue1 = generateHue($string);
            // Create a complementary hue by offsetting by 30-60 degrees
            // Modulo by 360 to ensure within valid hue range
            $hue2 = ($hue1 + 40) % 360;
            
            return "linear-gradient(135deg, 
                hsl({$hue1}, 80%, 65%) 0%, 
                hsl({$hue2}, 80%, 65%) 100%)";
        }
    @endphp

    <div class="container">
        <!-- Header Section -->
        <div class="d-flex align-items-center mb-4">
            <h1 class="me-3 mb-0">All Projects</h1>
            <div class="me-3 text-muted d-flex align-items-center" style="font-size: 1.5rem;">|</div>
            <a href="{{ route('projects.create') }}" class="btn btn-success d-flex align-items-center">
                <i class="bx bx-plus me-1"></i> New Project
            </a>
        </div>

        <!-- Pending Invitations Alert -->
        @if(isset($pendingInvitations) && $pendingInvitations->count() > 0)
            <div class="alert alert-info mb-4">
                <h6 class="alert-heading">Pending Team Invitations</h6>
                <p class="mb-0">You have {{ $pendingInvitations->count() }} pending team invitation(s). Check your email or teams tab to accept or decline them to see team projects.</p>
            </div>
        @endif

        <!-- Projects Grid -->
        <div class="row row-cols-1 row-cols-md-3 g-4">
            @forelse ($projects as $project)
                <div class="col">
                    <a href="{{ route('projects.details', $project->id) }}" class="text-decoration-none">
                        <div class="card h-100 border-0 shadow-sm hover-shadow transition-all">
                            <!-- Dynamic gradient header based on project name -->
                            <div class="card-img-top" style="height: 100px; background: {{ generateGradient($project->proj_name . $project->id) }}"></div>
                            <div class="card-body">
                                <p class="text-muted small mb-1">{{ $project->team_name }}</p>
                                <h5 class="card-title text-dark">{{ $project->proj_name }}</h5>
                                <p class="card-text small text-muted">
                                    <span class="me-1">📅</span> 
                                    {{ \Carbon\Carbon::parse($project->start_date)->format('j M Y') }} → 
                                    {{ \Carbon\Carbon::parse($project->end_date)->format('j M Y') }}
                                </p>
                                <p class="card-text small text-dark">
                                    {{ \Str::limit($project->proj_desc, 166) }}
                                </p>
                            </div>
                        </div>
                    </a>
                </div>
            @empty
                <div class="col-12">
                    <div class="alert alert-info">
                        No projects found. Either you haven't accepted any team invitations yet, or there are no projects in your accepted teams.
                    </div>
                </div>
            @endforelse
        </div>
    </div>

    <style>
        .hover-shadow:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
            transform: translateY(-3px);
        }
        .transition-all {
            transition: all 0.3s ease;
        }
    </style>
@endsection