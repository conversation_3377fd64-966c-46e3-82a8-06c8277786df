<?php

namespace App\Http\Controllers;

use App\SprintArchive;
use App\Sprint;
use App\Project;
use App\Task;
use App\Status;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SprintArchiveController extends Controller
{
    public function index($project_id)
    {
        $project = Project::findOrFail($project_id);
        $archivedSprints = Sprint::where('proj_name', $project->proj_name)
            ->where('active_sprint', 2) // Ended sprints
            ->with('sprintArchive')
            ->get();

        return view('sprint.archives.index', [
            'project' => $project,
            'archivedSprints' => $archivedSprints
        ]);
    }

    public function viewArchive($sprint_id, $view = 'kanban')
    {
        $sprint = Sprint::with('sprintArchive')->findOrFail($sprint_id);
        $project = Project::where('proj_name', $sprint->proj_name)->firstOrFail();
        
        if (!$sprint->sprintArchive) {
            return view('sprint.archives.no-archive', [
                'sprint' => $sprint,
                'project' => $project,
                'message' => 'No archive exists for this sprint. This could be because the sprint was ended before the archiving feature was implemented.'
            ]);
        }

        if ($view === 'kanban') {
            $statuses = Status::where('project_id', $project->id)->get();
            return view('sprint.archives.kanban', [
                'sprint' => $sprint,
                'project' => $project,
                'archive' => $sprint->sprintArchive,
                'statuses' => $statuses
            ]);
        } else {
            // Burndown view
            return view('sprint.archives.burndown', [
                'sprint' => $sprint,
                'project' => $project,
                'archive' => $sprint->sprintArchive
            ]);
        }
    }
} 