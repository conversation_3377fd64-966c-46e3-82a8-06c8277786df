<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Project;
use Illuminate\Support\Facades\Cache;

class LoadUserPermissions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $user = $request->user();
        
        if ($user && !$user->isAdmin()) {
            // We'll preload ALL user role mappings and permissions to avoid multiple DB hits
            $this->preloadUserRolesAndPermissions($user);
            
            // Now also try to get the current project context
            $projectId = $this->extractProjectId($request);
            \Log::debug('LoadUserPermissions: Extracted project ID: ' . ($projectId ?: 'null') . ' from URL: ' . $request->url());
            
            if ($projectId) {
                $this->setCurrentProjectContext($user, $projectId);
            }
        }
        
        return $next($request);
    }
    
    /**
     * Extract project ID from various possible sources in the request
     */
    private function extractProjectId($request)
    {
        // Try from route parameter (common route parameter names)
        $projectId = $request->route('project') ?? $request->route('id');
        if ($projectId) return $projectId;
        
        // Try from query parameters
        $projectId = $request->input('project_id') ?? $request->input('projectId') ?? $request->input('proj_id') ?? $request->input('id');
        if ($projectId) return $projectId;
        
        // Try from route parameters that might contain project
        $project = $request->route()->parameter('project');
        if (is_object($project) && method_exists($project, 'getKey')) {
            return $project->getKey();
        }
        
        // Try other common parameter names
        $project = $request->route()->parameter('id');
        if ($project && is_numeric($project)) {
            return $project;
        }
        
        return null;
    }
    
    /**
     * Preload all user roles and permissions at once to avoid multiple DB hits
     */
    private function preloadUserRolesAndPermissions($user)
    {
        // Use cache for 10 minutes to avoid hitting DB on every request
        $cacheKey = 'user_roles_permissions_' . $user->id;
        
        $roleMap = Cache::remember($cacheKey, 600, function() use ($user) {
            // 1. Get all team mappings for this user (now includes project_id)
            $teamMappings = DB::table('teammappings')
                ->where('username', $user->username)
                ->select('team_name', 'role_name', 'project_id')
                ->get();
            
            // 2. Also get direct user roles (for admin check)
            $directRoles = DB::table('user_role')
                ->where('user_id', $user->id)
                ->select('role_id')
                ->get()
                ->pluck('role_id')
                ->toArray();
            
            // Initialize result with direct roles if any
            $result = [];
            if (!empty($directRoles)) {
                $result['_direct'] = [
                    'role_name' => 'Direct Roles',
                    'role_id' => $directRoles[0], // For admin check we only need the first role
                    'permissions' => []
                ];
            }
              if ($teamMappings->isEmpty() && empty($directRoles)) {
                return $result;
            }
            
            // 3. Process each team mapping to handle project-specific roles
            foreach ($teamMappings as $mapping) {
                $teamName = $mapping->team_name;
                $roleName = $mapping->role_name;
                $projectId = $mapping->project_id;
                
                // Find the role for this specific project and role name
                $role = DB::table('roles')
                    ->where('role_name', $roleName)
                    ->where('project_id', $projectId)
                    ->select('role_id', 'role_name', 'project_id')
                    ->first();
                
                if ($role) {                    // Get permissions for this role
                    $rolePermissions = DB::table('permission_role')
                        ->join('permission', 'permission.id', '=', 'permission_role.permission_id')
                        ->where('permission_role.role_id', $role->role_id)
                        ->pluck('permission.key')
                        ->toArray();
                    
                    // Store project-specific role data
                    if (!isset($result[$teamName])) {
                        $result[$teamName] = [
                            'multiple_projects' => true,
                            'projects' => []
                        ];
                    } elseif (!isset($result[$teamName]['multiple_projects'])) {
                        // Convert existing single project format to multiple projects format
                        $existingData = $result[$teamName];
                        $result[$teamName] = [
                            'multiple_projects' => true,
                            'projects' => [
                                $existingData['project_id'] => $existingData
                            ]
                        ];
                    }
                    
                    $result[$teamName]['projects'][$projectId] = [
                        'role_name' => $role->role_name,
                        'role_id' => $role->role_id,
                        'project_id' => $role->project_id,
                        'permissions' => $rolePermissions
                    ];
                }
            }
            
            return $result;
        });
        
        // Store all team role mappings on the user
        $user->teamRoleMap = $roleMap;
    }
    
    /**
     * Set the current project context on the user
     */
    private function setCurrentProjectContext($user, $projectId)
    {
        try {
            // Get minimal project data - just the ID and team_name
            $project = DB::table('projects')
                ->where('id', $projectId)
                ->select('id', 'team_name')
                ->first();
              if ($project) {
                \Log::debug('LoadUserPermissions: Found project ' . $project->id . ' with team ' . $project->team_name);
                
                $user->currentProject = (object)[
                    'id' => $project->id,
                    'team_name' => $project->team_name
                ];
                
                // Set permissions if we have them for this team
                $teamName = $project->team_name;
                \Log::debug('LoadUserPermissions: Looking for team role map for team: ' . $teamName);
                \Log::debug('LoadUserPermissions: Available teams in role map: ' . json_encode(array_keys($user->teamRoleMap ?? [])));
                
                if (isset($user->teamRoleMap[$teamName])) {
                    $teamData = $user->teamRoleMap[$teamName];
                    \Log::debug('LoadUserPermissions: Found team data structure: ' . (isset($teamData['multiple_projects']) ? 'multiple_projects' : 'single_project'));
                    
                    // Handle multiple projects case
                    if (isset($teamData['multiple_projects']) && $teamData['multiple_projects']) {
                        // Find the role for this specific project
                        if (isset($teamData['projects'][$project->id])) {
                            $roleData = $teamData['projects'][$project->id];
                            $user->currentRoleName = $roleData['role_name'];
                            $user->currentRoleId = $roleData['role_id'];
                            $user->currentTeamName = $teamName;
                            $user->cachedPermissions = $roleData['permissions'];
                            
                            \Log::debug('LoadUserPermissions: Set permissions for user ' . $user->name . ' in team ' . $teamName . ' for project ' . $project->id . ': ' . json_encode($roleData['permissions']));
                        } else {
                            \Log::debug('LoadUserPermissions: No role found for project ' . $project->id . ' in team ' . $teamName);
                        }
                    } else {
                        // Single project case (backward compatibility)
                        if (isset($teamData['project_id']) && $teamData['project_id'] == $project->id) {
                            $user->currentRoleName = $teamData['role_name'];
                            $user->currentRoleId = $teamData['role_id'];
                            $user->currentTeamName = $teamName;
                            $user->cachedPermissions = $teamData['permissions'];
                            
                            \Log::debug('LoadUserPermissions: Set permissions for user ' . $user->name . ' in team ' . $teamName . ': ' . json_encode($teamData['permissions']));
                        } else {
                            \Log::debug('LoadUserPermissions: Project ID mismatch - role project_id: ' . ($teamData['project_id'] ?? 'null') . ', current project: ' . $project->id);
                        }
                    }
                } else {
                    \Log::debug('LoadUserPermissions: No team role map found for team: ' . $teamName);
                }
            } else {
                \Log::debug('LoadUserPermissions: No project found for ID: ' . $projectId);
            }
        } catch (\Exception $e) {
            Log::error('Failed to set project context: ' . $e->getMessage());
        }
    }
}
