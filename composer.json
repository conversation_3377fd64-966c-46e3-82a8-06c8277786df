{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "doctrine/dbal": "^4.0", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0", "jimmyjs/laravel-report-generator": "^2.2", "khill/lavacharts": "^3.1", "laravel/framework": "^9.0", "laravel/passport": "^10.4.2", "laravel/tinker": "^2.8", "laravel/ui": "*", "maatwebsite/excel": "^3.1", "phpoffice/phpspreadsheet": "^1.16", "twilio/sdk": "^7.13"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "spatie/laravel-ignition": "^1.0", "icanhazstring/composer-unused": "^0.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.25", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^8.5"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump"], "post-install-cmd": ["@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}