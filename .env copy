APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:Luk2Fb8R3hkZ1H8Yo4jJNBI3fhEaDLPubFNe6QlPsp4=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack

DB_CONNECTION=
DB_HOST=
DB_PORT=
DB_USERNAME=
DB_PASSWORD=
DB_DATABASE=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file 
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.testmail.app
MAIL_PORT=2525
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

CURLOPT_USERPWD = '<the-jira-user>:<the-jira-user-Password>'
JIRA_API_URL = '<path-to-your-project>/rest/api/latest/'
JIRA_AGILE_URL = '<path-to-your-project>/rest/agile/latest/'
RESOURCES_SEARCH = 'search'
RESOURCES_FILTER = 'filter'
STORYPOINT_FIELD = 'customfield_<id>'

STUDENT_PORTAL_URL =
STUDENT_PORTAL_API_KEY = 

MOCK_PORTAL_URL=
MOCK_PORTAL_KEY=
MOCK_PORTAL_CLIENT_ID=