# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy PHP app to Azure Web App - sagile

on:
  push:
    branches:
      - Overhaul
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'

      - name: Check DB connection (optional debug)
        run: |
          echo "<?php
          try {
              \$pdo = new PDO(
                  'mysql:host=${{ secrets.DB_HOST }};dbname=${{ secrets.DB_DATABASE }};port=3306',
                  '${{ secrets.DB_USERNAME }}',
                  '${{ secrets.DB_PASSWORD }}'
              );
              echo 'Connection successful';
          } catch (Exception \$e) {
              echo 'Connection failed: ' . \$e->getMessage();
          }" > test-db.php
      
          php test-db.php

      - name: Check if composer.json exists
        id: check_files
        uses: andstor/file-existence-action@v1
        with:
          files: 'composer.json'

      - name: Run composer install if composer.json exists
        if: steps.check_files.outputs.files_exists == 'true'
        run: composer validate --no-check-publish && composer install --prefer-dist --no-progress

      - name: Zip artifact for deployment
        run: zip release.zip ./* -r

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: php-app
          path: release.zip

  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'Production'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}
    permissions:
      id-token: write #This is required for requesting the JWT
      contents: read #This is required for actions/checkout

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: php-app

      - name: Unzip artifact for deployment
        run: unzip release.zip
      
      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_93CFEAA5F54F494883F69B975B38BFC1 }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_933F7CF849FA421CB2F4B2C2A115CC06 }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_408D2775FD534C48BD0373D56D3EF5F4 }}

      - name: 'Deploy to Azure Web App'
        uses: azure/webapps-deploy@v3
        id: deploy-to-webapp
        with:
          app-name: 'sagile'
          slot-name: 'Production'
          package: .
          
