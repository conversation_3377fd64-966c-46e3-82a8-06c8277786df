<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit User Story</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- FontAwesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: none;
            border-radius: 0.5rem;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 1rem;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .form-check-input {
            cursor: pointer;
        }
        
        .form-check {
            margin-bottom: 0.5rem;
        }
        
        .text-danger {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Edit User Story</h4>
                        
                    </div>
                    <div class="card-body">
                        <form action="{{route('userstory.update', $userstory)}}" method="post">
                            @csrf

                            <div class="mb-3">
                                <label for="user_story" class="form-label">User Story</label>
                                <input type="text" name="user_story" id="user_story" readonly value="{{$userstory->user_story}}" class="form-control">
                                @error('user_story')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="status_id" class="form-label">Status</label>
                                <select name="status_id" id="status_id" class="form-select">
                                    <option value="" selected disabled>Select</option>
                                    @foreach($statuses as $status)
                                        <option value="{{ $status->id }}" @if($userstory->title == $status->id) selected @endif>{{ $status->title }}</option>
                                    @endforeach
                                </select>
                                @error('status_id')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="user_names" class="form-label">Assigned to</label>
                                <select name="user_names[]" id="user_names" class="form-select" multiple>
                                    @foreach($teamlist as $teammember)
                                        <option value="{{ $teammember['username'] }}" {{ (old('user_names') && in_array($teammember['username'], old('user_names')) ? 'selected' : '') }}>
                                            {{ $teammember['username'] }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <h5>General NFRs and Their Specific Requirements</h5>
                                <div class="row">
                                    @foreach($generalNFRIds as $index => $generalNFR)
                                        <div class="col-md-6 mb-3">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">{{ $generalNFRNames[$index] }}</h6>
                                                </div>
                                                <div class="card-body">
                                                    @if(isset($specificNFRs[$generalNFR]) && isset($specificNFRIds[$generalNFR]))
                                                        @foreach($specificNFRs[$generalNFR] as $specificIndex => $specificNFR)
                                                            <div class="form-check mb-2">
                                                                <input class="form-check-input" type="checkbox" 
                                                                    name="selected_nfrs[{{ $generalNFR }}][]" 
                                                                    id="nfr_{{ $generalNFR }}_{{ $specificIndex }}" 
                                                                    value="{{ $specificNFRIds[$generalNFR][$specificIndex] }}"
                                                                    @if(in_array($specificNFRIds[$generalNFR][$specificIndex], $selectedSpecificNFRIds->toArray())) checked @endif>
                                                                <label class="form-check-label" for="nfr_{{ $generalNFR }}_{{ $specificIndex }}">
                                                                    {{ $specificNFR }}
                                                                </label>
                                                            </div>
                                                        @endforeach
                                                    @else
                                                        <p class="text-muted mb-0">No specific requirements available.</p>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            
                            <div class="mt-4 d-flex justify-content-between">
                                <a href="{{ url()->previous() }}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i> Update
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Function to notify parent iframe about height changes (for iframe compatibility)
        function notifyParentAboutHeight() {
            const height = Math.max(
                document.body.scrollHeight,
                document.documentElement.scrollHeight,
                document.body.offsetHeight,
                document.documentElement.offsetHeight,
                document.body.clientHeight,
                document.documentElement.clientHeight
            );
            
            // Add small buffer to avoid scrollbars
            const heightWithBuffer = height + 30;
            
            // Send message to parent
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'resize',
                    height: heightWithBuffer,
                    iframeId: 'userstory-iframe'
                }, '*');
            }
        }
        
        // Set up event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Initial height notification
            notifyParentAboutHeight();
            
            // Set up mutation observer to detect content changes
            const observer = new MutationObserver(function() {
                notifyParentAboutHeight();
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });
            
            // Additional triggers for height recalculation
            window.addEventListener('load', notifyParentAboutHeight);
            window.addEventListener('resize', notifyParentAboutHeight);
        });
    </script>
</body>
</html>
