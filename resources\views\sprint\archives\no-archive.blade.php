<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>No Archive - {{ $sprint->sprint_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
        }
        .no-archive-container {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }
        .no-archive-icon {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 20px;
        }
        .sprint-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="no-archive-container">
            <i class="fas fa-archive no-archive-icon"></i>
            <h3>No Archive Available</h3>
            <p class="text-muted">{{ $message }}</p>
            
            <div class="sprint-info">
                <h5>Sprint Details:</h5>
                <p><strong>Name:</strong> {{ $sprint->sprint_name }}</p>
                <p><strong>Period:</strong> {{ \Carbon\Carbon::parse($sprint->start_sprint)->format('j M Y') }} - {{ \Carbon\Carbon::parse($sprint->end_sprint)->format('j M Y') }}</p>
                <p><strong>Status:</strong> Ended</p>
            </div>

            <div class="mt-4">
                <a href="{{ route('sprint.archives', ['proj_id' => $project->id]) }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Archives
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 