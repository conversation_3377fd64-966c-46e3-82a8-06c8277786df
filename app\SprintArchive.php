<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class SprintArchive extends Model
{
    protected $fillable = [
        'sprint_id',
        'kanban_state',
        'burndown_data',
        'archived_at'
    ];

    protected $casts = [
        'kanban_state' => 'array',
        'burndown_data' => 'array',
        'archived_at' => 'datetime'
    ];

    public function sprint()
    {
        return $this->belongsTo(Sprint::class, 'sprint_id', 'sprint_id');
    }
} 