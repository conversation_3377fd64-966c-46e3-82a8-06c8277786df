<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Backlog</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- FontAwesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
        }
        
        .user-story {
            background-color: #f9f9f9;
            border-radius: 0.25rem;
            padding: 0.75rem;
        }
        
        .btn-link {
            color: #333;
            text-decoration: none;
        }
        
        hr {
            margin: 1.5rem 0;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: none;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 1rem;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .form-check-input {
            cursor: pointer;
        }
        
        .selected-count {
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            padding: 0.5rem;
            text-align: center;
            font-weight: 500;
            border: 1px solid #dee2e6;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
            padding: 0.75rem 1.25rem;
            margin-bottom: 1rem;
            border-radius: 0.25rem;
        }
    </style>
</head>
<body>
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif
    
    <div class="container-fluid">
        <div class="row">
            <!-- User Stories Panel -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Project Backlog: {{ $project->proj_name }}</h4>
                    </div>
                    <div class="card-body">
                        @if(isset($activeSprint))
                            <div class="alert alert-info mb-3">
                                <p class="mb-0"><strong>Note:</strong> Only showing user stories and tasks that are not in the active sprint <strong>{{ $activeSprint->sprint_name }}</strong> and are not marked as "Done". 
                                User stories marked as <strong>Partially in sprint</strong> have some tasks already in the active sprint.</p>
                            </div>
                        @else
                            <div class="alert alert-info mb-3">
                                <p class="mb-0"><strong>Note:</strong> Only showing user stories and tasks that are not marked as "Done".</p>
                            </div>
                        @endif
                        
                        @if($userStories->isEmpty())
                            <div class="alert alert-info">
                                @if(isset($activeSprint))
                                    All user stories and their tasks are either in the active sprint "{{ $activeSprint->sprint_name }}" or marked as "Done".
                                @else
                                    No user stories found in the backlog, or all user stories are marked as "Done".
                                @endif
                            </div>
                        @else
                            <form id="backlogForm" action="{{ isset($activeSprint) ? route('sprint.addItems', ['sprint_id' => $activeSprint->sprint_id]) : route('sprints.store') }}" method="POST">
                                @csrf
                                <input type="hidden" name="proj_name" value="{{ $project->proj_name }}" form="backlogForm">
                                @foreach($userStories as $userStory)
                                    <!-- User Story -->
                                    <div class="user-story mb-4">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <button class="btn btn-sm btn-link p-0 me-2" type="button" data-bs-toggle="collapse" data-bs-target="#userStory{{ $userStory->u_id }}Tasks">
                                                    <i class="fas fa-chevron-down"></i>
                                                </button>
                                                @can('addToSprint_backlog', $project)
                                                    <input type="checkbox" class="form-check-input me-2 userStoryCheckbox" name="selected_user_stories[]" value="{{ $userStory->u_id }}" data-userstory-id="{{ $userStory->u_id }}">
                                                @endcan
                                                <span class="fw-bold">{{ $userStory->user_story }}</span>
                                                @if(isset($activeSprint) && $userStory->sprint_id == $activeSprint->sprint_id)
                                                    <span class="ms-2 badge bg-info">Partially in sprint</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="collapse show" id="userStory{{ $userStory->u_id }}Tasks">
                                            <div class="ps-4 mb-2">
                                                @if(isset($tasksByUserStory[$userStory->u_id]) && count($tasksByUserStory[$userStory->u_id]) > 0)
                                                    @foreach($tasksByUserStory[$userStory->u_id] as $task)
                                                        <div class="d-flex align-items-center mb-2">
                                                        @can('addToSprint_backlog', $project)
                                                            <input type="checkbox" class="form-check-input me-2 taskCheckbox" name="selected_tasks[]" value="{{ $task->id }}" data-userstory-id="{{ $userStory->u_id }}" {{ $task->status_id == 'completed' ? 'checked' : '' }}>
                                                        @endcan
                                                            <span>{{ $task->title }}</span>
                                                            @if(isset($activeSprint))
                                                                <span class="ms-2 badge bg-secondary">Not in sprint</span>
                                                            @endif
                                                        </div>
                                                    @endforeach
                                                @else
                                                    <div class="text-muted">
                                                        @if(isset($activeSprint))
                                                            All tasks are either in the active sprint, marked as "done", or no tasks exist
                                                        @else
                                                            No tasks assigned to this user story or all tasks are marked as "done"
                                                        @endif
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    
                                    @if(!$loop->last)
                                        <hr>
                                    @endif
                                @endforeach
                        @endif
                        
                        <!-- Add New User Story Button -->
                        <div class="text-center mt-4">
                            @can('addUserStory_backlog', $project)
                            <a href="{{ route('backlog.create', ['proj_id' => $project->id]) }}" class="btn btn-success px-4">
                                <i class="fas fa-plus me-2"></i> New User Story
                            </a>
                            @endcan
                        </div>
                        
                        @if(!$userStories->isEmpty())
                            </form>
                        @endif
                    </div>
                </div>
            </div>
            
            @can('beginSprint_backlog', $project)
            <!-- Sprint Planner or Add to Sprint Panel -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        @if(isset($activeSprint))
                            <!-- Add to Current Sprint Panel -->
                            <h4 class="card-title mb-4">Add to Current Sprint</h4>
                            <div class="mb-3">
                                <p><strong>Active Sprint:</strong> {{ $activeSprint->sprint_name }}</p>
                                <p><strong>Duration:</strong> 
                                    {{ \Carbon\Carbon::parse($activeSprint->start_sprint)->format('d.m.Y') }} - 
                                    {{ \Carbon\Carbon::parse($activeSprint->end_sprint)->format('d.m.Y') }}
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <p class="selected-count"><span id="selectedUserStoryCount">0</span> User Stories selected, <span id="selectedTaskCount">0</span> Tasks selected</p>
                            </div>
                            
                            <!-- Add to Sprint Button -->
                            <div class="d-grid gap-2 mt-4">
                                @can('addToSprint_backlog', $project)
                                <button type="submit" form="backlogForm" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i> Add to Sprint
                                </button>
                                @endcan
                                @can('endSprint_backlog', $project)
                                <a href="{{ route('sprints.endSprint', ['sprint' => $activeSprint->sprint_id]) }}" class="btn btn-warning" onclick="return confirm('Are you sure you want to end this sprint? Any incomplete tasks will be moved back to the backlog.')">
                                    <i class="fas fa-flag-checkered me-2"></i> End Sprint
                                </a>
                                @endcan
                            </div>
                        @else
                            <!-- Sprint Planner Panel -->
                            <h4 class="card-title mb-4">Sprint Planner</h4>
                            
                            @if ($errors->any())
                                <div class="alert alert-danger mb-4">
                                    <ul class="mb-0">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                            
                            <div class="mb-3">
                                <p class="selected-count"><span id="selectedUserStoryCount">0</span> User Stories selected, <span id="selectedTaskCount">0</span> Tasks selected</p>
                            </div>
                            
                            <!-- Date Selection -->
                            <div class="mb-3">
                                <label for="startDate" class="form-label">Start Date:</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="startDate" name="start_sprint" form="backlogForm" value="{{ now()->format('Y-m-d') }}">
                                </div>
                                @error('start_sprint')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-4">
                                <label for="endDate" class="form-label">End Date:</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="endDate" name="end_sprint" form="backlogForm" value="{{ now()->addDays(14)->format('Y-m-d') }}">
                                </div>
                                @error('end_sprint')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Additional Sprint Fields -->
                            <div class="mb-3">
                                <label for="sprintName" class="form-label">Sprint Name:</label>
                                <input type="text" class="form-control" id="sprintName" name="sprint_name" form="backlogForm" required maxlength="100">

                                @include('inc.character-counter', 
                                [ 'inputId' => 'sprintName', 
                                        'counterId' => 'sprint_name_char_count', 
                                        'maxLength' => 100])
                        
                                @error('sprint_name')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label for="sprintDesc" class="form-label">Description:</label>
                                <textarea class="form-control" id="sprintDesc" name="sprint_desc" form="backlogForm" rows="3" required maxlength="300"></textarea>
                                @include('inc.character-counter', 
                                [ 'inputId' => 'sprintDesc', 
                                        'counterId' => 'sprint_desc_char_count', 
                                        'maxLength' => 300])
                                @error('sprint_desc')
                                    <div class="text-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Begin Sprint Button -->
                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" form="backlogForm" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i> Begin Sprint
                                </button>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            @endcan
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle user story collapse functionality
            const toggleButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const icon = this.querySelector('i');
                    if (icon.classList.contains('fa-chevron-down')) {
                        icon.classList.replace('fa-chevron-down', 'fa-chevron-right');
                    } else {
                        icon.classList.replace('fa-chevron-right', 'fa-chevron-down');
                    }
                    
                    // After toggling, notify parent about height change
                    setTimeout(notifyParentAboutHeight, 300);
                });
            });
            
            // Function to notify parent iframe about height changes
            function notifyParentAboutHeight() {
                const height = Math.max(
                    document.body.scrollHeight,
                    document.documentElement.scrollHeight,
                    document.body.offsetHeight,
                    document.documentElement.offsetHeight,
                    document.body.clientHeight,
                    document.documentElement.clientHeight
                );
                
                // Add small buffer to avoid scrollbars
                const heightWithBuffer = height + 30;
                
                // Send message to parent
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'resize',
                        height: heightWithBuffer,
                        iframeId: 'backlog-iframe'
                    }, '*');
                }
            }
            
            // Initial height notification
            notifyParentAboutHeight();
            
            // Set up mutation observer to detect content changes
            const observer = new MutationObserver(function() {
                notifyParentAboutHeight();
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });
            
            // Additional triggers for height recalculation
            window.addEventListener('load', notifyParentAboutHeight);
            window.addEventListener('resize', notifyParentAboutHeight);
            
            // Count selected items functionality
            const userStoryCheckboxes = document.querySelectorAll('.userStoryCheckbox');
            const taskCheckboxes = document.querySelectorAll('.taskCheckbox');
            const selectedUserStoryCountElement = document.getElementById('selectedUserStoryCount');
            const selectedTaskCountElement = document.getElementById('selectedTaskCount');
            
            function updateSelectedCounts() {
                const selectedUserStories = document.querySelectorAll('.userStoryCheckbox:checked').length;
                const selectedTasks = document.querySelectorAll('.taskCheckbox:checked').length;
                
                if(selectedUserStoryCountElement) selectedUserStoryCountElement.textContent = selectedUserStories;
                if(selectedTaskCountElement) selectedTaskCountElement.textContent = selectedTasks;
            }
            
            // Add event listeners to checkboxes
            userStoryCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const userStoryId = this.getAttribute('data-userstory-id');
                    const relatedTaskCheckboxes = document.querySelectorAll(`.taskCheckbox[data-userstory-id="${userStoryId}"]`);
                    
                    // When user story is checked, check all its tasks
                    relatedTaskCheckboxes.forEach(taskCheckbox => {
                        taskCheckbox.checked = this.checked;
                    });
                    
                    updateSelectedCounts();
                });
            });
            
            taskCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedCounts);
            });
            
            // Initialize counts
            updateSelectedCounts();
        });
    </script>
</body>
</html>