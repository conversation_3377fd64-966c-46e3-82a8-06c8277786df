<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Sprint extends Model
{
    protected $table = 'sprint';

    protected $fillable = ['sprint_name','proj_name','sprint_desc','start_sprint','end_sprint','users_name'];

    public $primaryKey = 'sprint_id';


    public function user()
    {
        return $this->belongsTo(User::class, 'username', 'users_name');
    }
    public function users()
    {
        return $this->belongsToMany(User::class)->withPivot('sprint_access');
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function userAccess()
    {
        return $this->hasMany(UserAccess::class);
    }
    public function userStories()
    {
        return $this->hasMany(UserStory::class, 'sprint_id');
    }

    public function sprintArchive()
    {
        return $this->hasOne(SprintArchive::class, 'sprint_id', 'sprint_id');
    }

    public static function getSprintById($sprint_id)
    {
        return self::where('sprint_id', $sprint_id)->firstOrFail();
    }
    public function tasks()
    {
        return $this->hasMany(Task::class, 'sprint_id');
    }

    public static function getSprintsByProjectId($projId)
    {
        return self::whereHas('userStories', function ($query) use ($projId) {
            $query->where('proj_id', $projId);
        })->distinct()->pluck('sprint_name');
    }

    public static function getSprintsByProjectName($projName)
    {
        return self::where('proj_name', $projName)->get();
    }

    public static function getFilteredSprints($isAdmin, $projectFilter, $projects)
    {
        $query = self::query();

        if ($isAdmin) {
            // For admin, optionally filter by selected project name
            return $query
                ->when($projectFilter, function ($q) {
                    $project = Project::find(request('project_filter'));
                    return $project ? $q->where('proj_name', $project->proj_name) : $q;
                })
                ->get();
        }

        // For non-admin: restrict to accessible projects
        $projectNames = $projects->pluck('proj_name')->toArray();

        return $query
            ->whereIn('proj_name', $projectNames)
            ->when($projectFilter, function ($q) {
                $project = Project::find(request('project_filter'));
                return $project ? $q->where('proj_name', $project->proj_name) : $q;
            })
            ->get();
    }
   
}
