<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archived Burndown - {{ $sprint->sprint_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
        }
        .archive-header {
            background: #fff;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .archive-info {
            color: #666;
            font-size: 0.9rem;
        }
        .chart-container {
            background: #fff;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="archive-header">
            <h2 class="mb-2">{{ $sprint->sprint_name }} - Archived Burndown Chart</h2>
            <div class="archive-info">
                <p class="mb-1">
                    <i class="fas fa-calendar"></i> 
                    Sprint Period: {{ \Carbon\Carbon::parse($sprint->start_sprint)->format('j M Y') }} - 
                    {{ \Carbon\Carbon::parse($sprint->end_sprint)->format('j M Y') }}
                </p>
                <p class="mb-1">
                    <i class="fas fa-archive"></i>
                    Archived on: {{ $archive->archived_at->format('j M Y, H:i') }}
                </p>
            </div>
        </div>

        <div class="chart-container">
            <div id="burndownChart" style="height: 400px;"></div>
        </div>

        <div class="mt-4">
            <a href="{{ route('sprint.archives', ['proj_id' => $project->id]) }}" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i>
                Back to Archives
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        google.charts.load('current', {'packages':['corechart']});
        google.charts.setOnLoadCallback(drawChart);

        function drawChart() {
            var data = google.visualization.arrayToDataTable([
                ['Day', 'Ideal Work', 'Actual Work'],
                @foreach($archive->burndown_data['idealData'] as $key => $value)
                    [{{ $key }}, {{ $value }}, {{ $archive->burndown_data['actualData'][$key] ?? 'null' }}],
                @endforeach
            ]);

            var options = {
                title: 'Sprint Burndown Progress',
                titleTextStyle: { 
                    fontSize: 18,
                    bold: true
                },
                curveType: 'none',
                legend: { 
                    position: 'bottom',
                    textStyle: { fontSize: 12 }
                },
                hAxis: {
                    title: 'Days',
                    titleTextStyle: { fontSize: 12 },
                    viewWindow: { min: 0 }
                },
                vAxis: {
                    title: 'Remaining Work',
                    titleTextStyle: { fontSize: 12 },
                    minValue: 0
                },
                chartArea: {
                    width: '85%',
                    height: '75%'
                },
                series: {
                    0: { lineDashStyle: [5, 5], color: '#4e73df' }, // Ideal line
                    1: { color: '#1cc88a' } // Actual line
                }
            };

            var chart = new google.visualization.LineChart(document.getElementById('burndownChart'));
            chart.draw(data, options);

            // Handle window resize
            window.addEventListener('resize', function() {
                chart.draw(data, options);
            });
        }
    </script>
</body>
</html> 