<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use App\Http\Team;
use App\Observers\Notifier;
use App\Http\User;
use App\Permission;
use App\Project;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //check that app is local
        if ($this->app->isLocal()) {
            //if local register your services you require for development
        } else {
            //else register your services you require for production
            $this->app['request']->server->set('HTTPS', true);
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);

        if ($this->app->environment('production')) {
            \URL::forceScheme('https');
        } else{
            \URL::forceScheme('http');
        }

    // Defer DB queries until app is fully booted
        $this->app->booted(function () {
            try {
                // Load all permissions in a single query
                $permissions = \App\Permission::all();
                  // Register gates for all permissions at once
                foreach ($permissions as $permission) {
                    Gate::define($permission->key, function ($user, \App\Project $project) use ($permission) {
                        \Log::debug('Gate check for permission: ' . $permission->key . ' on project: ' . $project->id . ' (' . $project->proj_name . ') for user: ' . $user->name);
                        
                        // Admin always has access
                        if ($user->isAdmin()) {
                            \Log::debug('User is admin - access granted');
                            return true;
                        }
                        
                          // High-performance path: use our preloaded team role map
                        $teamName = $project->team_name;

                        \Log::debug('TeamName for user ' . $user->name . ' in team ' . $teamName . ': ' . ($teamName ?: 'null'));
                        
                          // If current project matches the context we want
                        if (isset($user->currentProject) && 
                            isset($user->currentProject->team_name) && 
                            $user->currentProject->team_name === $teamName && 
                            isset($user->cachedPermissions)) {
                            \Log::debug('Using cached permissions for team ' . $teamName . ': ' . json_encode($user->cachedPermissions));
                            $hasPermission = in_array($permission->key, $user->cachedPermissions);
                            \Log::debug('Cached permission check result: ' . ($hasPermission ? 'granted' : 'denied'));
                            return $hasPermission;
                        }// If we have team role map but not for current project
                        if (isset($user->teamRoleMap) && isset($user->teamRoleMap[$teamName])) {
                            \Log::debug('Using team role map for team ' . $teamName);
                            $teamData = $user->teamRoleMap[$teamName];
                            
                            // Handle multiple projects case
                            if (isset($teamData['multiple_projects']) && $teamData['multiple_projects']) {
                                // Check if we have permissions for this specific project
                                if (isset($teamData['projects'][$project->id])) {
                                    $projectRoleData = $teamData['projects'][$project->id];
                                    return in_array($permission->key, $projectRoleData['permissions']);
                                }
                                return false; // No role for this specific project
                            } else {
                                // Single project case - ensure this role belongs to the current project
                                if (isset($teamData['project_id']) && $teamData['project_id'] == $project->id) {
                                    return in_array($permission->key, $teamData['permissions']);
                                }
                                return false; // Role doesn't belong to this project
                            }
                        }// Last resort fallback - but this should rarely happen with our middleware
                        \Log::debug('Fallback DB query for user ' . $user->name . ' in team ' . $teamName);
                        
                        // Query team mapping for this specific project
                        $teamMapping = DB::table('teammappings')
                            ->where('username', $user->username)
                            ->where('team_name', $teamName)
                            ->where('project_id', $project->id)
                            ->select('role_name')
                            ->first();
                        
                        \Log::debug('TeamMapping result: ' . ($teamMapping ? json_encode($teamMapping) : 'null'));
                            
                        if (!$teamMapping) return false;
                        
                        $roleName = $teamMapping->role_name;
                        
                        // Cache this result on the user to avoid duplicate queries
                        $queriedRoles = $user->queriedRoles ?? [];
                        
                        // Create a cache key that includes project context for project-specific roles
                        $cacheKey = $roleName . '_' . $project->id;
                        
                        if (!isset($queriedRoles[$cacheKey])) {
                            // Find the role for this specific project
                            $role = DB::table('roles')
                                ->where('role_name', $roleName)
                                ->where('project_id', $project->id)
                                ->select('role_id')
                                ->first();
                                
                            \Log::debug('Role for project ' . $project->id . ': ' . ($role ? $role->role_id : 'null'));
                            
                            if (!$role) return false;
                            
                            $rolePermissions = DB::table('permission_role')
                                ->join('permission', 'permission.id', '=', 'permission_role.permission_id')
                                ->where('permission_role.role_id', $role->role_id)
                                ->pluck('permission.key')
                                ->toArray();
                            
                            \Log::debug('Loaded ' . count($rolePermissions) . ' permissions from DB for role ' . $role->role_id);
                                
                            $queriedRoles[$cacheKey] = $rolePermissions;
                            $user->queriedRoles = $queriedRoles;
                        } else {
                            // Ensure we have the latest queriedRoles data
                            $queriedRoles = $user->queriedRoles;
                        }
                        
                        $hasPermission = in_array($permission->key, $queriedRoles[$cacheKey]);
                        \Log::debug('Permission check for ' . $permission->key . ': ' . ($hasPermission ? 'granted' : 'denied'));
                        
                        return $hasPermission;
                    });
                }
            } catch (\Exception $e) {
                \Log::error('Failed to register gates: ' . $e->getMessage());
            }
        });
    }

}
