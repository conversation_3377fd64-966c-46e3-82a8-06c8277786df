<?php

namespace App\Http\Controllers;

use App\User;
use App\Team;
use App\Role;
use App\Project;
use App\TeamMapping;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ProjectAssignmentController extends Controller
{
    /**
     * Display the project assignments page
     */
    public function index()
    {
        $user = \Auth::user();
        
        // Get teams the user is part of (team-only memberships)
        $userTeams = TeamMapping::teamOnly()
            ->where('username', $user->username)
            ->where('invitation_status', 'accepted')
            ->pluck('team_name');

        // Get projects for those teams
        $projects = Project::whereIn('team_name', $userTeams)->get();

        // Get all project assignments
        $projectAssignments = TeamMapping::projectAssignments()
            ->with(['user', 'project', 'team'])
            ->get()
            ->groupBy('project_id');

        // Check which teams the current user can manage
        $managedTeams = collect();
        if ($user->isAdmin()) {
            // <PERSON>min can manage all teams
            $managedTeams = $userTeams;
        } else {
            // Get teams where user is Team Manager
            $managedTeams = TeamMapping::teamOnly()
                ->where('username', $user->username)
                ->where('role_name', 'Team Manager')
                ->where('invitation_status', 'accepted')
                ->pluck('team_name');
        }

        return view('project-assignments.index', compact('projects', 'projectAssignments', 'managedTeams'));
    }

    /**
     * Show the form for creating a new project assignment
     */
    public function create(Request $request)
    {
        $projectId = $request->input('project_id');
        $project = Project::findOrFail($projectId);
        
        // Check if current user can manage assignments for this team
        if (!$this->canManageProjectAssignments($project->team_name)) {
            return redirect()->route('project-assignments.index')
                ->with('error', 'You do not have permission to assign members to this project. Only Team Managers can manage project assignments.');
        }
        
        // Get team members who are not yet assigned to this project
        $teamMembers = TeamMapping::teamOnly()
            ->where('team_name', $project->team_name)
            ->where('invitation_status', 'accepted')
            ->whereNotIn('username', function($query) use ($projectId) {
                $query->select('username')
                      ->from('teammappings')
                      ->where('project_id', $projectId);
            })
            ->get();
        // Get only project-specific roles for this project (not global roles)
        $roles = Role::where('project_id', $projectId)->get();

        return view('project-assignments.create', compact('project', 'teamMembers', 'roles'));
    }

    /**
     * Store a new project assignment
     */
    public function store(Request $request)
    {
        $request->validate([
            'project_id' => 'required|exists:projects,id',
            'username' => 'required|string',
            'role_name' => 'required|string',
        ]);

        $project = Project::findOrFail($request->project_id);
        
        // Check if current user can manage assignments for this team
        if (!$this->canManageProjectAssignments($project->team_name)) {
            return back()->with('error', 'You do not have permission to assign members to this project. Only Team Managers can manage project assignments.');
        }

        // Get the original team mapping (team-only membership)
        $originalMapping = TeamMapping::teamOnly()
            ->where('username', $request->username)
            ->where('team_name', function($query) use ($request) {
                $query->select('team_name')
                      ->from('projects')
                      ->where('id', $request->project_id);
            })
            ->first();

        if (!$originalMapping) {
            return back()->with('error', 'User is not a member of the project team.');
        }

        // Check if already assigned to this project
        $existingAssignment = TeamMapping::projectAssignments()
            ->where('username', $request->username)
            ->where('project_id', $request->project_id)
            ->first();

        if ($existingAssignment) {
            return back()->with('error', 'User is already assigned to this project.');
        }

        // Create new project assignment entry
        TeamMapping::create([
            'username' => $request->username,
            'role_name' => $request->role_name,
            'team_name' => $originalMapping->team_name,
            'project_id' => $request->project_id,
            'invitation_status' => 'accepted', // Automatically accepted since they're already team members
            'invitation_token' => null,
        ]);

        // Clear cache for the assigned user
        $this->clearUserPermissionsCache($request->username);

        return redirect()->route('project-assignments.show', $request->project_id)
            ->with('success', 'Team member successfully assigned to project.');
    }

    /**
     * Show project assignments for a specific project
     */
    public function show($projectId)
    {
        $project = Project::findOrFail($projectId);
        
        $projectAssignments = TeamMapping::projectAssignments()
            ->where('project_id', $projectId)
            ->with(['user'])
            ->get();

        // Check if current user can manage assignments for this team
        $canManageAssignments = $this->canManageProjectAssignments($project->team_name);

        return view('project-assignments.show', compact('project', 'projectAssignments', 'canManageAssignments'));
    }

    /**
     * Remove a team member from a project
     */
    public function destroy($assignmentId)
    {
        $assignment = TeamMapping::projectAssignments()->findOrFail($assignmentId);
        $projectName = $assignment->project->proj_name;
        $username = $assignment->username; // Store username before deletion
        
        // Check if current user can manage assignments for this team
        if (!$this->canManageProjectAssignments($assignment->project->team_name)) {
            return redirect()->route('project-assignments.index')
                ->with('error', 'You do not have permission to remove members from this project. Only Team Managers can manage project assignments.');
        }
        
        $assignment->delete();

        // Clear cache for the user who was removed from the project
        $this->clearUserPermissionsCache($username);

        return redirect()->route('project-assignments.index')
            ->with('success', "Team member removed from project: {$projectName}");
    }

    /**
     * Update project assignment role
     */
    public function updateRole(Request $request, $assignmentId)
    {
        $request->validate([
            'role_name' => 'required|string',
        ]);

        $assignment = TeamMapping::projectAssignments()->findOrFail($assignmentId);
        
        // Check if current user can manage assignments for this team
        if (!$this->canManageProjectAssignments($assignment->project->team_name)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to update roles for this project. Only Team Managers can manage project assignments.'
            ], 403);
        }
        
        $assignment->role_name = $request->role_name;
        $assignment->save();

        // Clear cache for the user whose role was updated
        $this->clearUserPermissionsCache($assignment->username);

        return response()->json([
            'success' => true,
            'message' => 'Project role updated successfully'
        ]);
    }

    /**
     * Get available team members for a project (AJAX)
     */
    public function getAvailableMembers(Request $request)
    {
        $projectId = $request->input('project_id');
        $project = Project::findOrFail($projectId);
        
        // Get team members not yet assigned to this project
        $availableMembers = TeamMapping::teamOnly()
            ->where('team_name', $project->team_name)
            ->where('invitation_status', 'accepted')
            ->whereNotIn('username', function($query) use ($projectId) {
                $query->select('username')
                      ->from('teammappings')
                      ->where('project_id', $projectId);
            })
            ->with('user')
            ->get();

        return response()->json($availableMembers);
    }

    /**
     * Show user's roles across all projects
     */
    public function userRoles($username)
    {
        $user = User::where('username', $username)->firstOrFail();
        
        // Get team memberships
        $teamMemberships = TeamMapping::teamOnly()
            ->where('username', $username)
            ->where('invitation_status', 'accepted')
            ->with('team')
            ->get();

        // Get project assignments grouped by project
        $projectRoles = TeamMapping::getUserProjectRoles($username);
        
        return view('project-assignments.user-roles', compact('user', 'teamMemberships', 'projectRoles'));
    }

    /**
     * Clear cache for a specific user's permissions
     */
    private function clearUserPermissionsCache($username)
    {
        try {
            $user = User::where('username', $username)->first();
            if ($user) {
                \Illuminate\Support\Facades\Cache::forget('user_admin_status_' . $user->id);
                \Illuminate\Support\Facades\Cache::forget('user_roles_permissions_' . $user->id);
            }
        } catch (\Exception $e) {
            \Log::error('Error clearing user permissions cache: ' . $e->getMessage());
        }
    }

    /**
     * Check if current user can manage project assignments for a team
     * Only Team Managers (in team-level role, not project-level) can manage assignments
     */
    private function canManageProjectAssignments($teamName)
    {
        $user = \Auth::user();
        
        // Admin users can always manage
        if ($user->isAdmin()) {
            return true;
        }
        
        // Check if user is Team Manager in the team (team-level role, project_id IS NULL)
        return TeamMapping::teamOnly()
            ->where('username', $user->username)
            ->where('team_name', $teamName)
            ->where('role_name', 'Team Manager')
            ->where('invitation_status', 'accepted')
            ->exists();
    }
}
