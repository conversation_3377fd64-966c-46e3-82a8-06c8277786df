<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class taskComment extends Model
{
    use HasFactory;

    // Specify the table name if it does not follow <PERSON><PERSON>'s default naming convention
    protected $table = 'taskComment';

    // Specify the fields that can be mass-assigned
    protected $fillable = [
        'task_id',
        'created_by',
        'assigned_to',
        'duedate',
    ];

    /**
     * Define relationship with the Task model
     */
    public function task()
    {
        return $this->belongsTo(Task::class, 'task_id');
    }

    public static function getCommentById($comment_id)
    {
        return self::findOrFail($comment_id);
    }

    public static function getCreatorsByTask($task_id)
    {
        return self::where('task_id', $task_id)
            ->pluck('created_by')
            ->unique()
            ->toArray();
    }

    public static function filterByCreator($task_id, $created_by = null)
    {
        $query = self::where('task_id', $task_id);

        if (!empty($created_by)) {
            $query->where('created_by', $created_by);
        }

        return $query;
    }

    public static function sortByDate($query, $sortDate = 'desc')
    {
        if (in_array($sortDate, ['asc', 'desc'])) {
            $query->orderBy('updated_at', $sortDate);
        }

        return $query;
    }

    public static function getTaskCommentsWithFilters($task_id, $created_by = null, $sort_date = 'desc', $per_page = 5)
    {
        // Start with base query for the task
        $query = self::where('task_id', $task_id);

        // Apply creator filter if provided
        if (!empty($created_by)) {
            $query->where('created_by', $created_by);
        }

        // Apply sorting
        if (in_array($sort_date, ['asc', 'desc'])) {
            $query->orderBy('updated_at', $sort_date);
        }

        // Return paginated results
        return $query->paginate($per_page);
    }
}