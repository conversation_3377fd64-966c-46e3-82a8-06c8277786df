<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Status</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: none;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 1rem;
        }
        .card-body {
            padding: 1.25rem;
        }
        .form-label {
            font-weight: 500;
        }
        .text-danger {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        .alert {
            margin-bottom: 1rem;
        }
        .form-text {
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <script>
                // Redirect back to index after successful creation
                setTimeout(function() {
                    window.location.href = "{{ route('status.project', ['proj_ID' => $project->id]) }}";
                }, 1000); // Redirect after 1 second
            </script>
        @endif

        @if (session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Create New Status</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('statuses.store') }}" method="post" id="statusForm">
                    @csrf

                    <div class="mb-3">
                        <label for="title" class="form-label">Status Name</label>
                        <input type="text" id="title" name="title" class="form-control" value="{{ old('title') }}" maxlength="100">
                        @include('inc.character-counter', 
                                [ 'inputId' => 'title', 
                                        'counterId' => 'status_title_char_count', 
                                        'maxLength' => 100])

                        <div class="form-text">The slug will be automatically generated from the status name (lowercase with dashes)</div>
                        @error('title')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <input type="hidden" name="project_id" value="{{ $project->id }}">

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-plus me-2"></i> Add Status
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Function to notify parent iframe about height changes
            function notifyParentAboutHeight() {
                const height = Math.max(
                    document.body.scrollHeight,
                    document.documentElement.scrollHeight,
                    document.body.offsetHeight,
                    document.documentElement.offsetHeight,
                    document.body.clientHeight,
                    document.documentElement.clientHeight
                );
                
                // Add small buffer to avoid scrollbars
                const heightWithBuffer = height + 30;
                
                // Send message to parent
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'resize',
                        height: heightWithBuffer,
                        iframeId: 'status-iframe'
                    }, '*');
                }
            }
            
            // Initial height notification
            notifyParentAboutHeight();
            
            // Set up mutation observer to detect content changes
            const observer = new MutationObserver(function() {
                notifyParentAboutHeight();
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });
            
            // Additional triggers for height recalculation
            window.addEventListener('load', notifyParentAboutHeight);
            window.addEventListener('resize', notifyParentAboutHeight);
        });
    </script>
</body>
</html>