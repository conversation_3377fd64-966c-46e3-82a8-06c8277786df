<?php

namespace App\Http\Controllers;
use App\Project;
use App\Team;
use App\Sprint;
use App\UserAccess;
use App\User;
use App\UserStory;
use App\Task;
use App\TeamMapping;
use App\Role;
use App\Permission;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

class ProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    //not real index
    public function index()
{
    $projects = Project::all(); // Fetch all projects

    $pro = []; // Define $pro as an empty array by default

    if (\Auth::check()) {
        $id = \Auth::user()->getId();
        if ($id) {
            $pro = Project::where('user_id', '=', $id)->get();
        }
    }

    dd($projects, $pro);

    return view('profeature.index', ['projects' => $projects, 'pro' => $pro]);
}




    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
{
    $user = \Auth::user();
    $project = new Project;

    // Get team names where user is mapped
    $teamNames = TeamMapping::where('username', "=", $user->username)
        ->pluck('team_name')
        ->toArray();

    // Only fetch teams that the user is part of
    $teams = Team::whereIn('team_name', $teamNames)->get();

    return view('project.create', ['teams' => $teams, 'projects' => $project->all(), 'title' => 'Create Project']);

}

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
{
    $validation = $request->validate([
        'proj_name' => 'required|unique:projects',
        'proj_desc' => 'required',
        'start_date' => 'required|date|after_or_equal:today',
        'end_date' => 'required|date|after_or_equal:start_date'
    ], [
        'proj_name.required' => '*The Project Title is required',
        'proj_name.unique' => '*There is already an existing project with the same name',
        'proj_desc.required' => '*The Description is required',
        'start_date.required' => '*The Start Date is required',
        'end_date.required' => '*The Completion Date is required'
    ]);

    //dd($request->team);
    
    $project = new Project();
    $project->proj_name = $request->proj_name;
    $project->proj_desc = $request->proj_desc;
    $project->start_date = $request->start_date;
    $project->end_date = $request->end_date; 
    $project->team_name = $request-> team; 
    $project->save();    // Step 1
    // create initial roles for new project 
    // (Project Manager[All permissions access], 
    // Developer[Not allowed to do CRUD Status & Roles])
    
    // Create Project Manager Role with all permissions
    $projectManagerRole = Role::create([
        'role_name' => 'Project Manager',
        'project_id' => $project->id
    ]);

    // Create Developer Role with limited permissions (no status/role CRUD)
    $developerRole = Role::create([
        'role_name' => 'Developer', 
        'project_id' => $project->id
    ]);

    // Define permission keys for Project Manager (all permissions)
    $projectManagerPermissionKeys = [
        'view_roles', 'addLane_kanban', 'addTask_kanban', 'editLane_kanban', 
        'deleteLane_kanban', 'deleteTask_kanban', 'addComment_kanban', 
        'updateTaskStatus_kanban', 'editTask_kanban', 'addUserStory_backlog',
        'beginSprint_backlog', 'addToSprint_backlog', 'endSprint_backlog',
        'add_userstory', 'edit_userstory', 'delete_userstory', 'editStatus_userstory',
        'view_task', 'add_task', 'edit_task', 'delete_task', 'viewCalendar_task',
        'viewComments_task', 'add_roles', 'edit_roles', 'delete_roles', 'updateUserRole_roles',
        'add_status', 'edit_status', 'delete_status', 'edit_details',
        'delete_details', 'share_details', 'view_sprintArchive',
        'viewKanbanArchive_sprintArchive', 'viewBurndownArchive_sprintArchive',
        'view_kanban', 'view_burndown', 'view_backlog', 'view_userstory',
        'view_forum', 'view_bugtracking', 'view_status', 'view_details'
    ];

    // Define permission keys for Developer (excluding status and role CRUD)
    $developerPermissionKeys = [
        'view_roles', 'addLane_kanban', 'addTask_kanban', 'editLane_kanban', 
        'deleteLane_kanban', 'deleteTask_kanban', 'addComment_kanban', 
        'updateTaskStatus_kanban', 'editTask_kanban', 'addUserStory_backlog',
        'beginSprint_backlog', 'addToSprint_backlog', 'endSprint_backlog',
        'add_userstory', 'edit_userstory', 'delete_userstory', 'editStatus_userstory',
        'view_task', 'add_task', 'edit_task', 'delete_task', 'viewCalendar_task',
        'viewComments_task', 'view_sprintArchive', 'viewKanbanArchive_sprintArchive',
        'viewBurndownArchive_sprintArchive', 'view_kanban', 'view_burndown', 
        'view_backlog', 'view_userstory', 'view_forum', 'view_bugtracking', 
        'view_status', 'view_details'
        // Excluded: 'add_roles', 'edit_roles', 'delete_roles', 'add_status', 
        // 'edit_status', 'delete_status', 'edit_details', 'delete_details', 'share_details'
    ];

    // Get permission IDs for Project Manager
    $projectManagerPermissionIds = Permission::whereIn('key', $projectManagerPermissionKeys)->pluck('id')->toArray();
    
    // Get permission IDs for Developer
    $developerPermissionIds = Permission::whereIn('key', $developerPermissionKeys)->pluck('id')->toArray();

    // Attach permissions to Project Manager role
    foreach ($projectManagerPermissionIds as $permissionId) {
        $projectManagerRole->permissions()->attach($permissionId);
    }

    // Attach permissions to Developer role
    foreach ($developerPermissionIds as $permissionId) {
        $developerRole->permissions()->attach($permissionId);
    }
      // Step 2
    // Assign all team members of team assigned to newly created project to a role
    // User that creates the project [logged in user basically] is project manager
    // all others are set to developer

    $currentUser = \Auth::user();
    $selectedTeamName = $request->team;

    // Get all team members from the selected team
    $teamMembers = TeamMapping::where('team_name', $selectedTeamName)->get();

    foreach ($teamMembers as $teamMember) {
        // Create new TeamMapping entry for this project
        $projectTeamMapping = new TeamMapping();
        $projectTeamMapping->username = $teamMember->username;
        $projectTeamMapping->team_name = $teamMember->team_name;
        $projectTeamMapping->project_id = $project->id;
        $projectTeamMapping->invitation_status = $teamMember->invitation_status ?? 'accepted';
        
        // Check if this team member is the project creator
        if ($teamMember->username === $currentUser->username) {
            // Assign Project Manager role to project creator
            $projectTeamMapping->role_name = 'Project Manager';
        } else {
            // Assign Developer role to all other team members
            $projectTeamMapping->role_name = 'Developer';
        }
        
        $projectTeamMapping->save();
    }

    // Clear cache for all team members assigned to this project
    $this->clearProjectTeamMembersCache($project->id);

    $userAccess = new UserAccess();
    $userAccess->project_id = $project->id;
    $userAccess->user_id = \Auth::user()->getId();
    $userAccess->attachment_access;
    $userAccess->project_access = 1;
    $userAccess->sprint_access =1;
    $userAccess->forum_access=1;
    $userAccess->userstory_access=1;
    $userAccess->secfeature_access=1;
    $userAccess->save();

    $user = Auth::user();

    // Fetch project IDs where the user is specifically assigned
    $projectIds = TeamMapping::where('username', '=', $user->username)
        ->whereNotNull('project_id')
        ->pluck('project_id')
        ->toArray();

    // Fetch projects that the user is assigned to
    $projects = Project::whereIn('id', $projectIds)->get();

    return view('project.newIndex', compact('projects'));

}



    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Project $project)
    {
        $project = new Project();
        return view('profeature.index')->with ('projects',$project->all());
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Project $project)
    {
        $user = \Auth::user();
        $pro = \App\Project::where('team_name', '=', $user->team_name)->get();

        return view('project.edit')
        ->with('projects', $pro)
        ->with('project', $project)
        ->with('title', 'Edit ' . $project->proj_name);

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,Project $project)
    {
        $project->proj_name=$request->proj_name;
        $project->proj_desc=$request->proj_desc;
        $project->start_date=$request->start_date;
        $project->end_date=$request->end_date; 
        $project->save(); 
    
        return redirect()->route('projects.details', ['id' => $project->id])
        ->with('success', 'Project has successfully been updated!');

    }
    

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Project $project)
{
    // Deletes the team and team mapping records associated with the project
    // $team = \App\Team::where('proj_name', $project->proj_name)->first();
    
    // if ($team) {
    //     // Delete team mappings associated with the team
    //     \App\Teammapping::where('team_name', $team->team_name)->delete();
    //     $team->delete();
    // }

    // Get sprints related to the project
    $sprints = Sprint::where('proj_name', $project->proj_name)->get();

    foreach ($sprints as $sprint) {
        // Get user stories related to each sprint
        $userstories = UserStory::where('sprint_id', $sprint->sprint_id)->get();

        foreach ($userstories as $userstory) {
            // Get tasks related to each user story
            $tasks = Task::where('userstory_id', $userstory->u_id)->get();

            // Delete tasks related to the user story
            foreach ($tasks as $task) {
                $task->delete();
            }

            // Delete the user story
            $userstory->delete();
        }

        // Delete the sprint
        $sprint->delete();
    }

    // Delete the project record
    $project->delete();

    $user = Auth::user();

    // Fetch project IDs where the user is specifically assigned
    $projectIds = TeamMapping::where('username', '=', $user->username)
        ->whereNotNull('project_id')
        ->pluck('project_id')
        ->toArray();

    // Fetch projects that the user is assigned to
    $projects = Project::whereIn('id', $projectIds)->get();

    return view('project.newIndex', compact('projects'));

}

    /**
     * Clear cache for all users assigned to a project
     */
    private function clearProjectTeamMembersCache($projectId)
    {
        try {
            // Get all team members assigned to this project
            $teamMappings = TeamMapping::where('project_id', $projectId)->get();

            foreach ($teamMappings as $mapping) {
                $user = User::where('username', $mapping->username)->first();
                if ($user) {
                    \Illuminate\Support\Facades\Cache::forget('user_admin_status_' . $user->id);
                    \Illuminate\Support\Facades\Cache::forget('user_roles_permissions_' . $user->id);
                }
            }
        } catch (\Exception $e) {
            \Log::error('Error clearing project team members cache: ' . $e->getMessage());
        }
    }
}
