@extends('layouts.app2')

<!-- Add Font Awesome CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

@include('inc.breadcrumbStyle')

@include('inc.navbar')

@section('content')
<!-- Add Font Awesome CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<!-- Header Section with Breadcrumb -->
<div class="mb-4">
    <div class="d-flex align-items-center">
        <h1 class="mb-0"><a href="{{ route('team.index') }}" class="breadcrumb-link">All Teams</a></h1>
        <h1 class="mb-0"><span class="breadcrumb-separator">/</span></h1>
        <h1 class="mb-0"><a href="{{ route('teammapping.index', ['team_name' => $team_name]) }}" class="breadcrumb-link">{{ $team_name }}</a></h1>
        <h1 class="mb-0"><span class="breadcrumb-separator">/</span></h1>
        <h1 class="mb-0 breadcrumb-current">Add Member</h1>
    </div>
</div>

<!-- Team Roles Information -->
<div class="permission-info mb-4">
    <h6><i class="fas fa-info-circle me-2"></i>Team Roles & Permissions</h6>
    <div class="row">
        <div class="col-md-6">
            <strong><i class="fas fa-crown text-warning me-1"></i>Team Manager:</strong>
            <ul class="mb-0 mt-1">
                <li>Add and remove team members</li>
                <li>Change member roles</li>
                <li>View all team information</li>
            </ul>
        </div>
        <div class="col-md-6">
            <strong><i class="fas fa-users text-secondary me-1"></i>Team Member:</strong>
            <ul class="mb-0 mt-1">
                <li>View team information</li>
                <li>Accept team invitations</li>
            </ul>
        </div>
    </div>
</div>

<div class="table-responsive">
    <div class="card">
        <div class="card-header" style="background-color: #f8f9fa; border-bottom: 1px solid rgba(0, 0, 0, 0.125);">
            <h4 class="mb-0">Add Team Members</h4>
        </div>
        <div class="card-body">
            <!-- Email Input Section -->
            <div class="form-group mb-4">
                <label for="email" class="form-label">Email Address:</label>
                <div class="input-group">
                    <input type="email" id="email" class="form-control" placeholder="Enter email address" required>
                    <button type="button" id="addEmailBtn" class="btn btn-primary">Add Email</button>
                </div>
                <small class="form-text text-muted">Enter email addresses to invite users to the team</small>
            </div>
            
            <!-- Role Selection -->
            <div class="form-group mb-4">
                <label for="role" class="form-label">Default Role:</label>
                <select id="role" class="form-control">
                    <option value="Team Member" selected>Team Member</option>
                    <option value="Team Manager">Team Manager</option>
                </select>
                <small class="form-text text-muted">
                    <strong>Team Manager:</strong> Can add/remove members and change roles<br>
                    <strong>Team Member:</strong> Can view team and accept invitations
                </small>
            </div>

            <!-- Email List Display -->
            <div class="form-group mb-4">
                <label class="form-label">Email List:</label>
                <div id="emailList" class="border rounded p-3 min-height-100">
                    <p class="text-muted mb-0" id="noEmailsMsg">No emails added yet</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex gap-2">
                <button type="button" id="sendInvitationsBtn" class="btn btn-success" disabled>
                    <i class="fas fa-envelope"></i> Send Invitations
                </button>
                <button type="button" id="clearAllBtn" class="btn btn-secondary" disabled>
                    <i class="fas fa-trash"></i> Clear All
                </button>
            </div>

            <!-- Hidden form for sending invitations -->
            <form id="invitationForm" action="{{route('teammappings.store')}}" method="post" style="display: none;">
                @csrf
                <input type="hidden" name="team_name" value="{{ $team_name }}">
                <input type="hidden" name="emails" id="emailsInput">
                <input type="hidden" name="role" id="roleInput">
                <input type="hidden" name="action" value="send_invitations">
            </form>
        </div>
    </div>
</div>
<br><br><br>

<style>
    .table-responsive .card {
        background-color: white;
        border-radius: 0.25rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125) !important;
    }
    
    /* Permission info styles */
    .permission-info {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.5rem;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        border-left: 3px solid #0d6efd;
    }

    .min-height-100 {
        min-height: 100px;
    }

    .email-item {
        display: inline-block;
        background-color: #e9ecef;
        border: 1px solid #ced4da;
        border-radius: 20px;
        padding: 5px 12px;
        margin: 2px;
        font-size: 14px;
    }

    .email-item .remove-email {
        margin-left: 8px;
        color: #dc3545;
        cursor: pointer;
        font-weight: bold;
    }

    .email-item .remove-email:hover {
        color: #c82333;
    }

    .d-flex.gap-2 > * {
        margin-right: 0.5rem;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .input-group .btn {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    /* Notification styles */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 9999;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        animation: fadeIn 0.3s ease-out;
        max-width: 350px;
    }
    
    .notification-success {
        background-color: #28a745;
        border-left: 5px solid #1e7e34;
    }
    
    .notification-error {
        background-color: #dc3545;
        border-left: 5px solid #bd2130;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }    }
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('email');
    const addEmailBtn = document.getElementById('addEmailBtn');
    const emailList = document.getElementById('emailList');
    const noEmailsMsg = document.getElementById('noEmailsMsg');
    const sendInvitationsBtn = document.getElementById('sendInvitationsBtn');
    const clearAllBtn = document.getElementById('clearAllBtn');
    const roleSelect = document.getElementById('role');
    const invitationForm = document.getElementById('invitationForm');
    const emailsInput = document.getElementById('emailsInput');
    const roleInput = document.getElementById('roleInput');

    let emails = [];

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function updateEmailList() {
        if (emails.length === 0) {
            emailList.innerHTML = '<p class="text-muted mb-0" id="noEmailsMsg">No emails added yet</p>';
            sendInvitationsBtn.disabled = true;
            clearAllBtn.disabled = true;
        } else {
            let html = '';
            emails.forEach((email, index) => {
                html += `<span class="email-item">
                    ${email}
                    <span class="remove-email" data-index="${index}">&times;</span>
                </span>`;
            });
            emailList.innerHTML = html;
            sendInvitationsBtn.disabled = false;
            clearAllBtn.disabled = false;
        }
    }

    function addEmail() {
        const email = emailInput.value.trim().toLowerCase();
        
        if (!email) {
            alert('Please enter an email address');
            return;
        }

        if (!isValidEmail(email)) {
            alert('Please enter a valid email address');
            return;
        }

        if (emails.includes(email)) {
            alert('This email is already in the list');
            return;
        }

        emails.push(email);
        emailInput.value = '';
        updateEmailList();
    }

    function removeEmail(index) {
        emails.splice(index, 1);
        updateEmailList();
    }

    function clearAll() {
        if (confirm('Are you sure you want to clear all emails?')) {
            emails = [];
            updateEmailList();
        }
    }    function sendInvitations() {
        if (emails.length === 0) {
            alert('Please add at least one email address');
            return;
        }

        const confirmMessage = `Are you sure you want to send invitations to ${emails.length} email(s) with the role "${roleSelect.value}"?`;
        if (confirm(confirmMessage)) {
            // Disable button to prevent double submission
            sendInvitationsBtn.disabled = true;
            sendInvitationsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            
            emailsInput.value = JSON.stringify(emails);
            roleInput.value = roleSelect.value;
            invitationForm.submit();
        }
    }

    // Event listeners
    addEmailBtn.addEventListener('click', addEmail);
    
    emailInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            addEmail();
        }
    });

    emailList.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-email')) {
            const index = parseInt(e.target.dataset.index);
            removeEmail(index);
        }
    });

    clearAllBtn.addEventListener('click', clearAll);
    sendInvitationsBtn.addEventListener('click', sendInvitations);
});
</script>

@endsection