<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\User;
use App\TeamMapping;
use App\Teams;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Config;
use App\Team;

class SSOController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Override the parent constructor to not apply auth middleware by default
        // Only apply auth middleware to methods that need it
        $this->middleware('auth')->except(['showLoginForm', 'handleDirectLogin', 'verifyToken', 'lecturerStudentAssignment']);
    }

    /**
     * Show the SSO login form.
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        return view('auth.sso-login');
    }

    /**
     * Handle direct SSO login from mock portal.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleDirectLogin(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'token' => 'required|string',
                'timestamp' => 'required|numeric',
                'client_id' => 'required|string',
                'state' => 'required|string',
                'redirect_url' => 'required|url',
                'user_data' => 'required|string',
            ]);

            // Verify if the request is not too old (prevent replay attacks)
            $requestTime = (int)$request->timestamp;
            $currentTime = time();
            
            if ($currentTime - $requestTime > 300) { // 5 minutes expiry
                throw ValidationException::withMessages([
                    'token' => ['SSO login link has expired.'],
                ]);
            }

            // Verify client ID
            if ($request->client_id !== 'mock-portal-client') {
                throw ValidationException::withMessages([
                    'client_id' => ['Invalid client ID.'],
                ]);
            }

            // Verify token signature
            $expectedToken = hash('sha256', $request->timestamp . Config::get('services.mock_portal.key') . $request->client_id);
            if (!hash_equals($expectedToken, $request->token)) {
                throw ValidationException::withMessages([
                    'token' => ['Invalid token signature.'],
                ]);
            }

            // Decode and validate user data
            $userData = json_decode(base64_decode($request->user_data), true);
            if (!$userData || !isset($userData['email']) || !isset($userData['name']) || !isset($userData['username']) || !isset($userData['identifier'])) {
                throw ValidationException::withMessages([
                    'user_data' => ['Invalid user data format.'],
                ]);
            }

            // Find or create user
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                [
                    'name' => $userData['name'],
                    'username' => $userData['username'],
                    'identifier' => $userData['identifier'],
                    'password' => Hash::make(Str::random(16)), // Random password as SSO users don't need it
                    'country' => 'Malaysia' // Default value
                ]
            );

            // Login the user
            Auth::login($user);

            // Generate token for API verification
            $apiToken = Str::random(60);
            $user->forceFill([
                'remember_token' => $apiToken,
            ])->save();

            // Redirect to our system's home page after successful authentication
            return redirect('/home');

        } catch (\Exception $e) {
            // Only redirect back to external system in case of error
            $queryParams = http_build_query([
                'error' => 'Authentication failed: ' . $e->getMessage(),
                'state' => $request->state ?? ''
            ]);

            return redirect($request->redirect_url . '?' . $queryParams);
        }
    }

    public function lecturerStudentAssignment(Request $request){
        try {
            \Log::info('lecturerStudentAssignment: Starting request processing', [
                'client_id' => $request->client_id,
                'timestamp' => $request->timestamp,
                'assignments_count' => is_array($request->assignments) ? count($request->assignments) : 0
            ]);
    
            // Validate the request
            $request->validate([
                'token' => 'required|string',
                'timestamp' => 'required|numeric',
                'client_id' => 'required|string',
                'state' => 'required|string',
                'redirect_url' => 'required|url',
                'assignments' => 'required|array',
                'assignments.*.lecturer' => 'required|array',
                'assignments.*.lecturer.staff_id' => 'required|string',
                'assignments.*.lecturer.name' => 'required|string',
                'assignments.*.lecturer.username' => 'required|string',
                'assignments.*.lecturer.email' => 'required|email',
                'assignments.*.students' => 'required|array',
                'assignments.*.students.*.matric_number' => 'required|string',
                'assignments.*.students.*.username' => 'required|string',
                'assignments.*.students.*.email' => 'required|string',
                'assignments.*.students.*.name' => 'required|string'
            ]);
    
            \Log::info('lecturerStudentAssignment: Request validation passed');
    
            // Verify if the request is not too old (prevent replay attacks)
            $requestTime = (int)$request->timestamp;
            $currentTime = time();
            
            \Log::info('lecturerStudentAssignment: Checking timestamp', [
                'request_time' => $requestTime,
                'current_time' => $currentTime,
                'time_diff' => $currentTime - $requestTime
            ]);
            
            if ($currentTime - $requestTime > 300) { // 5 minutes expiry
                \Log::warning('lecturerStudentAssignment: Request timestamp expired', [
                    'time_diff' => $currentTime - $requestTime
                ]);
                throw ValidationException::withMessages([
                    'token' => ['SSO login link has expired.'],
                ]);
            }
    
            // Verify client ID
            \Log::info('lecturerStudentAssignment: Verifying client ID', [
                'provided_client_id' => $request->client_id
            ]);
            
            if ($request->client_id !== 'mock-portal-client') {
                \Log::error('lecturerStudentAssignment: Invalid client ID', [
                    'provided_client_id' => $request->client_id
                ]);
                throw ValidationException::withMessages([
                    'client_id' => ['Invalid client ID.'],
                ]);
            }
    
            // Verify token signature
            $expectedToken = hash('sha256', $request->timestamp . Config::get('services.mock_portal.key') . $request->client_id);
            
            \Log::info('lecturerStudentAssignment: Verifying token signature', [
                'provided_token' => $request->token,
                'expected_token' => $expectedToken,
                'tokens_match' => hash_equals($expectedToken, $request->token)
            ]);
            
            if (!hash_equals($expectedToken, $request->token)) {
                \Log::error('lecturerStudentAssignment: Invalid token signature');
                throw ValidationException::withMessages([
                    'token' => ['Invalid token signature.'],
                ]);
            }
    
            \Log::info('lecturerStudentAssignment: All security checks passed, processing assignments');
    
            // Process assignments directly from request
            $assignments = $request->assignments;
    
            foreach ($assignments as $assignmentIndex => $assignment) {
                \Log::info("lecturerStudentAssignment: Processing assignment {$assignmentIndex}", [
                    'lecturer_staff_id' => $assignment['lecturer']['staff_id'],
                    'lecturer_name' => $assignment['lecturer']['name'],
                    'students_count' => count($assignment['students'])
                ]);
    
                // Create lecturer
                $lecturerData = $assignment['lecturer'];
                
                \Log::info('lecturerStudentAssignment: Creating/finding lecturer', [
                    'staff_id' => $lecturerData['staff_id'],
                    'name' => $lecturerData['name'],
                    'username' => $lecturerData['username'],
                    'email' => $lecturerData['email']
                ]);
                
                $lecturer = User::firstOrCreate(
                    ['identifier' => $lecturerData['staff_id']],
                    [
                        'name' => $lecturerData['name'],
                        'username' => $lecturerData['username'],
                        'email' => $lecturerData['email'],
                        'password' => Hash::make(Str::random(16)),
                        'country' => 'Malaysia',
                        'is_lecturer' => true
                    ]
                );
    
                \Log::info('lecturerStudentAssignment: Lecturer processed', [
                    'lecturer_id' => $lecturer->id,
                    'was_created' => $lecturer->wasRecentlyCreated
                ]);
    
                // Create students
                foreach ($assignment['students'] as $studentIndex => $studentData) {
                    \Log::info("lecturerStudentAssignment: Processing student {$studentIndex}", [
                        'matric_number' => $studentData['matric_number'],
                        'name' => $studentData['name'],
                        'username' => $studentData['username'],
                        'email' => $studentData['email']
                    ]);
    
                    $student = User::firstOrCreate(
                        ['identifier' => $studentData['matric_number']],
                        [
                            'name' => $studentData['name'], // No name in JSON, use matric as placeholder
                            'username' => $studentData['username'],
                            'email' => $studentData['email'],
                            'password' => Hash::make(Str::random(16)), //create random string as username
                            'country' => 'Malaysia',
                            'is_lecturer' => false
                        ]
                    );
    
                    \Log::info('lecturerStudentAssignment: Student processed', [
                        'student_id' => $student->id,
                        'was_created' => $student->wasRecentlyCreated
                    ]);
    
                    // Check if student already has a team assignment
                    $existingTeamMapping = TeamMapping::where('username', $student->username)->first();
    
                    \Log::info('lecturerStudentAssignment: Checking existing team mapping', [
                        'student_username' => $student->username,
                        'has_existing_team' => !is_null($existingTeamMapping),
                        'existing_team_name' => $existingTeamMapping ? $existingTeamMapping->team_name : null
                    ]);
    
                    if (!$existingTeamMapping) {
                        // Create a new team for the student
                        $teamName = $student->identifier . '_' . $student->name;
                        
                        \Log::info('lecturerStudentAssignment: Creating new team', [
                            'team_name' => $teamName,
                            'student_username' => $student->username
                        ]);
                        
                        $team = Team::firstOrCreate(
                            ['team_name' => $teamName],
                            ['proj_name' => ''] // Project name is left blank as per requirements
                        );
    
                        \Log::info('lecturerStudentAssignment: Team created/found', [
                            'team_id' => $team->id,
                            'team_name' => $team->team_name,
                            'was_created' => $team->wasRecentlyCreated
                        ]);
    
                        // Assign student to the team
                        $studentTeamMapping = TeamMapping::create([
                            'username' => $student->username,
                            'team_name' => $teamName,
                            'role_name' => 'Student'
                        ]);
    
                        \Log::info('lecturerStudentAssignment: Student assigned to team', [
                            'mapping_id' => $studentTeamMapping->id,
                            'username' => $student->username,
                            'team_name' => $teamName,
                            'role' => 'Student'
                        ]);
    
                        // Assign lecturer to the team as Supervisor
                        $lecturerTeamMapping = TeamMapping::create([
                            'username' => $lecturer->username,
                            'team_name' => $teamName,
                            'role_name' => 'Supervisor'
                        ]);
    
                        \Log::info('lecturerStudentAssignment: Lecturer assigned to team', [
                            'mapping_id' => $lecturerTeamMapping->id,
                            'username' => $lecturer->username,
                            'team_name' => $teamName,
                            'role' => 'Supervisor'
                        ]);
                    } else {
                        // If student already has a team, check if lecturer needs to be added
                        $lecturerTeamMapping = TeamMapping::where('username', $lecturer->username)
                            ->where('team_name', $existingTeamMapping->team_name)
                            ->first();
    
                        \Log::info('lecturerStudentAssignment: Checking lecturer assignment to existing team', [
                            'lecturer_username' => $lecturer->username,
                            'team_name' => $existingTeamMapping->team_name,
                            'lecturer_already_assigned' => !is_null($lecturerTeamMapping)
                        ]);
    
                        if (!$lecturerTeamMapping) {
                            // Add lecturer to existing team as Supervisor
                            $newLecturerTeamMapping = TeamMapping::create([
                                'username' => $lecturer->username,
                                'team_name' => $existingTeamMapping->team_name,
                                'role_name' => 'Supervisor'
                            ]);
    
                            \Log::info('lecturerStudentAssignment: Lecturer added to existing team', [
                                'mapping_id' => $newLecturerTeamMapping->id,
                                'username' => $lecturer->username,
                                'team_name' => $existingTeamMapping->team_name,
                                'role' => 'Supervisor'
                            ]);
                        } else {
                            \Log::info('lecturerStudentAssignment: Lecturer already assigned to team', [
                                'username' => $lecturer->username,
                                'team_name' => $existingTeamMapping->team_name
                            ]);
                        }
                    }
                }
    
                \Log::info("lecturerStudentAssignment: Completed assignment {$assignmentIndex}");
            }
    
            \Log::info('lecturerStudentAssignment: Successfully completed all assignments');
    
            return response()->json([
                'success' => true,
                'message' => 'Successfully synchronized lecturer-student assignments with SAgilePMT'
            ]);
    
        } catch (\Exception $e) {
            \Log::error('Error in lecturerStudentAssignment: ' . $e->getMessage(), [
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync assignments: ' . $e->getMessage()
            ], 500);
        }
    }
} 