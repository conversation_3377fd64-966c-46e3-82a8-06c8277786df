<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('permission')->insert([
            [
                'key' => 'view_kanban',
                'description' => 'Allows access to the kanban board',
            ],
            [
                'key' => 'view_burndown',
                'description' => 'Allows viewing of the burndown chart for tracking sprint progress',
            ],
            [
                'key' => 'view_backlog',
                'description' => 'Allows access to the product backlog items',
            ],
            [
                'key' => 'view_userstory',
                'description' => 'Allows viewing of user stories and their details',
            ],
            [
                'key' => 'view_forum',
                'description' => 'Allows access to the project discussion forum',
            ],
            [
                'key' => 'view_bugtracking',
                'description' => 'Allows viewing of reported bugs and tracking their status',
            ],
            [
                'key' => 'view_status',
                'description' => 'Allows viewing the statuses available in the project',
            ],
            [
                'key' => 'view_details',
                'description' => 'Allows viewing of project details',
            ],
            [
                'key' => 'view_roles',
                'description' => 'Allows viewing the list of users',
            ],
            [
                'key' => 'addLane_kanban',
                'description' => 'Allows addition of a lane in kanban board',
            ],
            [
                'key' => 'addTask_kanban',
                'description' => 'Allows addition of a task using kanban board',
            ],
            [
                'key' => 'editLane_kanban',
                'description' => 'Allows the editing of a lane name in kanban board',
            ],
            [
                'key' => 'deleteLane_kanban',
                'description' => 'Allows the deletion of a lane in kanban board',
            ],
            [
                'key' => 'deleteTask_kanban',
                'description' => 'Allows the deletion of a task in kanban board',
            ],
            [
                'key' => 'addComment_kanban',
                'description' => 'Allows the addition of a comment on a task in kanban board',
            ],
            [
                'key' => 'addUserStory_backlog',
                'description' => 'Allows the creation of userstory from backlog',
            ],
            [
                'key' => 'beginSprint_backlog',
                'description' => 'Allows the starting of a sprint from the backlog',
            ],
            [
                'key' => 'addToSprint_backlog',
                'description' => 'Allows the addition of items into the current sprint from the backlog',
            ],
            [
                'key' => 'endSprint_backlog',
                'description' => 'Allows the ending of current sprint from the backlog',
            ],
            [
                'key' => 'add_userstory',
                'description' => 'Allows creation of user story',
            ],
            [
                'key' => 'edit_userstory',
                'description' => 'Allows the editing of user story',
            ],
            [
                'key' => 'delete_userstory',
                'description' => 'Allows the deletion of a user story',
            ],
            [
                'key' => 'editStatus_userstory',
                'description' => 'Allows updating the user story status',
            ],
            [
                'key' => 'add_task',
                'description' => 'Allows creation of new tasks under user story',
            ],
            [
                'key' => 'edit_task',
                'description' => 'Allows editing of a task',
            ],
            [
                'key' => 'delete_task',
                'description' => 'Allows deletion of a task',
            ],
            [
                'key' => 'viewCalendar_task',
                'description' => 'Allows viewing the task calendar',
            ],
            [
                'key' => 'viewComments_task',
                'description' => 'Allows viewing the comments page from task',
            ],
            [
                'key' => 'add_roles',
                'description' => 'Allows creation of a role',
            ],
            [
                'key' => 'edit_roles',
                'description' => 'Allows permission editing of a role',
            ],
            [
                'key' => 'delete_roles',
                'description' => 'Allows deletion of a role',
            ],
            [
                'key' => 'add_status',
                'description' => 'Allows creation of a status',
            ],
            [
                'key' => 'edit_status',
                'description' => 'Allows editing status name',
            ],
            [
                'key' => 'delete_status',
                'description' => 'Allows deletion of a status',
            ],
            [
                'key' => 'edit_details',
                'description' => 'Allows editing of project details',
            ],
            [
                'key' => 'delete_details',
                'description' => 'Allows deletion of project',
            ],
            [
                'key' => 'view_sprintArchive',
                'description' => 'Allows access to the sprint archive',
            ],
            [
                'key' => 'viewKanbanArchive_sprintArchive',
                'description' => 'Allows viewing the kanban archive',
            ],
            [
                'key' => 'viewBurndownArchive_sprintArchive',
                'description' => 'Allows viewing the burndown archive',
            ],
            [
                'key' => 'updateTaskStatus_kanban',
                'description' => 'Allows updating task status by dragging and dropping in the Kanban board',
            ],
            [
                'key' => 'view_task',
                'description' => 'Allows viewing task list from user story',
            ],
            [
                'key' => 'share_details',
                'description' => 'Allows sharing project for public view',
            ],
            [
                'key' => 'editTask_kanban',
                'description' => 'Allows the editing of a task using kanban board',
            ],
            [
                'key' => 'updateUserRole_roles',
                'description' => 'Allows updating user role in the project',
            ],
        ]);
    }
}
