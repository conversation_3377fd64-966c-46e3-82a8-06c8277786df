@extends('layouts.app2')

@include('inc.breadcrumbStyle')

@section('content')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header Section with Breadcrumb -->
            <div class="mb-4">
                <div class="d-flex align-items-center">
                    <h1 class="mb-0 breadcrumb-current">Project Assignments</h1>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Project Assignment Permissions Information -->
            <div class="permission-info mb-4">
                <div class="d-flex justify-content-between align-items-center" 
                     data-bs-toggle="collapse" 
                     data-bs-target="#permissionsCollapse" 
                     aria-expanded="false" 
                     aria-controls="permissionsCollapse"
                     style="cursor: pointer;">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Project Assignment Permissions</h6>
                    <i class="fas fa-chevron-down transition-icon"></i>
                </div>
                <div class="collapse mt-2" id="permissionsCollapse">
                    <div class="row">
                        <div class="col-md-8">
                            <strong><i class="fas fa-crown text-warning me-1"></i>Team Manager:</strong>
                            <ul class="mb-0 mt-1">
                                <li>Assign team members to projects</li>
                                <li>Remove team members from projects</li>
                                <li>Update project roles for team members</li>
                                <li>View all project assignments for their teams</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <strong><i class="fas fa-users text-secondary me-1"></i>Team Member:</strong>
                            <ul class="mb-0 mt-1">
                                <li>View project assignments</li>
                                <li>See team project information</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Projects Grid -->
            <div class="row">
                @forelse($projects as $project)
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">{{ $project->proj_name }}</h5>
                                <span class="badge bg-primary">{{ $project->team_name }}</span>
                            </div>
                            <div class="card-body">
                                <p class="card-text text-muted">{{ Str::limit($project->proj_desc, 100) }}</p>
                                
                                <!-- Project Assignment Stats -->
                                @php
                                    $assignments = $projectAssignments->get($project->id, collect());
                                    $assignedCount = $assignments->count();
                                @endphp
                                
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-users me-1"></i>
                                        {{ $assignedCount }} member(s) assigned
                                    </small>
                                </div>

                                <!-- Assigned Members Preview -->
                                @if($assignedCount > 0)
                                    <div class="mb-3">
                                        <small class="text-muted d-block mb-1">Assigned Members:</small>
                                        @foreach($assignments->take(3) as $assignment)
                                            <span class="badge bg-light text-dark me-1 mb-1">
                                                {{ $assignment->username }} 
                                                <small>({{ $assignment->role_name }})</small>
                                            </span>
                                        @endforeach
                                        @if($assignedCount > 3)
                                            <span class="badge bg-secondary">+{{ $assignedCount - 3 }} more</span>
                                        @endif
                                    </div>
                                @endif
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100">
                                    <a href="{{ route('project-assignments.show', $project->id) }}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View Assignments
                                    </a>
                                    @if($managedTeams->contains($project->team_name))
                                        <a href="{{ route('project-assignments.create', ['project_id' => $project->id]) }}" 
                                           class="btn btn-success btn-sm">
                                            <i class="fas fa-plus me-1"></i>Assign Member
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Projects Available</h4>
                            <p class="text-muted">You don't have access to any projects yet.</p>
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s;
}

.card:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
    transform: translateY(-2px);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

/* Permission info styles */
.permission-info {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    border-left: 3px solid #0d6efd;
}

.permission-info [data-bs-toggle="collapse"]:hover {
    background-color: #e9ecef;
    border-radius: 0.25rem;
    padding: 0.25rem;
    margin: -0.25rem;
}

.transition-icon {
    transition: transform 0.3s ease;
}

.transition-icon.rotated {
    transform: rotate(180deg);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle chevron rotation for collapsible permissions
    const permissionsCollapse = document.getElementById('permissionsCollapse');
    const chevronIcon = document.querySelector('[data-bs-target="#permissionsCollapse"] .transition-icon');
    
    if (permissionsCollapse && chevronIcon) {
        permissionsCollapse.addEventListener('show.bs.collapse', function () {
            chevronIcon.classList.add('rotated');
        });
        
        permissionsCollapse.addEventListener('hide.bs.collapse', function () {
            chevronIcon.classList.remove('rotated');
        });
    }
});
</script>
@endsection
