<?php

namespace App\Http\Controllers;

use App\Project;
use App\UserStory;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class UCDController extends Controller
{
    public function index($project_id)
    {
        // $user = \Auth::user();
        $project = Project::where('id', $project_id)->first();
        $userstory = UserStory::where('proj_id', '=', $project_id)->get();

        $userstories = $userstory->pluck('user_story');
        $url = 'https://sagile-visual-narrator.vercel.app/ucd/';

        $jsonData = [
            'user_stories' => $userstories,
            'system_name' => $project->proj_name,
        ];

        try {
            Log::info('UCDController: Sending request to UCD service', [
                'project_id' => $project_id,
                'user_stories_count' => $userstories->count(),
                'system_name' => $project->proj_name,
            ]);
            for( $i = 0; $i < count($userstories); $i++) {
                Log::info('UCDController: User Story ' . ($i + 1) . ': ' . $userstories[$i]);
            }

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->withBody(json_encode($jsonData), 'application/json')->post($url);

            Log::info('UCDController: Received response from UCD service', [
                'status_code' => $response->status(),
                'response_body_length' => strlen($response->body()),
            ]);

            if ($response->successful()) {
                $responseData = $response->json();
                $dataUri = 'data:image/png;base64,' . $responseData['image'];
                $plantumlText = $responseData['plantuml_text'];
                $plantumlUrl = $responseData['plantuml_url'];
                $errorMsg = '';
                Log::info('UCDController: Successfully received response from UCD service', [
                    'data_uri_length' => strlen($dataUri),
                    'plantuml_text_length' => strlen($plantumlText),
                    'plantuml_url' => $plantumlUrl,
                ]);
            } else {
                $dataUri = '';
                $plantumlText = '';
                $plantumlUrl = '';
                $errorMsg = $this->handleErrorResponse($response->body());
                Log::error('UCDController: Error response from UCD service', [
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                ]);
            }
        } catch (\Exception $e) {
            $dataUri = '';
            $plantumlText = '';
            $plantumlUrl = '';
            $errorMsg = 'Error connecting to the diagram service: ' . $e->getMessage();
        }

        return view('ucd.index')
            ->with('title', 'UCD for ' . $project->proj_name)
            ->with('project', $project)
            ->with('dataUri', $dataUri)
            ->with('plantumlText', $plantumlText)
            ->with('plantumlUrl', $plantumlUrl)
            ->with('userstories', $userstories)
            ->with('errorMsg', $errorMsg);
    }

    private function handleErrorResponse($responseBody)
    {
        if ($responseBody == 'Invalid payload format: user_stories need to be in a list') {
            Log::error('UCDController Error: No User Stories detected.');
            return 'Error: No User Stories detected.';
        } elseif ($responseBody == 'Invalid payload format: some (or all) items in user_stories are not strings') {
            Log::error('UCDController Error: User Stories identified as not being strings.');
            return 'Error: User Stories identified as not being strings.';
        } elseif ($responseBody == 'Invalid payload format: system_name must be a string and cannot be empty') {
            Log::error('UCDController Error: Sprint name cannot be empty.');
            return 'Error: Sprint name cannot be empty.';
        } else {
            Log::error('UCDController Error: Unknown error occurred. Response body: ' . $responseBody);
            return 'Error: Unknown error occurred.';
        }
    }
    
}
