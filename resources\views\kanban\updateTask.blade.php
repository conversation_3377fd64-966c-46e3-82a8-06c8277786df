<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Task</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: none;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 1rem;
        }
        .card-body {
            padding: 1.25rem;
        }
        .form-label {
            font-weight: 500;
        }
        .text-danger {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        .date-info {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Update Task</h4>
            </div>
            <div class="card-body">
                <form id="updateTaskForm" action="{{ route('tasks.update', ['task' => $task]) }}" method="post" enctype="multipart/form-data">
                    @csrf

                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" id="title" name="title" class="form-control" value="{{ old('title', $task->title) }}" maxlength="100" required>
                        @include('inc.character-counter', 
                        [ 'inputId' => 'title', 
                                'counterId' => 'title_char_count', 
                                'maxLength' => 100])
                        @error('title')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description" name="description" class="form-control" rows="3" maxlength="300">{{ old('description', $task->description) }}</textarea>
                        @include('inc.character-counter', 
                        [ 'inputId' => 'description', 
                                'counterId' => 'description_char_count', 
                                'maxLength' => 300])
                        @error('description')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="user_names" class="form-label">Assigned to</label>
                        <select name="user_names[]" id="user_names" class="form-select" multiple>
                            @foreach($teamlist as $teammember)
                            <option value="{{ $teammember['username'] }}"
                                {{ (old('user_names') && in_array($teammember['username'], old('user_names'))) ? 'selected' : '' }}>
                                {{ $teammember['username'] }} (Team: {{ $teammember['team_name'] }})
                            </option>
                            @endforeach
                        </select>
                        @error('user_names')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <input type="hidden" name="order" value="{{ old('order', $task->order) }}">

                    <input type="hidden" name="status_id" value="{{ $status_id }}">
                    <input type="hidden" name="sprint_id" value="{{ $sprint_id }}">
                    <input type="hidden" name="sprintProjId" value="{{ $sprintProjId }}">
                    <input type="hidden" name="isKanban" value="1">

                    <div class="mb-3">
                        <label for="userstory" class="form-label">User Story</label>
                        <select name="userstory" id="userstory" class="form-select" required>
                            @foreach ($userStories as $userStory)
                            <option value="{{ $userStory->user_story }}"
                                {{ $userStory->u_id == $task->userstory_id ? 'selected' : '' }}>
                                {{ $userStory->user_story }}
                            </option>
                            @endforeach
                        </select>
                        @error('userstory')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" id="start_date" name="start_date" class="form-control" value="{{ old('start_date', $task->start_date) }}" required>
                            @error('start_date')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                            <div class="date-info">
                                {{ $sprint->sprint_name }} Start Date: {{ date('d F Y', strtotime($sprint->start_sprint)) }}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" id="end_date" name="end_date" class="form-control" value="{{ old('end_date', $task->end_date) }}" required>
                            @error('end_date')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                            <div class="date-info">
                                {{ $sprint->sprint_name }} End Date: {{ date('d F Y', strtotime($sprint->end_sprint)) }}
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ route('sprint.kanbanPage', ['proj_id' => $sprintProjId, 'sprint_id' => $sprint_id]) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i> Back to Kanban
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i> Update Task
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>