<?php

namespace App\Http\Controllers;

use App\User;
use App\Role;
use App\Project;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;



class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\RedirectResponse
     */
    public function index($project_id)
    {
        $user = \Auth::user();
        if (!$user) {
            return redirect()->route('login')->with('error', 'Please log in to access this page');
        }

        $teammapping = \App\TeamMapping::where('username', $user->username)->pluck('team_name')->toArray();
        $pro = Project::whereIn('team_name', $teammapping)->get();

        // Retrieve roles based on project_id
        $roles = Role::where('project_id', $project_id)->get();

        // Get current project to find team members
        $currentProject = $pro->firstWhere('id', $project_id);
        $teamMembers = collect();
        
        if ($currentProject) {
            // Get team members for the current project's team
            $teamMembers = \App\TeamMapping::where('team_name', $currentProject->team_name)
                ->where('project_id', $currentProject->id)
                ->where('invitation_status', 'accepted')
                ->get();
        }

        return view('role.index', [
            'roles' => $roles,
            'pro' => $pro,
            'teamMembers' => $teamMembers,
            'title' => 'Role'
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create($project_id)
    {
        return view('role.create')
            ->with('title', 'Create Role')
            ->with('project_id',$project_id);
    }
    
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Merge project_id into the request data for validation
        $data = $request->all();
        $data['project_id'] = $request->project_id;
          try {
            // Validate all data including project_id
            $validatedData = $request->validate([
                'role_name' => [
                    'required', 
                    Rule::unique('roles', 'role_name')->where('project_id', $request->project_id)
                ],
                'project_id' => ['required']
            ]);

            // Create the role with validated data
            $role = Role::create($validatedData);

            // Attach permissions if any were selected
            if ($request->has('permissions')) {
                $role->permissions()->attach($request->permissions);
            }

            // Clear cache for all users with this role
            $this->clearRolePermissionsCache($role->role_name, $request->project_id);

            // Redirect using direct URL path like StatusController
            $roles = Role::all();
            $user = \Auth::user();
            $teammapping = \App\TeamMapping::where('username', $user->username)->pluck('team_name')->toArray();
            $pro = Project::whereIn('team_name', $teammapping)->get();
            return redirect()
                ->route('role.index', ['project_id' => $request->project_id])
                ->with('success', 'Role has successfully been created!');        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Role creation failed: ' . $e->getMessage());
            \Log::error('Request data: ' . json_encode($request->all()));
            
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to create role: ' . $e->getMessage()]);
        }
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    /**
     * Display the specified resource.
     *
     * @param  \App\Role  $role
     * @return \Illuminate\Http\Response
     */
    public function show(Role $role)
    {
        return response()->json($role);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Role  $role
     * @return \Illuminate\Contracts\View\View
     */
    public function edit(Role $role)
    {
        $user = \Auth::user();
        return view('role.edit')
            ->with('roles', Role::all())
            ->with('role', $role);
    }

    


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Role  $role
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Role $role)
    {
        try {
            // Store project_id before deletion for redirect
            $projectId = $role->project_id;
            
            // Clear cache for all users with this role before deletion
            $this->clearRolePermissionsCache($role->role_name, $role->project_id);
            
            // Detach all permissions from the role (removes entries from permission_role table)
            $role->permissions()->detach();
            
            // Delete the role
            $role->delete();

            // Get required data for redirect
            $roles = Role::all();
            $user = \Auth::user();
            $teammapping = \App\TeamMapping::where('username', $user->username)->pluck('team_name')->toArray();
            $pro = Project::whereIn('team_name', $teammapping)->get();

            return redirect()
                    ->route('role.index', ['project_id' => $projectId])
                    ->with('success', 'Role has successfully been deleted!');
                    
        } catch (\Exception $e) {
            \Log::error('Role deletion failed: ' . $e->getMessage());
            
            return redirect()->back()
                ->withErrors(['error' => 'Failed to delete role: ' . $e->getMessage()]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Role  $role
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Role $role)
    {
        // Merge project_id into the request data for validation
        $data = $request->all();
        $data['project_id'] = $role->project_id;
        
        try {
            // Validate all data including project_id
            $validatedData = $request->validate([
                'role_name' => [
                    'required',
                    Rule::unique('roles', 'role_name')
                        ->ignore($role->role_id, 'role_id')
                        ->where('project_id', $role->project_id),
                ],
                'project_id' => ['required']
            ]);

            // Update the role with validated data
            $role->update([
                'role_name' => $validatedData['role_name'],
                'project_id' => $validatedData['project_id']
            ]);

            // Sync permissions if any were selected
            if ($request->has('permissions')) {
                $role->permissions()->sync($request->permissions);
            } else {
                // If no permissions are selected, detach all
                $role->permissions()->detach();
            }

            // Clear cache for all users with this role
            $this->clearRolePermissionsCache($role->role_name, $role->project_id);

            return redirect()
                ->route('role.index', ['project_id' => $role->project_id])
                ->with('success', 'Role has successfully been updated!');
                
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Role update failed: ' . $e->getMessage());
            \Log::error('Request data: ' . json_encode($request->all()));
            
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update role: ' . $e->getMessage()]);
        }
    }

    public function createRole(Request $request)
    {
        // Validate request
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Create role
        $role = Role::create(['name' => $request->name]);

        return redirect()->back()->with('success', 'Role created successfully.');
    }

    public function assignRole(Request $request)
    {
        // Validate request
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'role' => 'required|string|exists:roles,name',
        ]);

        // Assign role to user
        $user = User::findOrFail($request->user_id);
        $user->assignRole($request->role);

        return redirect()->back()->with('success', 'Role assigned successfully.');
    }

    /**
     * Update team member role
     */    public function updateTeamMemberRole(Request $request, $id)
    {
        try {
            $teammapping = \App\TeamMapping::findOrFail($id);
            
            // Find the user and clear their permissions cache before updating role
            $user = User::where('username', $teammapping->username)->first();
            if ($user) {
                \Illuminate\Support\Facades\Cache::forget('user_admin_status_' . $user->id);
                \Illuminate\Support\Facades\Cache::forget('user_roles_permissions_' . $user->id);
            }
            
            $teammapping->role_name = $request->role_name;
            $teammapping->save();

            return response()->json([
                'success' => true,
                'message' => 'Team member role updated successfully!'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating team member role: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update role. Please try again.'
            ], 500);
        }
    }

    /**
     * Clear cache for all users with a specific role in a project
     */
    private function clearRolePermissionsCache($roleName, $projectId)
    {
        try {
            // Get all team members with this role in the project
            $teamMappings = \App\TeamMapping::where('role_name', $roleName)
                ->where('project_id', $projectId)
                ->get();

            foreach ($teamMappings as $mapping) {
                $user = User::where('username', $mapping->username)->first();
                if ($user) {
                    \Illuminate\Support\Facades\Cache::forget('user_admin_status_' . $user->id);
                    \Illuminate\Support\Facades\Cache::forget('user_roles_permissions_' . $user->id);
                }
            }
        } catch (\Exception $e) {
            \Log::error('Error clearing role permissions cache: ' . $e->getMessage());
        }
    }
}

