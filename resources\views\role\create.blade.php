<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Role</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: none;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 1rem;
        }
        .card-body {
            padding: 1.25rem;
        }
        .form-label {
            font-weight: 500;
        }
        .text-danger {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        .alert {
            margin-bottom: 1rem;
        }
        .permissions-container {
            overflow-y: auto;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .permission-item {
            margin-bottom: 0.5rem;
        }
        .permission-group {
            margin-bottom: 20px;
        }
        .permission-group-title {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #dee2e6;
            text-transform: capitalize;
        }
        .permission-action {
            font-weight: 500;
            text-transform: capitalize;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <script>
                // Redirect back to index after successful creation
                setTimeout(function() {
                    window.location.href = "{{ route('roles.index') }}";
                }, 1000); // Redirect after 1 second
            </script>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Create New Role</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('roles.store') }}" method="post" id="roleForm">
                    @csrf

                    <input type="hidden" name="project_id" value="{{ request()->project_id }}">

                    <div class="mb-3">
                        <label for="role_name" class="form-label">Role Name</label>
                        <input type="text" id="role_name" name="role_name" class="form-control" value="{{ old('role_name') }}" maxlength="100">
                        @include('inc.character-counter', 
                                [ 'inputId' => 'role_name', 
                                        'counterId' => 'role_name_char_count', 
                                        'maxLength' => 100])
                        @error('role_name')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="permissions-container" id="grouped-permissions">
                            <!-- Permissions will be populated by JavaScript -->
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                        @error('permissions')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-plus me-2"></i> Add Role
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get permissions and organize them by entity (the part after the underscore)
            const permissions = [];
            
            @foreach(\App\Permission::all() as $permission)
                permissions.push({
                    id: {{ $permission->id }},
                    key: "{{ $permission->key }}",
                    description: "{{ $permission->description }}"
                });
            @endforeach
            
            // Group permissions by entity (the second part after the underscore)
            const groupedPermissions = {};
            
            permissions.forEach(permission => {
                const parts = permission.key.split('_');
                const action = parts[0] || 'other';
                const entity = parts.length > 1 ? parts.slice(1).join('_') : 'general';
                
                if (!groupedPermissions[entity]) {
                    groupedPermissions[entity] = [];
                }
                
                groupedPermissions[entity].push({
                    ...permission,
                    action: action
                });
            });
            
            // Sort entity groups alphabetically
            const sortedEntities = Object.keys(groupedPermissions).sort();
            
            // Generate HTML
            const container = document.getElementById('grouped-permissions');
            container.innerHTML = '';
            
            sortedEntities.forEach(entity => {
                // Create entity group
                const groupDiv = document.createElement('div');
                groupDiv.className = 'permission-group';
                
                // Create group title
                const titleDiv = document.createElement('div');
                titleDiv.className = 'permission-group-title';
                titleDiv.textContent = entity.charAt(0).toUpperCase() + entity.slice(1).replace('_', ' ');
                groupDiv.appendChild(titleDiv);
                
                // Sort permissions within this entity by action
                const sortedPermissions = groupedPermissions[entity].sort((a, b) => 
                    a.action.localeCompare(b.action)
                );
                
                // Add each permission to the group
                sortedPermissions.forEach(permission => {
                    const permissionDiv = document.createElement('div');
                    permissionDiv.className = 'permission-item';
                    
                    const formattedAction = permission.action.replace(/([A-Z])/g, ' $1')
                        .toLowerCase()
                        .replace(/^./, str => str.toUpperCase());
                    
                    permissionDiv.innerHTML = `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="permissions[]" 
                                value="${permission.id}" id="permission-${permission.id}">
                            <label class="form-check-label" for="permission-${permission.id}">
                                <span class="permission-action">${formattedAction}</span> - ${permission.description}
                            </label>
                        </div>
                    `;
                    
                    groupDiv.appendChild(permissionDiv);
                });
                
                container.appendChild(groupDiv);
            });
            
            // Function to notify parent iframe about height changes
            function notifyParentAboutHeight() {
                const height = Math.max(
                    document.body.scrollHeight,
                    document.documentElement.scrollHeight,
                    document.body.offsetHeight,
                    document.documentElement.offsetHeight,
                    document.body.clientHeight,
                    document.documentElement.clientHeight
                );
                
                // Add small buffer to avoid scrollbars
                const heightWithBuffer = height + 30;
                
                // Send message to parent
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'resize',
                        height: heightWithBuffer,
                        iframeId: 'roles-iframe'
                    }, '*');
                }
            }
            
            // Initial height notification
            notifyParentAboutHeight();
            
            // Set up mutation observer to detect content changes
            const observer = new MutationObserver(function() {
                notifyParentAboutHeight();
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });
            
            // Additional triggers for height recalculation
            window.addEventListener('load', notifyParentAboutHeight);
            window.addEventListener('resize', notifyParentAboutHeight);
        });
    </script>
</body>
</html>


