@extends('layouts.app2')


<!-- Add Font Awesome CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

@include('inc.breadcrumbStyle')

@section('dashboard')
{{-- @foreach($teams as $team)
        <li>
            <a href="{{ route('teams.edit', [$team]) }}">
             {{ $team->team_name }} 
            </a>
                     
        </li>
@endforeach --}}
@endsection

@include('inc.navbar')

@section('content')
<!-- Add Font Awesome CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<!-- Header Section -->
<div class="mb-4">
    <div class="d-flex align-items-center">
        <h1 class="mb-0"><a href="{{ route('team.index') }}" class="breadcrumb-link">All Teams</a></h1>
        <h1 class="mb-0"><span class="breadcrumb-separator">/</span></h1>
        <h1 class="mb-0 breadcrumb-current">{{ $teams->team_name }}</h1>
    </div>
</div>

@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

<!-- Team Roles Information -->
<div class="permission-info mb-4">
    <h6><i class="fas fa-info-circle me-2"></i>Team Roles & Permissions</h6>
    <div class="row">
        <div class="col-md-6">
            <strong><i class="fas fa-crown text-warning me-1"></i>Team Manager:</strong>
            <ul class="mb-0 mt-1">
                <li>Add and remove team members</li>
                <li>Change member roles</li>
                <li>View all team information</li>
            </ul>
        </div>
        <div class="col-md-6">
            <strong><i class="fas fa-users text-secondary me-1"></i>Team Member:</strong>
            <ul class="mb-0 mt-1">
                <li>View team information</li>
                <li>Accept team invitations</li>
            </ul>
        </div>
    </div>
</div>

@php
    // Check if current user can manage this team
    $currentUser = Auth::user();
    $canManageTeam = $currentUser->isAdmin() || 
        \App\TeamMapping::where('team_name', $teams->team_name)
            ->where('username', $currentUser->username)
            ->where('role_name', 'Team Manager')
            ->where('invitation_status', 'accepted')
            ->exists();
            
    $teamRoles = ['Team Manager', 'Team Member'];
@endphp

<br>
<!-- The Team -->
<div class="table-responsive">
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Username</th>  
                <th>Role</th>
                @if($canManageTeam)
                    <th>Actions</th>
                @endif
            </tr>
        </thead>
        <tbody>
            @forelse($teammappings as $teammapping)
                <tr> 
                    <td>
                        {{ $teammapping->username }}
                        @if($teammapping->invitation_status === 'pending')
                            <i class="fas fa-clock text-warning ms-2" title="Pending Invitation"></i>
                        @elseif($teammapping->invitation_status === 'declined')
                            <i class="fas fa-times-circle text-danger ms-2" title="Declined"></i>
                        @elseif($teammapping->invitation_status === 'accepted')
                            <i class="fas fa-check-circle text-success ms-2" title="Active"></i>
                        @endif
                    </td>
                    <td class="role-cell">
                        @if($canManageTeam && $teammapping->invitation_status === 'accepted')
                            <select name="role_name" class="form-select form-select-sm role-select" data-teammapping-id="{{ $teammapping->teammapping_id }}">
                                @foreach($teamRoles as $role)
                                    <option value="{{ $role }}" {{ $teammapping->role_name == $role ? 'selected' : '' }}>
                                        {{ $role }}
                                    </option>
                                @endforeach
                            </select>
                        @else
                            <span class="badge {{ $teammapping->role_name === 'Team Manager' ? 'bg-primary' : 'bg-secondary' }}">
                                {{ $teammapping->role_name }}
                            </span>
                        @endif
                    </td>
                    @if($canManageTeam)
                        <td>
                            @if($teammapping->invitation_status === 'pending')
                                @if($teammapping->username === Auth::user()->username)
                                    <form action="{{ route('teammapping.accept', $teammapping) }}" method="POST" class="d-inline me-2">
                                        @csrf
                                        <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Accept invitation to join {{ $teams->team_name }}?')">
                                            Accept Invitation
                                        </button>
                                    </form>
                                @endif
                                <form action="{{ route('teammapping.destroy', $teammapping) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('Cancel invitation for {{ $teammapping->username }}?')">
                                        Cancel Invitation
                                    </button>
                                </form>
                            @else
                                <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $teammapping->teammapping_id }}">
                                    Remove
                                </button>
                            @endif
                        </td>
                    @elseif($teammapping->invitation_status === 'pending' && $teammapping->username === Auth::user()->username)
                        <td>
                            <form action="{{ route('teammapping.accept', $teammapping) }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Accept invitation to join {{ $teams->team_name }}?')">
                                    Accept Invitation
                                </button>
                            </form>
                        </td>
                    @elseif(!$canManageTeam)
                        <td>
                            <span class="text-muted">-</span>
                        </td>
                    @endif
                </tr>                
                <!-- Delete Confirmation Modal for each team member -->
                @if($canManageTeam)
                    <div class="modal fade" id="deleteModal{{ $teammapping->teammapping_id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $teammapping->teammapping_id }}" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteModalLabel{{ $teammapping->teammapping_id }}">Confirm Team Member Removal</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    Are you sure you want to remove <strong>{{ $teammapping->username }}</strong> from this team?
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <form action="{{ route('teammapping.destroy', $teammapping) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger">Remove</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            @empty
                <tr>
                    <td colspan="{{ $canManageTeam ? '3' : '2' }}">No team members added yet</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>
<br><br><br>

@if($canManageTeam)
    <a href="{{ route('teammappings.create', ['teams' => $teams, 'team_name' => $teams->team_name]) }}" class="btn btn-success">Add New Member</a>
@endif

<style>
    .table {
        background-color: white;
        border-radius: 0.25rem;
    }
    .table th {
        background-color: #f8f9fa;
    }
    .success-alert {
        padding: 1rem;
        margin-bottom: 1rem;
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
        border-radius: 0.25rem;
    }
    /* Notification styles */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 9999;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        animation: fadeIn 0.3s ease-out;
        max-width: 350px;
    }
    
    .notification-success {
        background-color: #28a745;
        border-left: 5px solid #1e7e34;
    }
    
    .notification-error {
        background-color: #dc3545;
        border-left: 5px solid #bd2130;
    }
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* Role badge styles */
    .badge.bg-primary {
        background-color: #0d6efd !important;
    }
    
    .badge.bg-secondary {
        background-color: #6c757d !important;
    }
    
    /* Permission info styles */
    .permission-info {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.5rem;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        border-left: 3px solid #0d6efd;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all role dropdowns (only visible to team managers)
    const roleSelects = document.querySelectorAll('.role-select');
    
    // Function to show notifications
    function showNotification(message, type) {
        // Remove any existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => {
            notification.remove();
        });
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add to body
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-10px)';
            notification.style.transition = 'all 0.3s ease-out';
            
            // Complete removal after animation
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
    
    // Add change event listener to each dropdown
    roleSelects.forEach(select => {
        select.addEventListener('change', function() {
            const teammappingId = this.getAttribute('data-teammapping-id');
            const roleName = this.value;
            const originalValue = this.getAttribute('data-original-value') || '';
            
            // Skip AJAX call if the role hasn't changed
            if (originalValue === roleName) {
                return;
            }
            
            // Validate role before sending
            if (!['Team Manager', 'Team Member'].includes(roleName)) {
                showNotification('Invalid role selected. Please choose Team Manager or Team Member.', 'error');
                this.value = originalValue; // Reset to original value
                return;
            }
            
            // Store current selection as original for next comparison
            this.setAttribute('data-original-value', roleName);
            
            // Create form data
            const formData = new FormData();
            formData.append('role_name', roleName);
            formData.append('_token', '{{ csrf_token() }}');
            
            // Visual feedback - disable select during AJAX
            this.disabled = true;
            
            // Send AJAX request
            fetch(`{{ url('teammappings') }}/${teammappingId}/updateRole`, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                // Re-enable select
                this.disabled = false;
                
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || 'Failed to update role');
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                } else {
                    showNotification(data.message || 'An error occurred', 'error');
                    // Reset to original value on failure
                    this.value = originalValue;
                    this.setAttribute('data-original-value', originalValue);
                }
            })
            .catch(error => {
                // Re-enable select if still disabled
                this.disabled = false;
                console.error('Error updating role:', error);
                showNotification(error.message || 'Failed to update role. Please try again.', 'error');
                // Reset to original value on error
                this.value = originalValue;
                this.setAttribute('data-original-value', originalValue);
            });
        });
        
        // Set initial data-original-value attribute
        select.setAttribute('data-original-value', select.value);
    });
});
</script>
   
@endsection

