<?php

namespace App\Http\Controllers;

use App\UserStory;
use App\Task;
use App\Project;
use App\Sprint;
use App\Status;
use Illuminate\Http\Request;

class BacklogController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($project_id)
    {
        \Log::info('BacklogController@index started', ['project_id' => $project_id]);
        
        // Get the project
        $project = Project::findOrFail($project_id);
        \Log::info('Project found', ['project' => $project->toArray()]);
        
        // First check for sprints with explicit active_sprint value of 1
        $activeSprint = Sprint::where('proj_name', $project->proj_name)
            ->where('active_sprint', 1)
            ->first();
        
        \Log::info('Explicit active sprint check completed', [
            'proj_name' => $project->proj_name,
            'active_sprint_found' => $activeSprint ? true : false,
            'active_sprint_data' => $activeSprint ? $activeSprint->toArray() : null
        ]);

        // If no active sprint found, check sprints with NULL active_sprint value
        if (!$activeSprint) {
            \Log::info('No explicit active sprint found, checking NULL active_sprint sprints');
            $currentDate = \Carbon\Carbon::now();
            
            // Find sprint with NULL active_sprint where current date is between start and end dates
            $dateRangeSprint = Sprint::where('proj_name', $project->proj_name)
                ->whereNull('active_sprint')
                ->where('start_sprint', '<=', $currentDate)
                ->where('end_sprint', '>=', $currentDate)
                ->first();
            
            if ($dateRangeSprint) {
                // Set this sprint as active
                $dateRangeSprint->active_sprint = 1;
                $dateRangeSprint->save();
                $activeSprint = $dateRangeSprint;
                
                \Log::info('Found and activated date range sprint', [
                    'sprint_id' => $dateRangeSprint->sprint_id,
                    'start_date' => $dateRangeSprint->start_sprint,
                    'end_date' => $dateRangeSprint->end_sprint
                ]);
            }
        }
        
        // Active sprint ID for filtering (only use if sprint is active)
        $activeSprintId = ($activeSprint && $activeSprint->active_sprint == 1) ? $activeSprint->sprint_id : null;
        \Log::info('Active sprint ID determined', ['active_sprint_id' => $activeSprintId]);
        
        // Get user stories based on the active sprint status
        if ($activeSprintId) {
            \Log::info('Processing user stories with active sprint', ['active_sprint_id' => $activeSprintId]);
            
            // Get "Done" status ID for this project
            $doneStatus = Status::where('project_id', $project_id)
                ->where('title', 'Done')
                ->first();
            
            // Get all user stories for this project that are not "Done"
            $allUserStoriesQuery = UserStory::where('proj_id', $project_id);
            if ($doneStatus) {
                $allUserStoriesQuery->where('status_id', '!=', $doneStatus->id);
            }
            $allUserStories = $allUserStoriesQuery->get();
            
            \Log::info('All non-done user stories retrieved', [
                'total_user_stories' => $allUserStories->count(),
                'user_story_ids' => $allUserStories->pluck('u_id')->toArray(),
                'done_status_id' => $doneStatus ? $doneStatus->id : null
            ]);
            
            // Filter user stories that are not in the active sprint or have tasks not in the active sprint
            $userStories = collect();
            
            foreach ($allUserStories as $userStory) {
                \Log::debug('Processing user story', [
                    'user_story_id' => $userStory->u_id,
                    'user_story_sprint_id' => $userStory->sprint_id,
                    'active_sprint_id' => $activeSprintId
                ]);
                
                // If user story is not in active sprint, add it directly
                if ($userStory->sprint_id != $activeSprintId) {
                    \Log::debug('User story not in active sprint - adding to collection', [
                        'user_story_id' => $userStory->u_id
                    ]);
                    $userStories->push($userStory);
                } else {
                    \Log::debug('User story is in active sprint - checking tasks', [
                        'user_story_id' => $userStory->u_id
                    ]);
                    
                    // Check if any tasks of this user story are not in the active sprint
                    $tasksNotInSprint = Task::where('userstory_id', $userStory->u_id)
                        ->where(function($query) use ($activeSprintId) {
                            $query->where('sprint_id', '!=', $activeSprintId)
                                ->orWhereNull('sprint_id');
                        })
                        ->exists();
                    
                    \Log::debug('Tasks not in sprint check completed', [
                        'user_story_id' => $userStory->u_id,
                        'has_tasks_not_in_sprint' => $tasksNotInSprint
                    ]);
                    
                    if ($tasksNotInSprint) {
                        \Log::debug('User story has tasks not in active sprint - adding to collection', [
                            'user_story_id' => $userStory->u_id
                        ]);
                        $userStories->push($userStory);
                    }
                }
            }
            
            \Log::info('User stories filtering completed with active sprint', [
                'filtered_user_stories_count' => $userStories->count(),
                'filtered_user_story_ids' => $userStories->pluck('u_id')->toArray()
            ]);
            
        } else {
            \Log::info('No active sprint - getting backlog user stories');
            
            // Get "Done" status ID for this project
            $doneStatus = Status::where('project_id', $project_id)
                ->where('title', 'Done')
                ->first();
            
            // If no active sprint, get all user stories in the backlog that are not "Done"
            $userStoriesQuery = UserStory::where('proj_id', $project_id)
                ->where(function($query) {
                    $query->where('sprint_id', 0)
                        ->orWhereNull('sprint_id');
                });
            
            if ($doneStatus) {
                $userStoriesQuery->where('status_id', '!=', $doneStatus->id);
            }
            
            $userStories = $userStoriesQuery->get();
            
            \Log::info('Backlog non-done user stories retrieved', [
                'backlog_user_stories_count' => $userStories->count(),
                'backlog_user_story_ids' => $userStories->pluck('u_id')->toArray(),
                'done_status_id' => $doneStatus ? $doneStatus->id : null
            ]);
        }
            
        // Get all tasks related to these user stories, filtering out those in the active sprint and those that are "done"
        \Log::info('Starting task retrieval for user stories');
        $tasksByUserStory = [];
        
        // Get "done" status for tasks
        $doneTaskStatus = Status::where('project_id', $project_id)
            ->where('slug', 'done')
            ->first();
        
        foreach ($userStories as $userStory) {
            \Log::debug('Processing tasks for user story', ['user_story_id' => $userStory->u_id]);
            
            if ($activeSprintId) {
                // Only include tasks not in the active sprint and not "done"
                $tasksQuery = Task::where('userstory_id', $userStory->u_id)
                    ->where(function($query) use ($activeSprintId) {
                        $query->where('sprint_id', '!=', $activeSprintId)
                            ->orWhereNull('sprint_id')
                            ->orWhere('sprint_id', 0);
                    });
                    
                if ($doneTaskStatus) {
                    $tasksQuery->where('status_id', '!=', $doneTaskStatus->id);
                }
                
                $tasksByUserStory[$userStory->u_id] = $tasksQuery->get();
                
                \Log::debug('Tasks filtered for active sprint and done status', [
                    'user_story_id' => $userStory->u_id,
                    'active_sprint_id' => $activeSprintId,
                    'done_task_status_id' => $doneTaskStatus ? $doneTaskStatus->id : null,
                    'tasks_count' => $tasksByUserStory[$userStory->u_id]->count(),
                    'task_ids' => $tasksByUserStory[$userStory->u_id]->pluck('id')->toArray()
                ]);
                
            } else {
                // Include all tasks if no active sprint but exclude "done" tasks
                $tasksQuery = Task::where('userstory_id', $userStory->u_id);
                
                if ($doneTaskStatus) {
                    $tasksQuery->where('status_id', '!=', $doneTaskStatus->id);
                }
                
                $tasksByUserStory[$userStory->u_id] = $tasksQuery->get();
                
                \Log::debug('All tasks retrieved (no active sprint) excluding done tasks', [
                    'user_story_id' => $userStory->u_id,
                    'done_task_status_id' => $doneTaskStatus ? $doneTaskStatus->id : null,
                    'tasks_count' => $tasksByUserStory[$userStory->u_id]->count(),
                    'task_ids' => $tasksByUserStory[$userStory->u_id]->pluck('id')->toArray()
                ]);
            }
        }
        
        \Log::info('Task retrieval completed', [
            'total_user_stories_with_tasks' => count($tasksByUserStory),
            'total_tasks_retrieved' => collect($tasksByUserStory)->flatten()->count()
        ]);
        
        \Log::info('BacklogController@index completed successfully', [
            'project_id' => $project_id,
            'active_sprint_id' => $activeSprintId,
            'user_stories_count' => $userStories->count(),
            'tasks_by_user_story_count' => count($tasksByUserStory),
            'note' => 'Filtered out user stories with Done status and tasks with done status'
        ]);
        
        return view('backlogTest.index', [
            'project' => $project,
            'userStories' => $userStories,
            'tasksByUserStory' => $tasksByUserStory,
            'activeSprint' => $activeSprint
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
