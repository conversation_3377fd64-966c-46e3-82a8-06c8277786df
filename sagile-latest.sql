


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
SET NAMES utf8mb4;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table attachments
# ------------------------------------------------------------

DROP TABLE IF EXISTS `attachments`;

CREATE TABLE `attachments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `file_path` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table boards
# ------------------------------------------------------------

DROP TABLE IF EXISTS `boards`;

CREATE TABLE `boards` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `boardId` int NOT NULL,
  `totalTaskFilter` int NOT NULL,
  `tasksDoneFilter` int NOT NULL,
  `slug` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table bugscore
# ------------------------------------------------------------

DROP TABLE IF EXISTS `bugscore`;

CREATE TABLE `bugscore` (
  `id` int NOT NULL AUTO_INCREMENT,
  `project_id` int NOT NULL,
  `severity_weight` decimal(5,2) NOT NULL,
  `status_weight` decimal(5,2) NOT NULL,
  `due_weight` decimal(5,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table bugtrack
# ------------------------------------------------------------

DROP TABLE IF EXISTS `bugtrack`;

CREATE TABLE `bugtrack` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `project_id` bigint unsigned DEFAULT NULL,
  `title` varchar(191) NOT NULL,
  `description` text NOT NULL,
  `severity` varchar(191) NOT NULL DEFAULT 'medium',
  `status` varchar(191) NOT NULL DEFAULT 'open',
  `flow` varchar(191) DEFAULT NULL,
  `expected_results` text,
  `actual_results` text,
  `attachment` varchar(191) DEFAULT NULL,
  `assigned_to` bigint unsigned DEFAULT NULL,
  `reported_by` bigint unsigned NOT NULL,
  `due_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table burndownchart
# ------------------------------------------------------------

DROP TABLE IF EXISTS `burndownchart`;

CREATE TABLE `burndownchart` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `task_name` varchar(191) NOT NULL,
  `description` text,
  `story_points` smallint unsigned NOT NULL DEFAULT '0',
  `due_date` date DEFAULT NULL,
  `status` enum('Not Started','In Progress','Completed') NOT NULL DEFAULT 'Not Started',
  `user_name` varchar(191) NOT NULL,
  `task_id` bigint unsigned NOT NULL,
  `proj_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table calendar
# ------------------------------------------------------------

DROP TABLE IF EXISTS `calendar`;

CREATE TABLE `calendar` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `status` varchar(191) NOT NULL DEFAULT 'pending',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table charts
# ------------------------------------------------------------

DROP TABLE IF EXISTS `charts`;

CREATE TABLE `charts` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `boardId` int NOT NULL,
  `sprintname` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `storyPointsTotal` double NOT NULL,
  `tasksTotal` double NOT NULL,
  `tasksDone` double NOT NULL,
  `storyPointsDone` double NOT NULL,
  `startDate` date NOT NULL,
  `endDate` date NOT NULL,
  `sprintDay` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table cig
# ------------------------------------------------------------

DROP TABLE IF EXISTS `cig`;

CREATE TABLE `cig` (
  `cig_id` int NOT NULL AUTO_INCREMENT,
  `sprint_id` bigint unsigned NOT NULL,
  `u_id` bigint unsigned NOT NULL,
  `general_nfr_id` bigint unsigned NOT NULL,
  `specific_nfr_id` int NOT NULL,
  `proj_id` bigint unsigned NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`cig_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table coding_standards
# ------------------------------------------------------------

DROP TABLE IF EXISTS `coding_standards`;

CREATE TABLE `coding_standards` (
  `codestand_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `codestand_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`codestand_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `coding_standards` WRITE;
/*!40000 ALTER TABLE `coding_standards` DISABLE KEYS */;

INSERT INTO `coding_standards` (`codestand_id`, `codestand_name`, `created_at`, `updated_at`) VALUES
	(1, 'simpleStandard', '2025-05-23 18:44:49', '2025-05-23 18:44:49');

/*!40000 ALTER TABLE `coding_standards` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table comments
# ------------------------------------------------------------

DROP TABLE IF EXISTS `comments`;

CREATE TABLE `comments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `forum_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `content` text NOT NULL,
  `parent_comment_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `comments` WRITE;
/*!40000 ALTER TABLE `comments` DISABLE KEYS */;

INSERT INTO `comments` (`id`, `forum_id`, `user_id`, `content`, `parent_comment_id`, `created_at`, `updated_at`) VALUES
	(1, 4, 1, 'comment 1', NULL, '2024-06-19 06:12:53', '2024-06-19 06:12:53'),
	(2, 5, 32, '<p>forum comment</p>', NULL, '2025-08-18 09:22:47', '2025-08-18 09:22:47');

/*!40000 ALTER TABLE `comments` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table defect_features
# ------------------------------------------------------------

DROP TABLE IF EXISTS `defect_features`;

CREATE TABLE `defect_features` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table diagram_components
# ------------------------------------------------------------

DROP TABLE IF EXISTS `diagram_components`;

CREATE TABLE `diagram_components` (
  `id` varchar(36) NOT NULL,
  `node_id` varchar(255) NOT NULL,
  `diagram_id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `version` varchar(50) DEFAULT NULL,
  `deletable` tinyint DEFAULT '1',
  `created_by` varchar(255) DEFAULT NULL,
  `last_updated_by` varchar(255) DEFAULT NULL,
  `preconditions` json DEFAULT NULL,
  `postconditions` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `diagram_components` WRITE;
/*!40000 ALTER TABLE `diagram_components` DISABLE KEYS */;

INSERT INTO `diagram_components` (`id`, `node_id`, `diagram_id`, `name`, `description`, `version`, `deletable`, `created_by`, `last_updated_by`, `preconditions`, `postconditions`) VALUES
	('4681cb07-d1af-49f6-b785-7d573e34a140', 'actor_1', '30', 'Project Manager', 'This use case is ...', '1.0', 1, 'system', 'system', '[]', '[]'),
	('505128c8-4dae-40b8-93bf-2051901f6b38', 'actor_2', '54', 'Developer', 'This use case is hihi ...', '1.0', 1, 'system', 'system', '[\"a\"]', '[\"b\"]'),
	('52fa5a00-95e9-4081-ae7b-977d4b187571', 'usecase_1', '37', 'Perform UAT tests', 'Allow project manager to perform uat tests', '1.1', 1, 'system', 'system', '[\"Project Manager is looed into the system\"]', '[\"Marked passed or fail\"]'),
	('bdbfc950-4003-4495-9ca1-3404522ea0d5', 'usecase_1', '30', 'Eat', 'This use case is funny!', '1.2', 1, 'system', 'system', '[\"Precondition1\",\"Pre2\"]', '[\"Post1\"]'),
	('c1a81b47-7a00-4bde-b1ab-aa4cb247d288', 'usecase_2', '30', 'drinkibng', 'This use case is ...', '1.0', 1, 'system', 'system', '[\"asdf\"]', '[\"asdf\"]'),
	('cf3b7feb-c685-40bf-962b-f0acc7b64766', 'usecase_3', '30', 'full', 'This use case is asdf ...', '1.0', 1, 'system', 'system', '[]', '[]'),
	('cfd99f5e-67a3-4b1a-9ada-7de4c6adcf02', 'usecase_3', '54', 'Update the status of tasks assigned to me', 'This use case is a ...', '1.0', 1, 'system', 'system', '[]', '[]'),
	('e68ce82f-f7e3-4d51-9b59-0a18d8b628d4', 'usecase_2', '44', 'Update test case', 'This use case is ...', '1.0', 1, 'system', 'system', '[]', '[]');

/*!40000 ALTER TABLE `diagram_components` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table diagram_usecase_specification
# ------------------------------------------------------------

DROP TABLE IF EXISTS `diagram_usecase_specification`;

CREATE TABLE `diagram_usecase_specification` (
  `id` varchar(36) NOT NULL,
  `usecase_id` varchar(36) NOT NULL,
  `type` varchar(50) NOT NULL DEFAULT 'NORMAL',
  `name` varchar(255) NOT NULL,
  `entry_point` varchar(255) DEFAULT NULL,
  `exit_point` varchar(255) DEFAULT NULL,
  `steps` varchar(4000) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `diagram_usecase_specification` WRITE;
/*!40000 ALTER TABLE `diagram_usecase_specification` DISABLE KEYS */;

INSERT INTO `diagram_usecase_specification` (`id`, `usecase_id`, `type`, `name`, `entry_point`, `exit_point`, `steps`) VALUES
	('33938508-ec18-4751-9b6d-0b1979052c2e', 'e68ce82f-f7e3-4d51-9b59-0a18d8b628d4', 'NORMAL', 'Normal flow', NULL, NULL, '[{\"id\":\"step-1\",\"description\":\"\"}]'),
	('42026096-551b-468e-b0e8-11516dd9a8e0', 'cfd99f5e-67a3-4b1a-9ada-7de4c6adcf02', 'NORMAL', 'Normal flow', NULL, NULL, '[{\"id\":\"step-1\",\"description\":\"\"}]'),
	('ecf74419-fcfc-4a00-94c9-574af72f688f', '505128c8-4dae-40b8-93bf-2051901f6b38', 'NORMAL', 'Normal flow', NULL, NULL, '[{\"id\":\"step-1\",\"description\":\"c\"}]');

/*!40000 ALTER TABLE `diagram_usecase_specification` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table diagrams
# ------------------------------------------------------------

DROP TABLE IF EXISTS `diagrams`;

CREATE TABLE `diagrams` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `project_id` varchar(36) NOT NULL,
  `diagram_element` json NOT NULL,
  `original_plantuml` text NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `diagrams` WRITE;
/*!40000 ALTER TABLE `diagrams` DISABLE KEYS */;

INSERT INTO `diagrams` (`id`, `name`, `project_id`, `diagram_element`, `original_plantuml`) VALUES
	('0bd9691a-4da3-44cd-9cbc-c78f69f01459', 'teatat', '37', '{\"edges\":[{\"id\":\"eactor_1-usecase_1\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecase_1\",\"sourceHandle\":\"right\",\"targetHandle\":\"left\"}],\"nodes\":[{\"id\":\"actor_1\",\"data\":{\"type\":\"actor\",\"label\":\"Project Manager\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":80,\"height\":148},\"position\":{\"x\":-100,\"y\":0}},{\"id\":\"usecase_1\",\"data\":{\"type\":\"usecase\",\"label\":\"perform UAT tests\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":250,\"y\":50}},{\"id\":\"package_1\",\"data\":{\"type\":\"package\",\"label\":\"teatat \",\"width\":300,\"height\":230},\"type\":\"package\",\"height\":320,\"zIndex\":-11,\"measured\":{\"width\":300,\"height\":320},\"position\":{\"x\":200,\"y\":0}}]}', '\n@startuml\nleft to right direction\n\nactor \"Project Manager\"\n\nrectangle teatat {\n\tusecase \"perform UAT tests\"\n}\n\n\"Project Manager\" --> \"perform UAT tests\"\n\n@enduml'),
	('31c91411-bb59-431e-ae89-769727d69384', 'PSM2 Demo Project', '54', '{\"edges\":[{\"id\":\"eactor_1-usecase_2\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecase_2\",\"sourceHandle\":\"right\",\"targetHandle\":\"left\"},{\"id\":\"eactor_2-usecase_3\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_2\",\"target\":\"usecase_3\",\"sourceHandle\":\"left\",\"targetHandle\":\"right\"}],\"nodes\":[{\"id\":\"actor_1\",\"data\":{\"type\":\"actor\",\"label\":\"Project Manager\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":117,\"height\":148},\"position\":{\"x\":-241.09772423025436,\"y\":17.295850066934406},\"selected\":false},{\"id\":\"actor_2\",\"data\":{\"type\":\"actor\",\"label\":\"Developer\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":80,\"height\":124},\"position\":{\"x\":475.2878179384204,\"y\":143.66800535475232},\"selected\":false},{\"id\":\"usecase_1\",\"data\":{\"type\":\"usecase\",\"label\":\"I LOVE FEMBOYS\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":130.32252553818066,\"y\":399.3590290441487},\"selected\":false},{\"id\":\"usecase_2\",\"data\":{\"type\":\"usecase\",\"label\":\"UC01: assign tasks to developers\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":125.82222272939572,\"y\":79.07791088582606},\"selected\":false},{\"id\":\"usecase_3\",\"data\":{\"type\":\"usecase\",\"label\":\"update the status of tasks assigned to me\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":130.74966532797856,\"y\":261.78045515394916},\"selected\":false},{\"id\":\"package_1\",\"data\":{\"type\":\"package\",\"label\":\"PSM2_Demo_Project \",\"width\":300,\"height\":590},\"type\":\"package\",\"height\":680,\"zIndex\":-11,\"dragging\":false,\"measured\":{\"width\":300,\"height\":680},\"position\":{\"x\":58.90227576974564,\"y\":17.295850066934406},\"selected\":false}]}', '\n@startuml\nleft to right direction\n\nactor \"Project Manager\"\nactor \"Developer\"\n\nrectangle PSM2_Demo_Project {\n\tusecase \"create new projects\"\n\tusecase \"assign tasks to developers\"\n\tusecase \"update the status of tasks assigned to me\"\n\tusecase \"manage team activities in an organised space\"\n}\n\n\"Project Manager\" --> \"assign tasks to developers\"\n\"update the status of tasks assigned to me\" <-- \"Developer\"\n\"Project Manager\" --> \"manage team activities in an organised space\"\n\"manage team activities in an organised space\" .> \"create new projects\" : include\n\n@enduml'),
	('763df313-5f9f-4e65-8c1b-f24309d97c64', 'TestAddNewCount', '41', '{\"edges\":[{\"id\":\"eactor_1-usecase_1\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecase_1\",\"sourceHandle\":\"right\",\"targetHandle\":\"left\"},{\"id\":\"eactor_2-usecase_2\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_2\",\"target\":\"usecase_2\",\"sourceHandle\":\"left\",\"targetHandle\":\"right\"}],\"nodes\":[{\"id\":\"actor_1\",\"data\":{\"type\":\"actor\",\"label\":\"Project Manager\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":117,\"height\":148},\"position\":{\"x\":-100,\"y\":0}},{\"id\":\"actor_2\",\"data\":{\"type\":\"actor\",\"label\":\"Developer\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":80,\"height\":124},\"position\":{\"x\":600,\"y\":120}},{\"id\":\"usecase_1\",\"data\":{\"type\":\"usecase\",\"label\":\"Create new projects\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":250,\"y\":50}},{\"id\":\"usecase_2\",\"data\":{\"type\":\"usecase\",\"label\":\"Test User Story\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":250,\"y\":170}},{\"id\":\"package_1\",\"data\":{\"type\":\"package\",\"label\":\"TestAddNewCount \",\"width\":300,\"height\":350},\"type\":\"package\",\"height\":440,\"zIndex\":-11,\"measured\":{\"width\":300,\"height\":440},\"position\":{\"x\":200,\"y\":0}}]}', '\n@startuml\nleft to right direction\n\nactor \"Project Manager\"\nactor \"Developer\"\n\nrectangle TestAddNewCount {\n\tusecase \"Create new projects\"\n\tusecase \"Test User Story\"\n}\n\n\"Project Manager\" --> \"Create new projects\"\n\"Test User Story\" <-- \"Developer\"\n\n@enduml'),
	('f4ba96d4-9685-481b-80ff-67999a9a5c49', 'FULL_SYSTEM_TESTING', '44', '{\"edges\":[{\"id\":\"eactor_1-usecase_1\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecase_1\",\"selected\":false,\"sourceHandle\":\"right\",\"targetHandle\":\"left\"},{\"id\":\"eusecase_1-usecase_2-include\",\"data\":{\"type\":\"include\"},\"type\":\"include\",\"label\":\"«include»\",\"style\":{\"strokeDasharray\":\"5 5\"},\"source\":\"usecase_1\",\"target\":\"usecase_2\",\"selected\":false,\"markerEnd\":{\"type\":\"arrowclosed\",\"color\":\"#b1b1b7\",\"width\":20,\"height\":20},\"labelStyle\":{\"fill\":\"#000\",\"fontFamily\":\"monospace\"},\"sourceHandle\":\"right\",\"targetHandle\":\"left\"},{\"id\":\"xy-edge__actor_1right-usecaseshape-1751536844610left\",\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecaseshape-1751536844610\",\"selected\":false,\"sourceHandle\":\"right\",\"targetHandle\":\"left\"},{\"id\":\"xy-edge__actor_1-1751536872861-1751536877908top-actor_1bottom\",\"type\":\"generalization\",\"source\":\"actor_1-1751536872861-1751536877908\",\"target\":\"actor_1\",\"sourceHandle\":\"top\",\"targetHandle\":\"bottom\"}],\"nodes\":[{\"id\":\"actor_1\",\"data\":{\"type\":\"actor\",\"label\":\"Project Manager\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":117,\"height\":148},\"position\":{\"x\":-162.5133973765616,\"y\":31.172479373794964},\"selected\":true},{\"id\":\"usecase_1\",\"data\":{\"type\":\"usecase\",\"label\":\"testSYSTEM\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":241.75368139223568,\"y\":55.30120481927713},\"selected\":false},{\"id\":\"usecase_2\",\"data\":{\"type\":\"usecase\",\"label\":\"Update test case\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":359.5582329317269,\"y\":235.97054886211515},\"selected\":false},{\"id\":\"package_1\",\"data\":{\"type\":\"package\",\"label\":\"FULL_SYSTEM_TESTING \",\"width\":300,\"height\":350},\"type\":\"package\",\"height\":440,\"zIndex\":-11,\"dragging\":false,\"measured\":{\"width\":300,\"height\":440},\"position\":{\"x\":198.23293172690765,\"y\":-2.9451137884872765},\"selected\":false},{\"id\":\"usecaseshape-1751536844610\",\"data\":{\"type\":\"usecase\",\"label\":\"Hello\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":5.133868808567684,\"y\":232.73092369477916},\"selected\":false},{\"id\":\"actor_1-1751536872861-1751536877908\",\"data\":{\"type\":\"actor\",\"label\":\"Project Manager\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":117,\"height\":148},\"position\":{\"x\":-313.1040285382741,\"y\":290.415029598518},\"selected\":false}]}', '\n@startuml\nleft to right direction\n\nactor \"Project Manager\"\n\nrectangle FULL_SYSTEM_TESTING {\n\tusecase \"testSYSTEM\"\n\tusecase \"Add test case\"\n}\n\n\"Project Manager\" --> \"testSYSTEM\"\n\"testSYSTEM\" .> \"Add test case\" : include\n\n@enduml'),
	('fa9151b6-f7eb-4d78-ab69-3057f7a0f81c', 'PROJECT-TAUFIQ', '30', '{\"edges\":[{\"id\":\"eactor_1-usecase_2\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecase_2\",\"selected\":false,\"sourceHandle\":\"right\",\"targetHandle\":\"left\"},{\"id\":\"eactor_1-usecase_3\",\"data\":{\"type\":\"association\"},\"type\":\"association\",\"source\":\"actor_1\",\"target\":\"usecase_3\",\"selected\":false,\"sourceHandle\":\"right\",\"targetHandle\":\"left\"},{\"id\":\"eusecase_3-usecase_1-include\",\"data\":{\"type\":\"include\"},\"type\":\"include\",\"label\":\"«include»\",\"style\":{\"strokeDasharray\":\"5 5\"},\"source\":\"usecase_3\",\"target\":\"usecase_1\",\"selected\":false,\"markerEnd\":{\"type\":\"arrowclosed\",\"color\":\"#b1b1b7\",\"width\":20,\"height\":20},\"labelStyle\":{\"fill\":\"#000\",\"fontFamily\":\"monospace\"},\"sourceHandle\":\"right\",\"targetHandle\":\"left\"}],\"nodes\":[{\"id\":\"actor_1\",\"data\":{\"type\":\"actor\",\"label\":\"Project Manager\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":117,\"height\":148},\"position\":{\"x\":-73.18922865821457,\"y\":73.46720214190093},\"selected\":false},{\"id\":\"usecase_1\",\"data\":{\"type\":\"usecase\",\"label\":\"eat\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":654.0873305244671,\"y\":412.2750849857144},\"selected\":false},{\"id\":\"usecase_2\",\"data\":{\"type\":\"usecase\",\"label\":\"drinkibng\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":276.4945278241121,\"y\":119.96866219748546},\"selected\":false},{\"id\":\"usecase_3\",\"data\":{\"type\":\"usecase\",\"label\":\"full\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":257.59477286581097,\"y\":320.379091463244},\"selected\":false},{\"id\":\"package_1\",\"data\":{\"type\":\"package\",\"label\":\"PROJECT-TAUFIQ \",\"width\":300,\"height\":470},\"type\":\"package\",\"height\":560,\"zIndex\":-11,\"dragging\":false,\"measured\":{\"width\":300,\"height\":560},\"position\":{\"x\":201.08496755225872,\"y\":1.0849675522587177},\"selected\":false},{\"id\":\"usecaseshape-1754289073307\",\"data\":{\"type\":\"usecase\",\"label\":\"\"},\"type\":\"usecaseshape\",\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":-182.15787523693933,\"y\":346.8856523010229},\"selected\":false},{\"id\":\"usecase_3-1754289183726\",\"data\":{\"type\":\"usecase\",\"label\":\"full\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":154,\"height\":104},\"position\":{\"x\":566.6069747157221,\"y\":143.00324507248382},\"selected\":false},{\"id\":\"usecaseshape-1754289294410\",\"data\":{\"type\":\"rectangularactor\",\"label\":\"asdf\"},\"type\":\"usecaseshape\",\"dragging\":false,\"measured\":{\"width\":150,\"height\":100},\"position\":{\"x\":10.748225133463777,\"y\":318.52192390077937},\"selected\":false}]}', '\n@startuml\nleft to right direction\n\nactor \"Project Manager\"\n\nrectangle PROJECT-TAUFIQ {\n\tusecase \"eat\"\n\tusecase \"drink\"\n\tusecase \"full\"\n}\n\n\"Project Manager\" --> \"drink\"\n\"Project Manager\" --> \"full\"\n\"full\" .> \"eat\" : include\n\n@enduml');

/*!40000 ALTER TABLE `diagrams` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table failed_jobs
# ------------------------------------------------------------

DROP TABLE IF EXISTS `failed_jobs`;

CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table forum_favorites
# ------------------------------------------------------------

DROP TABLE IF EXISTS `forum_favorites`;

CREATE TABLE `forum_favorites` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `forum_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table forums
# ------------------------------------------------------------

DROP TABLE IF EXISTS `forums`;

CREATE TABLE `forums` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `category` varchar(191) NOT NULL,
  `assigned_users` longtext,
  `content` longtext NOT NULL,
  `private_forum` tinyint NOT NULL DEFAULT '0',
  `private_users` longtext,
  `image_urls` text,
  `user_id` bigint unsigned NOT NULL,
  `project_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `forums` WRITE;
/*!40000 ALTER TABLE `forums` DISABLE KEYS */;

INSERT INTO `forums` (`id`, `title`, `category`, `assigned_users`, `content`, `private_forum`, `private_users`, `image_urls`, `user_id`, `project_id`, `created_at`, `updated_at`) VALUES
	(1, 'forumnew', 'Category 2', NULL, 'forumcontent', 0, NULL, NULL, 1, 4, '2024-06-13 15:39:43', '2024-06-13 15:39:43'),
	(2, 'forum1', 'Category 1', NULL, 'content', 0, NULL, NULL, 1, 4, '2024-06-13 15:40:03', '2024-06-13 15:40:03'),
	(3, 'forum2', 'Category 3', NULL, 'content23', 0, NULL, 'https://buffer.com/library/content/images/size/w1200/2023/10/free-images.jpg', 1, 4, '2024-06-13 15:42:13', '2024-06-19 06:04:47'),
	(4, 'forumtitle', 'Category 2', NULL, 'forum is interesting', 0, NULL, 'https://buffer.com/library/content/images/size/w1200/2023/10/free-images.jpg', 1, 4, '2024-06-19 06:12:16', '2024-06-19 06:12:43'),
	(5, 'Forum Test', 'Project Planning', NULL, 'Test for forum', 0, NULL, NULL, 32, 53, '2025-08-18 09:22:28', '2025-08-18 09:22:28');

/*!40000 ALTER TABLE `forums` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table generalnfr
# ------------------------------------------------------------

DROP TABLE IF EXISTS `generalnfr`;

CREATE TABLE `generalnfr` (
  `general_nfr_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `general_nfr` varchar(255) NOT NULL,
  `general_nfr_desc` varchar(255) DEFAULT NULL,
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`general_nfr_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `generalnfr` WRITE;
/*!40000 ALTER TABLE `generalnfr` DISABLE KEYS */;

INSERT INTO `generalnfr` (`general_nfr_id`, `general_nfr`, `general_nfr_desc`, `created_by`, `created_at`, `updated_at`) VALUES
	(1, 'testRequirement', 'asd', 4, '2025-05-23 11:55:57', '2025-05-23 11:55:57');

/*!40000 ALTER TABLE `generalnfr` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table mappings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `mappings`;

CREATE TABLE `mappings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ustory_id` int NOT NULL,
  `type_NFR` int NOT NULL,
  `id_NFR` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table migrations
# ------------------------------------------------------------

DROP TABLE IF EXISTS `migrations`;

CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
	(1, '2014_10_12_100000_create_password_resets_table', 1);

/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table nfr
# ------------------------------------------------------------

DROP TABLE IF EXISTS `nfr`;

CREATE TABLE `nfr` (
  `nfr_id` int NOT NULL AUTO_INCREMENT,
  `general_nfr_id` bigint unsigned NOT NULL,
  `specific_nfr` varchar(255) NOT NULL,
  `specific_nfr_desc` varchar(255) DEFAULT NULL,
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`nfr_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `nfr` WRITE;
/*!40000 ALTER TABLE `nfr` DISABLE KEYS */;

INSERT INTO `nfr` (`nfr_id`, `general_nfr_id`, `specific_nfr`, `specific_nfr_desc`, `created_by`, `created_at`, `updated_at`) VALUES
	(1, 1, 'testRequirement', 'asd', 4, '2025-05-23 11:55:57', '2025-05-23 11:55:57');

/*!40000 ALTER TABLE `nfr` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table oauth_access_tokens
# ------------------------------------------------------------

DROP TABLE IF EXISTS `oauth_access_tokens`;

CREATE TABLE `oauth_access_tokens` (
  `id` varchar(100) NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `client_id` bigint unsigned NOT NULL,
  `name` varchar(191) DEFAULT NULL,
  `scopes` text,
  `revoked` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `oauth_access_tokens` WRITE;
/*!40000 ALTER TABLE `oauth_access_tokens` DISABLE KEYS */;

INSERT INTO `oauth_access_tokens` (`id`, `user_id`, `client_id`, `name`, `scopes`, `revoked`, `created_at`, `updated_at`, `expires_at`) VALUES
	('23cb5161798ef472a08eeb431170be92ed7302d61e96f3223ed14a2436b04eef4b00c1a6a14de3aa', 29, 3, 'SAgileApp', '[]', 0, '2025-06-24 10:00:15', '2025-06-24 10:00:15', '2026-06-24 18:00:15'),
	('315f897d8143acd37f987ad1949460a47e08eaccf03d02754623b3b3a583567d4c955d26d224df18', 29, 3, 'SAgileApp', '[]', 0, '2025-06-24 10:02:33', '2025-06-24 10:02:33', '2026-06-24 18:02:33'),
	('6fd4d3d47128624db08c45e8ad021570d1104772f530b45dc207c0cc518ad28d4541157b016bf5e2', 29, 3, 'SAgileApp', '[]', 0, '2025-06-24 10:01:20', '2025-06-24 10:01:20', '2026-06-24 18:01:20');

/*!40000 ALTER TABLE `oauth_access_tokens` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table oauth_auth_codes
# ------------------------------------------------------------

DROP TABLE IF EXISTS `oauth_auth_codes`;

CREATE TABLE `oauth_auth_codes` (
  `my_row_id` bigint unsigned NOT NULL AUTO_INCREMENT /*!80023 INVISIBLE */,
  `id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `client_id` bigint unsigned NOT NULL,
  `scopes` text COLLATE utf8mb4_unicode_ci,
  `revoked` tinyint(1) NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`my_row_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table oauth_clients
# ------------------------------------------------------------

DROP TABLE IF EXISTS `oauth_clients`;

CREATE TABLE `oauth_clients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `name` varchar(191) NOT NULL,
  `secret` varchar(100) DEFAULT NULL,
  `provider` varchar(191) DEFAULT NULL,
  `redirect` text NOT NULL,
  `personal_access_client` tinyint NOT NULL,
  `password_client` tinyint NOT NULL,
  `revoked` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `oauth_clients` WRITE;
/*!40000 ALTER TABLE `oauth_clients` DISABLE KEYS */;

INSERT INTO `oauth_clients` (`id`, `user_id`, `name`, `secret`, `provider`, `redirect`, `personal_access_client`, `password_client`, `revoked`, `created_at`, `updated_at`) VALUES
	(1, NULL, 'Laravel Personal Access Client', 'dc6RvYLq5DP0FmjIhc2CboVkn97paZ9jF6uDjW6C', NULL, 'http://localhost', 1, 0, 0, '2025-06-24 09:22:19', '2025-06-24 09:22:19'),
	(2, NULL, 'Laravel Password Grant Client', 'toAJ7uTHOthD4z4QvAqb8locJS3xSKTh61LGTvHi', 'users', 'http://localhost', 0, 1, 0, '2025-06-24 09:22:19', '2025-06-24 09:22:19'),
	(3, NULL, 'Laravel Personal Access Client', 'TgDuSlXBFW3W3l4XYX682j6I6IVMjIfbqXHzkngW', NULL, 'http://localhost', 1, 0, 0, '2025-06-24 09:55:56', '2025-06-24 09:55:56'),
	(4, NULL, 'Laravel Password Grant Client', 'Gs0blk7mLq8xbpI7s9ofyF6WkvZRXdMI4mupyjHV', 'users', 'http://localhost', 0, 1, 0, '2025-06-24 09:55:56', '2025-06-24 09:55:56');

/*!40000 ALTER TABLE `oauth_clients` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table oauth_personal_access_clients
# ------------------------------------------------------------

DROP TABLE IF EXISTS `oauth_personal_access_clients`;

CREATE TABLE `oauth_personal_access_clients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `oauth_personal_access_clients` WRITE;
/*!40000 ALTER TABLE `oauth_personal_access_clients` DISABLE KEYS */;

INSERT INTO `oauth_personal_access_clients` (`id`, `client_id`, `created_at`, `updated_at`) VALUES
	(1, 1, '2025-06-24 09:22:19', '2025-06-24 09:22:19'),
	(2, 3, '2025-06-24 09:55:56', '2025-06-24 09:55:56');

/*!40000 ALTER TABLE `oauth_personal_access_clients` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table oauth_refresh_tokens
# ------------------------------------------------------------

DROP TABLE IF EXISTS `oauth_refresh_tokens`;

CREATE TABLE `oauth_refresh_tokens` (
  `id` varchar(100) NOT NULL,
  `access_token_id` varchar(100) NOT NULL,
  `revoked` tinyint NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table password_resets
# ------------------------------------------------------------

DROP TABLE IF EXISTS `password_resets`;

CREATE TABLE `password_resets` (
  `my_row_id` bigint unsigned NOT NULL AUTO_INCREMENT /*!80023 INVISIBLE */,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`my_row_id`),
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





# Dump of table performance_features
# ------------------------------------------------------------

DROP TABLE IF EXISTS `performance_features`;

CREATE TABLE `performance_features` (
  `perfeature_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `perfeature_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`perfeature_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `performance_features` WRITE;
/*!40000 ALTER TABLE `performance_features` DISABLE KEYS */;

INSERT INTO `performance_features` (`perfeature_id`, `perfeature_name`, `created_at`, `updated_at`) VALUES
	(1, 'TestAddPerf', '2024-01-04 04:28:35', '2024-01-04 04:28:35');

/*!40000 ALTER TABLE `performance_features` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table permission
# ------------------------------------------------------------

DROP TABLE IF EXISTS `permission`;

CREATE TABLE `permission` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(191) NOT NULL,
  `description` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `permission` WRITE;
/*!40000 ALTER TABLE `permission` DISABLE KEYS */;

INSERT INTO `permission` (`id`, `key`, `description`, `created_at`, `updated_at`) VALUES
	(1, 'view_kanban', 'Allows access to the kanban board', NULL, NULL),
	(2, 'view_burndown', 'Allows viewing of the burndown chart for tracking sprint progress', NULL, NULL),
	(3, 'view_backlog', 'Allows access to the product backlog items', NULL, NULL),
	(4, 'view_userstory', 'Allows viewing of user stories and their details', NULL, NULL),
	(5, 'view_forum', 'Allows access to the project discussion forum', NULL, NULL),
	(6, 'view_bugtracking', 'Allows viewing of reported bugs and tracking their status', NULL, NULL),
	(7, 'view_status', 'Allows viewing the statuses available in the project', NULL, NULL),
	(8, 'view_details', 'Allows viewing of project details', NULL, NULL),
	(9, 'view_roles', 'Allows viewing the list of users', NULL, NULL),
	(10, 'addLane_kanban', 'Allows addition of a lane in kanban board', NULL, NULL),
	(11, 'addTask_kanban', 'Allows addition of a task using kanban board', NULL, NULL),
	(12, 'editLane_kanban', 'Allows the editing of a lane name in kanban board', NULL, NULL),
	(13, 'deleteLane_kanban', 'Allows the deletion of a lane in kanban board', NULL, NULL),
	(14, 'deleteTask_kanban', 'Allows the deletion of a task in kanban board', NULL, NULL),
	(15, 'addComment_kanban', 'Allows the addition of a comment on a task in kanban board', NULL, NULL),
	(16, 'addUserStory_backlog', 'Allows the creation of userstory from backlog', NULL, NULL),
	(17, 'beginSprint_backlog', 'Allows the starting of a sprint from the backlog', NULL, NULL),
	(18, 'addToSprint_backlog', 'Allows the addition of items into the current sprint from the backlog', NULL, NULL),
	(19, 'endSprint_backlog', 'Allows the ending of current sprint from the backlog', NULL, NULL),
	(20, 'add_userstory', 'Allows creation of user story', NULL, NULL),
	(21, 'edit_userstory', 'Allows the editing of user story', NULL, NULL),
	(22, 'delete_userstory', 'Allows the deletion of a user story', NULL, NULL),
	(23, 'editStatus_userstory', 'Allows updating the user story status', NULL, NULL),
	(24, 'add_task', 'Allows creation of new tasks under user story', NULL, NULL),
	(25, 'edit_task', 'Allows editing of a task', NULL, NULL),
	(26, 'delete_task', 'Allows deletion of a task', NULL, NULL),
	(27, 'viewCalendar_task', 'Allows viewing the task calendar', NULL, NULL),
	(28, 'viewComments_task', 'Allows viewing the comments page from task', NULL, NULL),
	(29, 'add_roles', 'Allows creation of a role', NULL, NULL),
	(30, 'edit_roles', 'Allows permission editing of a role', NULL, NULL),
	(31, 'delete_roles', 'Allows deletion of a role', NULL, NULL),
	(32, 'add_status', 'Allows creation of a status', NULL, NULL),
	(33, 'edit_status', 'Allows editing status name', NULL, NULL),
	(34, 'delete_status', 'Allows deletion of a status', NULL, NULL),
	(35, 'edit_details', 'Allows editing of project details', NULL, NULL),
	(36, 'delete_details', 'Allows deletion of project', NULL, NULL),
	(37, 'view_sprintArchive', 'Allows access to the sprint archive', NULL, NULL),
	(38, 'viewKanbanArchive_sprintArchive', 'Allows viewing the kanban archive', NULL, NULL),
	(39, 'viewBurndownArchive_sprintArchive', 'Allows viewing the burndown archive', NULL, NULL),
	(40, 'updateTaskStatus_kanban', 'Allows updating task status by dragging and dropping in the Kanban board', NULL, NULL),
	(41, 'view_task', 'Allows viewing task list from user story', NULL, NULL),
	(42, 'share_details', 'Allows sharing project for public view', NULL, NULL),
	(43, 'editTask_kanban', 'Allows the editing of a task using kanban board', NULL, NULL),
	(44, 'updateUserRole_roles', 'Allows updating user role in the project', NULL, NULL);

/*!40000 ALTER TABLE `permission` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table permission_role
# ------------------------------------------------------------

DROP TABLE IF EXISTS `permission_role`;

CREATE TABLE `permission_role` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `permission_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1929 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `permission_role` WRITE;
/*!40000 ALTER TABLE `permission_role` DISABLE KEYS */;

INSERT INTO `permission_role` (`id`, `permission_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(1, 1, 1, NULL, NULL),
	(2, 2, 1, NULL, NULL),
	(3, 5, 1, NULL, NULL),
	(4, 6, 1, NULL, NULL),
	(5, 1, 2, NULL, NULL),
	(7, 3, 2, NULL, NULL),
	(8, 4, 2, NULL, NULL),
	(10, 6, 2, NULL, NULL),
	(11, 7, 2, NULL, NULL),
	(13, 9, 2, NULL, NULL),
	(14, 2, 2, NULL, NULL),
	(15, 1, 5, NULL, NULL),
	(16, 2, 5, NULL, NULL),
	(31, 30, 2, NULL, NULL),
	(32, 37, 2, NULL, NULL),
	(47, 29, 2, NULL, NULL),
	(48, 31, 2, NULL, NULL),
	(49, 16, 6, NULL, NULL),
	(50, 17, 6, NULL, NULL),
	(51, 3, 6, NULL, NULL),
	(52, 6, 6, NULL, NULL),
	(53, 2, 6, NULL, NULL),
	(54, 12, 6, NULL, NULL),
	(55, 1, 6, NULL, NULL),
	(56, 30, 6, NULL, NULL),
	(57, 9, 6, NULL, NULL),
	(58, 39, 6, NULL, NULL),
	(59, 38, 6, NULL, NULL),
	(60, 27, 6, NULL, NULL),
	(61, 28, 6, NULL, NULL),
	(62, 23, 6, NULL, NULL),
	(63, 4, 6, NULL, NULL),
	(65, 15, 2, NULL, NULL),
	(66, 10, 2, NULL, NULL),
	(67, 11, 2, NULL, NULL),
	(68, 18, 2, NULL, NULL),
	(69, 16, 2, NULL, NULL),
	(70, 17, 2, NULL, NULL),
	(71, 19, 2, NULL, NULL),
	(72, 13, 2, NULL, NULL),
	(73, 14, 2, NULL, NULL),
	(74, 12, 2, NULL, NULL),
	(75, 41, 2, NULL, NULL),
	(76, 20, 2, NULL, NULL),
	(77, 22, 2, NULL, NULL),
	(78, 21, 2, NULL, NULL),
	(79, 23, 2, NULL, NULL),
	(80, 32, 2, NULL, NULL),
	(81, 33, 2, NULL, NULL),
	(82, 34, 2, NULL, NULL),
	(83, 24, 2, NULL, NULL),
	(84, 26, 2, NULL, NULL),
	(85, 25, 2, NULL, NULL),
	(86, 27, 2, NULL, NULL),
	(87, 28, 2, NULL, NULL),
	(88, 39, 2, NULL, NULL),
	(89, 36, 2, NULL, NULL),
	(90, 35, 2, NULL, NULL),
	(91, 42, 2, NULL, NULL),
	(92, 38, 2, NULL, NULL),
	(93, 43, 2, NULL, NULL),
	(94, 40, 2, NULL, NULL),
	(95, 8, 2, NULL, NULL),
	(96, 5, 2, NULL, NULL),
	(109, 2, 14, NULL, NULL),
	(110, 1, 14, NULL, NULL),
	(111, 41, 14, NULL, NULL),
	(112, 4, 14, NULL, NULL),
	(113, 18, 15, NULL, NULL),
	(114, 39, 15, NULL, NULL),
	(115, 38, 15, NULL, NULL),
	(116, 18, 16, NULL, NULL),
	(117, 16, 16, NULL, NULL),
	(118, 18, 17, NULL, NULL),
	(119, 16, 17, NULL, NULL),
	(120, 18, 3, NULL, NULL),
	(121, 16, 3, NULL, NULL),
	(122, 9, 18, NULL, NULL),
	(123, 16, 18, NULL, NULL),
	(124, 9, 21, NULL, NULL),
	(125, 30, 21, NULL, NULL),
	(126, 18, 21, NULL, NULL),
	(127, 16, 21, NULL, NULL),
	(128, 17, 21, NULL, NULL),
	(129, 19, 21, NULL, NULL),
	(131, 2, 21, NULL, NULL),
	(132, 36, 21, NULL, NULL),
	(133, 35, 21, NULL, NULL),
	(134, 42, 21, NULL, NULL),
	(135, 8, 21, NULL, NULL),
	(136, 5, 21, NULL, NULL),
	(137, 15, 21, NULL, NULL),
	(138, 10, 21, NULL, NULL),
	(139, 11, 21, NULL, NULL),
	(140, 13, 21, NULL, NULL),
	(141, 14, 21, NULL, NULL),
	(142, 12, 21, NULL, NULL),
	(143, 43, 21, NULL, NULL),
	(144, 40, 21, NULL, NULL),
	(145, 1, 21, NULL, NULL),
	(146, 29, 21, NULL, NULL),
	(147, 31, 21, NULL, NULL),
	(148, 37, 21, NULL, NULL),
	(149, 39, 21, NULL, NULL),
	(150, 38, 21, NULL, NULL),
	(151, 32, 21, NULL, NULL),
	(152, 34, 21, NULL, NULL),
	(153, 33, 21, NULL, NULL),
	(154, 7, 21, NULL, NULL),
	(155, 24, 21, NULL, NULL),
	(156, 26, 21, NULL, NULL),
	(157, 25, 21, NULL, NULL),
	(158, 41, 21, NULL, NULL),
	(159, 27, 21, NULL, NULL),
	(160, 28, 21, NULL, NULL),
	(161, 20, 21, NULL, NULL),
	(162, 22, 21, NULL, NULL),
	(163, 21, 21, NULL, NULL),
	(164, 23, 21, NULL, NULL),
	(165, 4, 21, NULL, NULL),
	(166, 3, 21, NULL, NULL),
	(167, 1, 22, NULL, NULL),
	(168, 2, 22, NULL, NULL),
	(170, 4, 22, NULL, NULL),
	(171, 5, 22, NULL, NULL),
	(172, 6, 22, NULL, NULL),
	(173, 7, 22, NULL, NULL),
	(174, 8, 22, NULL, NULL),
	(175, 9, 22, NULL, NULL),
	(176, 10, 22, NULL, NULL),
	(177, 11, 22, NULL, NULL),
	(178, 12, 22, NULL, NULL),
	(179, 13, 22, NULL, NULL),
	(180, 14, 22, NULL, NULL),
	(181, 15, 22, NULL, NULL),
	(182, 16, 22, NULL, NULL),
	(183, 17, 22, NULL, NULL),
	(184, 18, 22, NULL, NULL),
	(186, 20, 22, NULL, NULL),
	(187, 21, 22, NULL, NULL),
	(188, 22, 22, NULL, NULL),
	(189, 23, 22, NULL, NULL),
	(190, 24, 22, NULL, NULL),
	(191, 25, 22, NULL, NULL),
	(192, 26, 22, NULL, NULL),
	(193, 27, 22, NULL, NULL),
	(194, 28, 22, NULL, NULL),
	(195, 29, 22, NULL, NULL),
	(196, 30, 22, NULL, NULL),
	(197, 31, 22, NULL, NULL),
	(198, 32, 22, NULL, NULL),
	(199, 33, 22, NULL, NULL),
	(200, 34, 22, NULL, NULL),
	(201, 35, 22, NULL, NULL),
	(202, 36, 22, NULL, NULL),
	(203, 37, 22, NULL, NULL),
	(204, 38, 22, NULL, NULL),
	(205, 39, 22, NULL, NULL),
	(206, 40, 22, NULL, NULL),
	(207, 41, 22, NULL, NULL),
	(208, 42, 22, NULL, NULL),
	(209, 43, 22, NULL, NULL),
	(210, 1, 23, NULL, NULL),
	(211, 2, 23, NULL, NULL),
	(212, 3, 23, NULL, NULL),
	(213, 4, 23, NULL, NULL),
	(214, 5, 23, NULL, NULL),
	(215, 6, 23, NULL, NULL),
	(216, 7, 23, NULL, NULL),
	(217, 8, 23, NULL, NULL),
	(218, 9, 23, NULL, NULL),
	(219, 10, 23, NULL, NULL),
	(220, 11, 23, NULL, NULL),
	(221, 12, 23, NULL, NULL),
	(222, 13, 23, NULL, NULL),
	(223, 14, 23, NULL, NULL),
	(224, 15, 23, NULL, NULL),
	(225, 16, 23, NULL, NULL),
	(226, 17, 23, NULL, NULL),
	(227, 18, 23, NULL, NULL),
	(228, 19, 23, NULL, NULL),
	(229, 20, 23, NULL, NULL),
	(230, 21, 23, NULL, NULL),
	(231, 22, 23, NULL, NULL),
	(232, 23, 23, NULL, NULL),
	(233, 24, 23, NULL, NULL),
	(234, 25, 23, NULL, NULL),
	(235, 26, 23, NULL, NULL),
	(236, 27, 23, NULL, NULL),
	(237, 28, 23, NULL, NULL),
	(238, 37, 23, NULL, NULL),
	(239, 38, 23, NULL, NULL),
	(240, 39, 23, NULL, NULL),
	(241, 40, 23, NULL, NULL),
	(242, 41, 23, NULL, NULL),
	(243, 43, 23, NULL, NULL),
	(244, 1, 24, NULL, NULL),
	(245, 2, 24, NULL, NULL),
	(246, 3, 24, NULL, NULL),
	(247, 4, 24, NULL, NULL),
	(248, 5, 24, NULL, NULL),
	(249, 6, 24, NULL, NULL),
	(250, 7, 24, NULL, NULL),
	(251, 8, 24, NULL, NULL),
	(252, 9, 24, NULL, NULL),
	(253, 10, 24, NULL, NULL),
	(254, 11, 24, NULL, NULL),
	(255, 12, 24, NULL, NULL),
	(256, 13, 24, NULL, NULL),
	(257, 14, 24, NULL, NULL),
	(258, 15, 24, NULL, NULL),
	(259, 16, 24, NULL, NULL),
	(260, 17, 24, NULL, NULL),
	(261, 18, 24, NULL, NULL),
	(262, 19, 24, NULL, NULL),
	(263, 20, 24, NULL, NULL),
	(264, 21, 24, NULL, NULL),
	(265, 22, 24, NULL, NULL),
	(266, 23, 24, NULL, NULL),
	(267, 24, 24, NULL, NULL),
	(268, 25, 24, NULL, NULL),
	(269, 26, 24, NULL, NULL),
	(270, 27, 24, NULL, NULL),
	(271, 28, 24, NULL, NULL),
	(272, 29, 24, NULL, NULL),
	(273, 30, 24, NULL, NULL),
	(274, 31, 24, NULL, NULL),
	(275, 32, 24, NULL, NULL),
	(276, 33, 24, NULL, NULL),
	(277, 34, 24, NULL, NULL),
	(278, 35, 24, NULL, NULL),
	(279, 36, 24, NULL, NULL),
	(280, 37, 24, NULL, NULL),
	(281, 38, 24, NULL, NULL),
	(282, 39, 24, NULL, NULL),
	(283, 40, 24, NULL, NULL),
	(284, 41, 24, NULL, NULL),
	(285, 42, 24, NULL, NULL),
	(286, 43, 24, NULL, NULL),
	(287, 1, 25, NULL, NULL),
	(288, 2, 25, NULL, NULL),
	(289, 3, 25, NULL, NULL),
	(290, 4, 25, NULL, NULL),
	(291, 5, 25, NULL, NULL),
	(292, 6, 25, NULL, NULL),
	(293, 7, 25, NULL, NULL),
	(294, 8, 25, NULL, NULL),
	(295, 9, 25, NULL, NULL),
	(296, 10, 25, NULL, NULL),
	(297, 11, 25, NULL, NULL);
INSERT INTO `permission_role` (`id`, `permission_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(298, 12, 25, NULL, NULL),
	(299, 13, 25, NULL, NULL),
	(300, 14, 25, NULL, NULL),
	(301, 15, 25, NULL, NULL),
	(302, 16, 25, NULL, NULL),
	(303, 17, 25, NULL, NULL),
	(304, 18, 25, NULL, NULL),
	(305, 19, 25, NULL, NULL),
	(306, 20, 25, NULL, NULL),
	(307, 21, 25, NULL, NULL),
	(308, 22, 25, NULL, NULL),
	(309, 23, 25, NULL, NULL),
	(310, 24, 25, NULL, NULL),
	(311, 25, 25, NULL, NULL),
	(312, 26, 25, NULL, NULL),
	(313, 27, 25, NULL, NULL),
	(314, 28, 25, NULL, NULL),
	(315, 37, 25, NULL, NULL),
	(316, 38, 25, NULL, NULL),
	(317, 39, 25, NULL, NULL),
	(318, 40, 25, NULL, NULL),
	(319, 41, 25, NULL, NULL),
	(320, 43, 25, NULL, NULL),
	(321, 19, 26, NULL, NULL),
	(322, 3, 26, NULL, NULL),
	(323, 44, 22, NULL, NULL),
	(324, 19, 22, NULL, NULL),
	(325, 3, 22, NULL, NULL),
	(326, 44, 24, NULL, NULL),
	(327, 1, 27, NULL, NULL),
	(328, 2, 27, NULL, NULL),
	(329, 3, 27, NULL, NULL),
	(330, 4, 27, NULL, NULL),
	(331, 5, 27, NULL, NULL),
	(332, 6, 27, NULL, NULL),
	(333, 7, 27, NULL, NULL),
	(334, 8, 27, NULL, NULL),
	(335, 9, 27, NULL, NULL),
	(336, 10, 27, NULL, NULL),
	(337, 11, 27, NULL, NULL),
	(338, 12, 27, NULL, NULL),
	(339, 13, 27, NULL, NULL),
	(340, 14, 27, NULL, NULL),
	(341, 15, 27, NULL, NULL),
	(342, 16, 27, NULL, NULL),
	(345, 19, 27, NULL, NULL),
	(346, 20, 27, NULL, NULL),
	(347, 21, 27, NULL, NULL),
	(348, 22, 27, NULL, NULL),
	(349, 23, 27, NULL, NULL),
	(350, 24, 27, NULL, NULL),
	(351, 25, 27, NULL, NULL),
	(352, 26, 27, NULL, NULL),
	(353, 27, 27, NULL, NULL),
	(354, 28, 27, NULL, NULL),
	(355, 29, 27, NULL, NULL),
	(356, 30, 27, NULL, NULL),
	(357, 31, 27, NULL, NULL),
	(358, 32, 27, NULL, NULL),
	(359, 33, 27, NULL, NULL),
	(360, 34, 27, NULL, NULL),
	(361, 35, 27, NULL, NULL),
	(362, 36, 27, NULL, NULL),
	(363, 37, 27, NULL, NULL),
	(364, 38, 27, NULL, NULL),
	(365, 39, 27, NULL, NULL),
	(366, 40, 27, NULL, NULL),
	(367, 41, 27, NULL, NULL),
	(368, 42, 27, NULL, NULL),
	(369, 43, 27, NULL, NULL),
	(370, 1, 28, NULL, NULL),
	(371, 2, 28, NULL, NULL),
	(372, 3, 28, NULL, NULL),
	(373, 4, 28, NULL, NULL),
	(374, 5, 28, NULL, NULL),
	(375, 6, 28, NULL, NULL),
	(376, 7, 28, NULL, NULL),
	(377, 8, 28, NULL, NULL),
	(378, 9, 28, NULL, NULL),
	(379, 10, 28, NULL, NULL),
	(380, 11, 28, NULL, NULL),
	(381, 12, 28, NULL, NULL),
	(382, 13, 28, NULL, NULL),
	(383, 14, 28, NULL, NULL),
	(384, 15, 28, NULL, NULL),
	(385, 16, 28, NULL, NULL),
	(386, 17, 28, NULL, NULL),
	(387, 18, 28, NULL, NULL),
	(388, 19, 28, NULL, NULL),
	(389, 20, 28, NULL, NULL),
	(390, 21, 28, NULL, NULL),
	(391, 22, 28, NULL, NULL),
	(392, 23, 28, NULL, NULL),
	(393, 24, 28, NULL, NULL),
	(394, 25, 28, NULL, NULL),
	(395, 26, 28, NULL, NULL),
	(396, 27, 28, NULL, NULL),
	(397, 28, 28, NULL, NULL),
	(398, 37, 28, NULL, NULL),
	(399, 38, 28, NULL, NULL),
	(400, 39, 28, NULL, NULL),
	(401, 40, 28, NULL, NULL),
	(402, 41, 28, NULL, NULL),
	(403, 43, 28, NULL, NULL),
	(404, 44, 27, NULL, NULL),
	(405, 17, 27, NULL, NULL),
	(406, 18, 27, NULL, NULL),
	(407, 1, 29, NULL, NULL),
	(408, 2, 29, NULL, NULL),
	(409, 3, 29, NULL, NULL),
	(410, 4, 29, NULL, NULL),
	(411, 5, 29, NULL, NULL),
	(412, 6, 29, NULL, NULL),
	(413, 7, 29, NULL, NULL),
	(414, 8, 29, NULL, NULL),
	(415, 9, 29, NULL, NULL),
	(416, 10, 29, NULL, NULL),
	(417, 11, 29, NULL, NULL),
	(418, 12, 29, NULL, NULL),
	(419, 13, 29, NULL, NULL),
	(420, 14, 29, NULL, NULL),
	(421, 15, 29, NULL, NULL),
	(422, 16, 29, NULL, NULL),
	(423, 17, 29, NULL, NULL),
	(424, 18, 29, NULL, NULL),
	(425, 19, 29, NULL, NULL),
	(426, 20, 29, NULL, NULL),
	(427, 21, 29, NULL, NULL),
	(428, 22, 29, NULL, NULL),
	(429, 23, 29, NULL, NULL),
	(430, 24, 29, NULL, NULL),
	(431, 25, 29, NULL, NULL),
	(432, 26, 29, NULL, NULL),
	(433, 27, 29, NULL, NULL),
	(434, 28, 29, NULL, NULL),
	(435, 29, 29, NULL, NULL),
	(436, 30, 29, NULL, NULL),
	(437, 31, 29, NULL, NULL),
	(438, 32, 29, NULL, NULL),
	(439, 33, 29, NULL, NULL),
	(440, 34, 29, NULL, NULL),
	(441, 35, 29, NULL, NULL),
	(442, 36, 29, NULL, NULL),
	(443, 37, 29, NULL, NULL),
	(444, 38, 29, NULL, NULL),
	(445, 39, 29, NULL, NULL),
	(447, 41, 29, NULL, NULL),
	(448, 42, 29, NULL, NULL),
	(449, 43, 29, NULL, NULL),
	(450, 1, 30, NULL, NULL),
	(451, 2, 30, NULL, NULL),
	(452, 3, 30, NULL, NULL),
	(453, 4, 30, NULL, NULL),
	(454, 5, 30, NULL, NULL),
	(455, 6, 30, NULL, NULL),
	(456, 7, 30, NULL, NULL),
	(457, 8, 30, NULL, NULL),
	(458, 9, 30, NULL, NULL),
	(459, 10, 30, NULL, NULL),
	(460, 11, 30, NULL, NULL),
	(461, 12, 30, NULL, NULL),
	(462, 13, 30, NULL, NULL),
	(463, 14, 30, NULL, NULL),
	(464, 15, 30, NULL, NULL),
	(465, 16, 30, NULL, NULL),
	(466, 17, 30, NULL, NULL),
	(467, 18, 30, NULL, NULL),
	(468, 19, 30, NULL, NULL),
	(469, 20, 30, NULL, NULL),
	(470, 21, 30, NULL, NULL),
	(471, 22, 30, NULL, NULL),
	(472, 23, 30, NULL, NULL),
	(473, 24, 30, NULL, NULL),
	(474, 25, 30, NULL, NULL),
	(475, 26, 30, NULL, NULL),
	(476, 27, 30, NULL, NULL),
	(477, 28, 30, NULL, NULL),
	(478, 37, 30, NULL, NULL),
	(479, 38, 30, NULL, NULL),
	(480, 39, 30, NULL, NULL),
	(481, 40, 30, NULL, NULL),
	(482, 41, 30, NULL, NULL),
	(483, 43, 30, NULL, NULL),
	(484, 44, 29, NULL, NULL),
	(486, 40, 29, NULL, NULL),
	(487, 1, 31, NULL, NULL),
	(488, 2, 31, NULL, NULL),
	(489, 3, 31, NULL, NULL),
	(490, 4, 31, NULL, NULL),
	(491, 5, 31, NULL, NULL),
	(492, 6, 31, NULL, NULL),
	(493, 7, 31, NULL, NULL),
	(494, 8, 31, NULL, NULL),
	(495, 9, 31, NULL, NULL),
	(496, 10, 31, NULL, NULL),
	(497, 11, 31, NULL, NULL),
	(498, 12, 31, NULL, NULL),
	(499, 13, 31, NULL, NULL),
	(500, 14, 31, NULL, NULL),
	(501, 15, 31, NULL, NULL),
	(502, 16, 31, NULL, NULL),
	(503, 17, 31, NULL, NULL),
	(504, 18, 31, NULL, NULL),
	(505, 19, 31, NULL, NULL),
	(506, 20, 31, NULL, NULL),
	(507, 21, 31, NULL, NULL),
	(508, 22, 31, NULL, NULL),
	(509, 23, 31, NULL, NULL),
	(510, 24, 31, NULL, NULL),
	(511, 25, 31, NULL, NULL),
	(512, 26, 31, NULL, NULL),
	(513, 27, 31, NULL, NULL),
	(514, 28, 31, NULL, NULL),
	(515, 29, 31, NULL, NULL),
	(516, 30, 31, NULL, NULL),
	(517, 31, 31, NULL, NULL),
	(518, 32, 31, NULL, NULL),
	(519, 33, 31, NULL, NULL),
	(520, 34, 31, NULL, NULL),
	(521, 35, 31, NULL, NULL),
	(522, 36, 31, NULL, NULL),
	(523, 37, 31, NULL, NULL),
	(524, 38, 31, NULL, NULL),
	(525, 39, 31, NULL, NULL),
	(526, 40, 31, NULL, NULL),
	(527, 41, 31, NULL, NULL),
	(528, 42, 31, NULL, NULL),
	(529, 43, 31, NULL, NULL),
	(530, 44, 31, NULL, NULL),
	(531, 1, 32, NULL, NULL),
	(532, 2, 32, NULL, NULL),
	(533, 3, 32, NULL, NULL),
	(534, 4, 32, NULL, NULL),
	(535, 5, 32, NULL, NULL),
	(536, 6, 32, NULL, NULL),
	(537, 7, 32, NULL, NULL),
	(538, 8, 32, NULL, NULL),
	(539, 9, 32, NULL, NULL),
	(540, 10, 32, NULL, NULL),
	(541, 11, 32, NULL, NULL),
	(542, 12, 32, NULL, NULL),
	(543, 13, 32, NULL, NULL),
	(544, 14, 32, NULL, NULL),
	(545, 15, 32, NULL, NULL),
	(546, 16, 32, NULL, NULL),
	(547, 17, 32, NULL, NULL),
	(548, 18, 32, NULL, NULL),
	(549, 19, 32, NULL, NULL),
	(550, 20, 32, NULL, NULL),
	(551, 21, 32, NULL, NULL);
INSERT INTO `permission_role` (`id`, `permission_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(552, 22, 32, NULL, NULL),
	(553, 23, 32, NULL, NULL),
	(554, 24, 32, NULL, NULL),
	(555, 25, 32, NULL, NULL),
	(556, 26, 32, NULL, NULL),
	(557, 27, 32, NULL, NULL),
	(558, 28, 32, NULL, NULL),
	(559, 37, 32, NULL, NULL),
	(560, 38, 32, NULL, NULL),
	(561, 39, 32, NULL, NULL),
	(562, 40, 32, NULL, NULL),
	(563, 41, 32, NULL, NULL),
	(564, 43, 32, NULL, NULL),
	(565, 1, 33, NULL, NULL),
	(566, 2, 33, NULL, NULL),
	(567, 3, 33, NULL, NULL),
	(568, 4, 33, NULL, NULL),
	(569, 5, 33, NULL, NULL),
	(570, 6, 33, NULL, NULL),
	(571, 7, 33, NULL, NULL),
	(572, 8, 33, NULL, NULL),
	(573, 9, 33, NULL, NULL),
	(574, 10, 33, NULL, NULL),
	(575, 11, 33, NULL, NULL),
	(576, 12, 33, NULL, NULL),
	(577, 13, 33, NULL, NULL),
	(578, 14, 33, NULL, NULL),
	(579, 15, 33, NULL, NULL),
	(580, 16, 33, NULL, NULL),
	(581, 17, 33, NULL, NULL),
	(582, 18, 33, NULL, NULL),
	(583, 19, 33, NULL, NULL),
	(584, 20, 33, NULL, NULL),
	(585, 21, 33, NULL, NULL),
	(586, 22, 33, NULL, NULL),
	(587, 23, 33, NULL, NULL),
	(588, 24, 33, NULL, NULL),
	(589, 25, 33, NULL, NULL),
	(590, 26, 33, NULL, NULL),
	(591, 27, 33, NULL, NULL),
	(592, 28, 33, NULL, NULL),
	(593, 29, 33, NULL, NULL),
	(594, 30, 33, NULL, NULL),
	(595, 31, 33, NULL, NULL),
	(596, 32, 33, NULL, NULL),
	(597, 33, 33, NULL, NULL),
	(598, 34, 33, NULL, NULL),
	(599, 35, 33, NULL, NULL),
	(600, 36, 33, NULL, NULL),
	(601, 37, 33, NULL, NULL),
	(602, 38, 33, NULL, NULL),
	(603, 39, 33, NULL, NULL),
	(604, 40, 33, NULL, NULL),
	(605, 41, 33, NULL, NULL),
	(606, 42, 33, NULL, NULL),
	(607, 43, 33, NULL, NULL),
	(608, 44, 33, NULL, NULL),
	(609, 1, 34, NULL, NULL),
	(610, 2, 34, NULL, NULL),
	(611, 3, 34, NULL, NULL),
	(612, 4, 34, NULL, NULL),
	(613, 5, 34, NULL, NULL),
	(614, 6, 34, NULL, NULL),
	(615, 7, 34, NULL, NULL),
	(616, 8, 34, NULL, NULL),
	(617, 9, 34, NULL, NULL),
	(618, 10, 34, NULL, NULL),
	(619, 11, 34, NULL, NULL),
	(620, 12, 34, NULL, NULL),
	(621, 13, 34, NULL, NULL),
	(622, 14, 34, NULL, NULL),
	(623, 15, 34, NULL, NULL),
	(624, 16, 34, NULL, NULL),
	(625, 17, 34, NULL, NULL),
	(626, 18, 34, NULL, NULL),
	(627, 19, 34, NULL, NULL),
	(628, 20, 34, NULL, NULL),
	(629, 21, 34, NULL, NULL),
	(630, 22, 34, NULL, NULL),
	(631, 23, 34, NULL, NULL),
	(632, 24, 34, NULL, NULL),
	(633, 25, 34, NULL, NULL),
	(634, 26, 34, NULL, NULL),
	(635, 27, 34, NULL, NULL),
	(636, 28, 34, NULL, NULL),
	(637, 37, 34, NULL, NULL),
	(638, 38, 34, NULL, NULL),
	(639, 39, 34, NULL, NULL),
	(640, 40, 34, NULL, NULL),
	(641, 41, 34, NULL, NULL),
	(642, 43, 34, NULL, NULL),
	(643, 1, 35, NULL, NULL),
	(644, 2, 35, NULL, NULL),
	(645, 3, 35, NULL, NULL),
	(646, 4, 35, NULL, NULL),
	(647, 5, 35, NULL, NULL),
	(648, 6, 35, NULL, NULL),
	(649, 7, 35, NULL, NULL),
	(650, 8, 35, NULL, NULL),
	(651, 9, 35, NULL, NULL),
	(652, 10, 35, NULL, NULL),
	(653, 11, 35, NULL, NULL),
	(654, 12, 35, NULL, NULL),
	(655, 13, 35, NULL, NULL),
	(656, 14, 35, NULL, NULL),
	(657, 15, 35, NULL, NULL),
	(658, 16, 35, NULL, NULL),
	(659, 17, 35, NULL, NULL),
	(660, 18, 35, NULL, NULL),
	(661, 19, 35, NULL, NULL),
	(662, 20, 35, NULL, NULL),
	(663, 21, 35, NULL, NULL),
	(664, 22, 35, NULL, NULL),
	(665, 23, 35, NULL, NULL),
	(666, 24, 35, NULL, NULL),
	(667, 25, 35, NULL, NULL),
	(668, 26, 35, NULL, NULL),
	(669, 27, 35, NULL, NULL),
	(670, 28, 35, NULL, NULL),
	(671, 29, 35, NULL, NULL),
	(672, 30, 35, NULL, NULL),
	(673, 31, 35, NULL, NULL),
	(674, 32, 35, NULL, NULL),
	(675, 33, 35, NULL, NULL),
	(676, 34, 35, NULL, NULL),
	(677, 35, 35, NULL, NULL),
	(678, 36, 35, NULL, NULL),
	(679, 37, 35, NULL, NULL),
	(680, 38, 35, NULL, NULL),
	(681, 39, 35, NULL, NULL),
	(682, 40, 35, NULL, NULL),
	(683, 41, 35, NULL, NULL),
	(684, 42, 35, NULL, NULL),
	(685, 43, 35, NULL, NULL),
	(686, 44, 35, NULL, NULL),
	(687, 1, 36, NULL, NULL),
	(688, 2, 36, NULL, NULL),
	(689, 3, 36, NULL, NULL),
	(690, 4, 36, NULL, NULL),
	(691, 5, 36, NULL, NULL),
	(692, 6, 36, NULL, NULL),
	(693, 7, 36, NULL, NULL),
	(694, 8, 36, NULL, NULL),
	(695, 9, 36, NULL, NULL),
	(696, 10, 36, NULL, NULL),
	(697, 11, 36, NULL, NULL),
	(698, 12, 36, NULL, NULL),
	(699, 13, 36, NULL, NULL),
	(700, 14, 36, NULL, NULL),
	(701, 15, 36, NULL, NULL),
	(702, 16, 36, NULL, NULL),
	(703, 17, 36, NULL, NULL),
	(704, 18, 36, NULL, NULL),
	(705, 19, 36, NULL, NULL),
	(706, 20, 36, NULL, NULL),
	(707, 21, 36, NULL, NULL),
	(708, 22, 36, NULL, NULL),
	(709, 23, 36, NULL, NULL),
	(710, 24, 36, NULL, NULL),
	(711, 25, 36, NULL, NULL),
	(712, 26, 36, NULL, NULL),
	(713, 27, 36, NULL, NULL),
	(714, 28, 36, NULL, NULL),
	(715, 37, 36, NULL, NULL),
	(716, 38, 36, NULL, NULL),
	(717, 39, 36, NULL, NULL),
	(718, 40, 36, NULL, NULL),
	(719, 41, 36, NULL, NULL),
	(720, 43, 36, NULL, NULL),
	(721, 1, 37, NULL, NULL),
	(722, 2, 37, NULL, NULL),
	(723, 3, 37, NULL, NULL),
	(724, 4, 37, NULL, NULL),
	(725, 5, 37, NULL, NULL),
	(726, 6, 37, NULL, NULL),
	(727, 7, 37, NULL, NULL),
	(728, 8, 37, NULL, NULL),
	(729, 9, 37, NULL, NULL),
	(730, 10, 37, NULL, NULL),
	(731, 11, 37, NULL, NULL),
	(732, 12, 37, NULL, NULL),
	(733, 13, 37, NULL, NULL),
	(734, 14, 37, NULL, NULL),
	(735, 15, 37, NULL, NULL),
	(736, 16, 37, NULL, NULL),
	(737, 17, 37, NULL, NULL),
	(738, 18, 37, NULL, NULL),
	(739, 19, 37, NULL, NULL),
	(740, 20, 37, NULL, NULL),
	(741, 21, 37, NULL, NULL),
	(742, 22, 37, NULL, NULL),
	(743, 23, 37, NULL, NULL),
	(744, 24, 37, NULL, NULL),
	(745, 25, 37, NULL, NULL),
	(746, 26, 37, NULL, NULL),
	(747, 27, 37, NULL, NULL),
	(748, 28, 37, NULL, NULL),
	(749, 29, 37, NULL, NULL),
	(750, 30, 37, NULL, NULL),
	(751, 31, 37, NULL, NULL),
	(752, 32, 37, NULL, NULL),
	(753, 33, 37, NULL, NULL),
	(754, 34, 37, NULL, NULL),
	(755, 35, 37, NULL, NULL),
	(756, 36, 37, NULL, NULL),
	(757, 37, 37, NULL, NULL),
	(758, 38, 37, NULL, NULL),
	(759, 39, 37, NULL, NULL),
	(760, 40, 37, NULL, NULL),
	(761, 41, 37, NULL, NULL),
	(762, 42, 37, NULL, NULL),
	(763, 43, 37, NULL, NULL),
	(764, 44, 37, NULL, NULL),
	(765, 1, 38, NULL, NULL),
	(766, 2, 38, NULL, NULL),
	(767, 3, 38, NULL, NULL),
	(768, 4, 38, NULL, NULL),
	(769, 5, 38, NULL, NULL),
	(770, 6, 38, NULL, NULL),
	(771, 7, 38, NULL, NULL),
	(772, 8, 38, NULL, NULL),
	(773, 9, 38, NULL, NULL),
	(774, 10, 38, NULL, NULL),
	(775, 11, 38, NULL, NULL),
	(776, 12, 38, NULL, NULL),
	(777, 13, 38, NULL, NULL),
	(778, 14, 38, NULL, NULL),
	(779, 15, 38, NULL, NULL),
	(780, 16, 38, NULL, NULL),
	(781, 17, 38, NULL, NULL),
	(782, 18, 38, NULL, NULL),
	(783, 19, 38, NULL, NULL),
	(784, 20, 38, NULL, NULL),
	(785, 21, 38, NULL, NULL),
	(786, 22, 38, NULL, NULL),
	(787, 23, 38, NULL, NULL),
	(788, 24, 38, NULL, NULL),
	(789, 25, 38, NULL, NULL),
	(790, 26, 38, NULL, NULL),
	(791, 27, 38, NULL, NULL),
	(792, 28, 38, NULL, NULL),
	(793, 37, 38, NULL, NULL),
	(794, 38, 38, NULL, NULL),
	(795, 39, 38, NULL, NULL),
	(796, 40, 38, NULL, NULL),
	(797, 41, 38, NULL, NULL),
	(798, 43, 38, NULL, NULL),
	(799, 9, 39, NULL, NULL),
	(800, 44, 39, NULL, NULL),
	(801, 30, 39, NULL, NULL);
INSERT INTO `permission_role` (`id`, `permission_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(802, 18, 39, NULL, NULL),
	(803, 16, 39, NULL, NULL),
	(804, 17, 39, NULL, NULL),
	(805, 19, 39, NULL, NULL),
	(806, 3, 39, NULL, NULL),
	(807, 6, 39, NULL, NULL),
	(808, 2, 39, NULL, NULL),
	(809, 36, 39, NULL, NULL),
	(810, 35, 39, NULL, NULL),
	(811, 42, 39, NULL, NULL),
	(812, 8, 39, NULL, NULL),
	(813, 15, 39, NULL, NULL),
	(814, 10, 39, NULL, NULL),
	(815, 11, 39, NULL, NULL),
	(816, 13, 39, NULL, NULL),
	(817, 14, 39, NULL, NULL),
	(818, 12, 39, NULL, NULL),
	(819, 43, 39, NULL, NULL),
	(820, 40, 39, NULL, NULL),
	(821, 1, 39, NULL, NULL),
	(822, 29, 39, NULL, NULL),
	(823, 31, 39, NULL, NULL),
	(824, 37, 39, NULL, NULL),
	(825, 39, 39, NULL, NULL),
	(826, 38, 39, NULL, NULL),
	(827, 32, 39, NULL, NULL),
	(828, 34, 39, NULL, NULL),
	(829, 33, 39, NULL, NULL),
	(830, 7, 39, NULL, NULL),
	(831, 24, 39, NULL, NULL),
	(832, 26, 39, NULL, NULL),
	(833, 25, 39, NULL, NULL),
	(834, 41, 39, NULL, NULL),
	(835, 27, 39, NULL, NULL),
	(836, 28, 39, NULL, NULL),
	(837, 20, 39, NULL, NULL),
	(838, 22, 39, NULL, NULL),
	(839, 21, 39, NULL, NULL),
	(840, 23, 39, NULL, NULL),
	(841, 4, 39, NULL, NULL),
	(842, 6, 40, NULL, NULL),
	(843, 2, 40, NULL, NULL),
	(844, 1, 41, NULL, NULL),
	(845, 2, 41, NULL, NULL),
	(846, 3, 41, NULL, NULL),
	(847, 4, 41, NULL, NULL),
	(848, 5, 41, NULL, NULL),
	(849, 6, 41, NULL, NULL),
	(850, 7, 41, NULL, NULL),
	(851, 8, 41, NULL, NULL),
	(852, 9, 41, NULL, NULL),
	(853, 10, 41, NULL, NULL),
	(854, 11, 41, NULL, NULL),
	(855, 12, 41, NULL, NULL),
	(856, 13, 41, NULL, NULL),
	(857, 14, 41, NULL, NULL),
	(858, 15, 41, NULL, NULL),
	(859, 16, 41, NULL, NULL),
	(860, 17, 41, NULL, NULL),
	(861, 18, 41, NULL, NULL),
	(862, 19, 41, NULL, NULL),
	(863, 20, 41, NULL, NULL),
	(864, 21, 41, NULL, NULL),
	(865, 22, 41, NULL, NULL),
	(866, 23, 41, NULL, NULL),
	(867, 24, 41, NULL, NULL),
	(868, 25, 41, NULL, NULL),
	(869, 26, 41, NULL, NULL),
	(870, 27, 41, NULL, NULL),
	(871, 28, 41, NULL, NULL),
	(872, 29, 41, NULL, NULL),
	(873, 30, 41, NULL, NULL),
	(874, 31, 41, NULL, NULL),
	(875, 32, 41, NULL, NULL),
	(876, 33, 41, NULL, NULL),
	(877, 34, 41, NULL, NULL),
	(878, 35, 41, NULL, NULL),
	(879, 36, 41, NULL, NULL),
	(880, 37, 41, NULL, NULL),
	(881, 38, 41, NULL, NULL),
	(882, 39, 41, NULL, NULL),
	(883, 40, 41, NULL, NULL),
	(884, 41, 41, NULL, NULL),
	(885, 42, 41, NULL, NULL),
	(886, 43, 41, NULL, NULL),
	(887, 44, 41, NULL, NULL),
	(888, 1, 42, NULL, NULL),
	(889, 2, 42, NULL, NULL),
	(890, 3, 42, NULL, NULL),
	(891, 4, 42, NULL, NULL),
	(892, 5, 42, NULL, NULL),
	(893, 6, 42, NULL, NULL),
	(894, 7, 42, NULL, NULL),
	(895, 8, 42, NULL, NULL),
	(896, 9, 42, NULL, NULL),
	(897, 10, 42, NULL, NULL),
	(898, 11, 42, NULL, NULL),
	(899, 12, 42, NULL, NULL),
	(900, 13, 42, NULL, NULL),
	(901, 14, 42, NULL, NULL),
	(902, 15, 42, NULL, NULL),
	(903, 16, 42, NULL, NULL),
	(904, 17, 42, NULL, NULL),
	(905, 18, 42, NULL, NULL),
	(906, 19, 42, NULL, NULL),
	(907, 20, 42, NULL, NULL),
	(908, 21, 42, NULL, NULL),
	(909, 22, 42, NULL, NULL),
	(910, 23, 42, NULL, NULL),
	(911, 24, 42, NULL, NULL),
	(912, 25, 42, NULL, NULL),
	(913, 26, 42, NULL, NULL),
	(914, 27, 42, NULL, NULL),
	(915, 28, 42, NULL, NULL),
	(916, 37, 42, NULL, NULL),
	(917, 38, 42, NULL, NULL),
	(918, 39, 42, NULL, NULL),
	(919, 40, 42, NULL, NULL),
	(920, 41, 42, NULL, NULL),
	(921, 43, 42, NULL, NULL),
	(922, 5, 39, NULL, NULL),
	(923, 1, 43, NULL, NULL),
	(924, 2, 43, NULL, NULL),
	(925, 3, 43, NULL, NULL),
	(926, 4, 43, NULL, NULL),
	(927, 5, 43, NULL, NULL),
	(928, 6, 43, NULL, NULL),
	(929, 7, 43, NULL, NULL),
	(930, 8, 43, NULL, NULL),
	(931, 9, 43, NULL, NULL),
	(932, 10, 43, NULL, NULL),
	(933, 11, 43, NULL, NULL),
	(934, 12, 43, NULL, NULL),
	(935, 13, 43, NULL, NULL),
	(936, 14, 43, NULL, NULL),
	(937, 15, 43, NULL, NULL),
	(938, 16, 43, NULL, NULL),
	(939, 17, 43, NULL, NULL),
	(940, 18, 43, NULL, NULL),
	(941, 19, 43, NULL, NULL),
	(942, 20, 43, NULL, NULL),
	(943, 21, 43, NULL, NULL),
	(944, 22, 43, NULL, NULL),
	(945, 23, 43, NULL, NULL),
	(946, 24, 43, NULL, NULL),
	(947, 25, 43, NULL, NULL),
	(948, 26, 43, NULL, NULL),
	(949, 27, 43, NULL, NULL),
	(950, 28, 43, NULL, NULL),
	(951, 29, 43, NULL, NULL),
	(952, 30, 43, NULL, NULL),
	(953, 31, 43, NULL, NULL),
	(954, 32, 43, NULL, NULL),
	(955, 33, 43, NULL, NULL),
	(956, 34, 43, NULL, NULL),
	(957, 35, 43, NULL, NULL),
	(958, 36, 43, NULL, NULL),
	(959, 37, 43, NULL, NULL),
	(960, 38, 43, NULL, NULL),
	(961, 39, 43, NULL, NULL),
	(962, 40, 43, NULL, NULL),
	(963, 41, 43, NULL, NULL),
	(964, 42, 43, NULL, NULL),
	(965, 43, 43, NULL, NULL),
	(966, 44, 43, NULL, NULL),
	(967, 1, 44, NULL, NULL),
	(968, 2, 44, NULL, NULL),
	(969, 3, 44, NULL, NULL),
	(970, 4, 44, NULL, NULL),
	(971, 5, 44, NULL, NULL),
	(972, 6, 44, NULL, NULL),
	(973, 7, 44, NULL, NULL),
	(974, 8, 44, NULL, NULL),
	(975, 9, 44, NULL, NULL),
	(976, 10, 44, NULL, NULL),
	(977, 11, 44, NULL, NULL),
	(978, 12, 44, NULL, NULL),
	(979, 13, 44, NULL, NULL),
	(980, 14, 44, NULL, NULL),
	(981, 15, 44, NULL, NULL),
	(982, 16, 44, NULL, NULL),
	(983, 17, 44, NULL, NULL),
	(984, 18, 44, NULL, NULL),
	(985, 19, 44, NULL, NULL),
	(986, 20, 44, NULL, NULL),
	(987, 21, 44, NULL, NULL),
	(988, 22, 44, NULL, NULL),
	(989, 23, 44, NULL, NULL),
	(990, 24, 44, NULL, NULL),
	(991, 25, 44, NULL, NULL),
	(992, 26, 44, NULL, NULL),
	(993, 27, 44, NULL, NULL),
	(994, 28, 44, NULL, NULL),
	(995, 37, 44, NULL, NULL),
	(996, 38, 44, NULL, NULL),
	(997, 39, 44, NULL, NULL),
	(998, 40, 44, NULL, NULL),
	(999, 41, 44, NULL, NULL),
	(1000, 43, 44, NULL, NULL),
	(1001, 44, 28, NULL, NULL),
	(1002, 2, 45, NULL, NULL),
	(1003, 1, 45, NULL, NULL),
	(1004, 37, 45, NULL, NULL),
	(1005, 39, 45, NULL, NULL),
	(1006, 38, 45, NULL, NULL),
	(1007, 26, 45, NULL, NULL),
	(1008, 41, 45, NULL, NULL),
	(1009, 22, 45, NULL, NULL),
	(1010, 4, 45, NULL, NULL),
	(1011, 44, 45, NULL, NULL),
	(1012, 9, 45, NULL, NULL),
	(1013, 1, 46, NULL, NULL),
	(1014, 2, 46, NULL, NULL),
	(1015, 3, 46, NULL, NULL),
	(1016, 4, 46, NULL, NULL),
	(1017, 5, 46, NULL, NULL),
	(1018, 6, 46, NULL, NULL),
	(1019, 7, 46, NULL, NULL),
	(1020, 8, 46, NULL, NULL),
	(1021, 9, 46, NULL, NULL),
	(1022, 10, 46, NULL, NULL),
	(1023, 11, 46, NULL, NULL),
	(1024, 12, 46, NULL, NULL),
	(1025, 13, 46, NULL, NULL),
	(1026, 14, 46, NULL, NULL),
	(1027, 15, 46, NULL, NULL),
	(1028, 16, 46, NULL, NULL),
	(1029, 17, 46, NULL, NULL),
	(1030, 18, 46, NULL, NULL),
	(1031, 19, 46, NULL, NULL),
	(1032, 20, 46, NULL, NULL),
	(1033, 21, 46, NULL, NULL),
	(1034, 22, 46, NULL, NULL),
	(1035, 23, 46, NULL, NULL),
	(1036, 24, 46, NULL, NULL),
	(1037, 25, 46, NULL, NULL),
	(1038, 26, 46, NULL, NULL),
	(1039, 27, 46, NULL, NULL),
	(1040, 28, 46, NULL, NULL),
	(1041, 29, 46, NULL, NULL),
	(1042, 30, 46, NULL, NULL),
	(1043, 31, 46, NULL, NULL),
	(1044, 32, 46, NULL, NULL),
	(1045, 33, 46, NULL, NULL),
	(1046, 34, 46, NULL, NULL),
	(1047, 35, 46, NULL, NULL),
	(1048, 36, 46, NULL, NULL),
	(1049, 37, 46, NULL, NULL),
	(1050, 38, 46, NULL, NULL),
	(1051, 39, 46, NULL, NULL);
INSERT INTO `permission_role` (`id`, `permission_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(1052, 40, 46, NULL, NULL),
	(1053, 41, 46, NULL, NULL),
	(1054, 42, 46, NULL, NULL),
	(1055, 43, 46, NULL, NULL),
	(1056, 44, 46, NULL, NULL),
	(1057, 1, 47, NULL, NULL),
	(1058, 2, 47, NULL, NULL),
	(1059, 3, 47, NULL, NULL),
	(1060, 4, 47, NULL, NULL),
	(1061, 5, 47, NULL, NULL),
	(1062, 6, 47, NULL, NULL),
	(1063, 7, 47, NULL, NULL),
	(1064, 8, 47, NULL, NULL),
	(1065, 9, 47, NULL, NULL),
	(1066, 10, 47, NULL, NULL),
	(1067, 11, 47, NULL, NULL),
	(1068, 12, 47, NULL, NULL),
	(1069, 13, 47, NULL, NULL),
	(1070, 14, 47, NULL, NULL),
	(1071, 15, 47, NULL, NULL),
	(1072, 16, 47, NULL, NULL),
	(1073, 17, 47, NULL, NULL),
	(1074, 18, 47, NULL, NULL),
	(1075, 19, 47, NULL, NULL),
	(1076, 20, 47, NULL, NULL),
	(1077, 21, 47, NULL, NULL),
	(1078, 22, 47, NULL, NULL),
	(1079, 23, 47, NULL, NULL),
	(1080, 24, 47, NULL, NULL),
	(1081, 25, 47, NULL, NULL),
	(1082, 26, 47, NULL, NULL),
	(1083, 27, 47, NULL, NULL),
	(1084, 28, 47, NULL, NULL),
	(1085, 37, 47, NULL, NULL),
	(1086, 38, 47, NULL, NULL),
	(1087, 39, 47, NULL, NULL),
	(1088, 40, 47, NULL, NULL),
	(1089, 41, 47, NULL, NULL),
	(1090, 43, 47, NULL, NULL),
	(1091, 1, 48, NULL, NULL),
	(1092, 2, 48, NULL, NULL),
	(1093, 3, 48, NULL, NULL),
	(1094, 4, 48, NULL, NULL),
	(1095, 5, 48, NULL, NULL),
	(1096, 6, 48, NULL, NULL),
	(1097, 7, 48, NULL, NULL),
	(1098, 8, 48, NULL, NULL),
	(1099, 9, 48, NULL, NULL),
	(1100, 10, 48, NULL, NULL),
	(1101, 11, 48, NULL, NULL),
	(1102, 12, 48, NULL, NULL),
	(1103, 13, 48, NULL, NULL),
	(1104, 14, 48, NULL, NULL),
	(1105, 15, 48, NULL, NULL),
	(1106, 16, 48, NULL, NULL),
	(1107, 17, 48, NULL, NULL),
	(1108, 18, 48, NULL, NULL),
	(1109, 19, 48, NULL, NULL),
	(1110, 20, 48, NULL, NULL),
	(1111, 21, 48, NULL, NULL),
	(1112, 22, 48, NULL, NULL),
	(1113, 23, 48, NULL, NULL),
	(1114, 24, 48, NULL, NULL),
	(1115, 25, 48, NULL, NULL),
	(1116, 26, 48, NULL, NULL),
	(1117, 27, 48, NULL, NULL),
	(1118, 28, 48, NULL, NULL),
	(1119, 29, 48, NULL, NULL),
	(1120, 30, 48, NULL, NULL),
	(1121, 31, 48, NULL, NULL),
	(1122, 32, 48, NULL, NULL),
	(1123, 33, 48, NULL, NULL),
	(1124, 34, 48, NULL, NULL),
	(1125, 35, 48, NULL, NULL),
	(1126, 36, 48, NULL, NULL),
	(1127, 37, 48, NULL, NULL),
	(1128, 38, 48, NULL, NULL),
	(1129, 39, 48, NULL, NULL),
	(1130, 40, 48, NULL, NULL),
	(1131, 41, 48, NULL, NULL),
	(1132, 42, 48, NULL, NULL),
	(1133, 43, 48, NULL, NULL),
	(1134, 44, 48, NULL, NULL),
	(1135, 1, 49, NULL, NULL),
	(1136, 2, 49, NULL, NULL),
	(1137, 3, 49, NULL, NULL),
	(1138, 4, 49, NULL, NULL),
	(1139, 5, 49, NULL, NULL),
	(1140, 6, 49, NULL, NULL),
	(1141, 7, 49, NULL, NULL),
	(1142, 8, 49, NULL, NULL),
	(1143, 9, 49, NULL, NULL),
	(1144, 10, 49, NULL, NULL),
	(1145, 11, 49, NULL, NULL),
	(1146, 12, 49, NULL, NULL),
	(1147, 13, 49, NULL, NULL),
	(1148, 14, 49, NULL, NULL),
	(1149, 15, 49, NULL, NULL),
	(1150, 16, 49, NULL, NULL),
	(1151, 17, 49, NULL, NULL),
	(1152, 18, 49, NULL, NULL),
	(1153, 19, 49, NULL, NULL),
	(1154, 20, 49, NULL, NULL),
	(1155, 21, 49, NULL, NULL),
	(1156, 22, 49, NULL, NULL),
	(1157, 23, 49, NULL, NULL),
	(1158, 24, 49, NULL, NULL),
	(1159, 25, 49, NULL, NULL),
	(1160, 26, 49, NULL, NULL),
	(1161, 27, 49, NULL, NULL),
	(1162, 28, 49, NULL, NULL),
	(1163, 37, 49, NULL, NULL),
	(1164, 38, 49, NULL, NULL),
	(1165, 39, 49, NULL, NULL),
	(1166, 40, 49, NULL, NULL),
	(1167, 41, 49, NULL, NULL),
	(1168, 43, 49, NULL, NULL),
	(1169, 1, 50, NULL, NULL),
	(1170, 2, 50, NULL, NULL),
	(1171, 3, 50, NULL, NULL),
	(1172, 4, 50, NULL, NULL),
	(1173, 5, 50, NULL, NULL),
	(1174, 6, 50, NULL, NULL),
	(1175, 7, 50, NULL, NULL),
	(1176, 8, 50, NULL, NULL),
	(1177, 9, 50, NULL, NULL),
	(1178, 10, 50, NULL, NULL),
	(1179, 11, 50, NULL, NULL),
	(1180, 12, 50, NULL, NULL),
	(1181, 13, 50, NULL, NULL),
	(1182, 14, 50, NULL, NULL),
	(1183, 15, 50, NULL, NULL),
	(1184, 16, 50, NULL, NULL),
	(1185, 17, 50, NULL, NULL),
	(1186, 18, 50, NULL, NULL),
	(1187, 19, 50, NULL, NULL),
	(1188, 20, 50, NULL, NULL),
	(1189, 21, 50, NULL, NULL),
	(1190, 22, 50, NULL, NULL),
	(1191, 23, 50, NULL, NULL),
	(1192, 24, 50, NULL, NULL),
	(1193, 25, 50, NULL, NULL),
	(1194, 26, 50, NULL, NULL),
	(1195, 27, 50, NULL, NULL),
	(1196, 28, 50, NULL, NULL),
	(1197, 29, 50, NULL, NULL),
	(1198, 30, 50, NULL, NULL),
	(1199, 31, 50, NULL, NULL),
	(1200, 32, 50, NULL, NULL),
	(1201, 33, 50, NULL, NULL),
	(1202, 34, 50, NULL, NULL),
	(1203, 35, 50, NULL, NULL),
	(1204, 36, 50, NULL, NULL),
	(1205, 37, 50, NULL, NULL),
	(1206, 38, 50, NULL, NULL),
	(1207, 39, 50, NULL, NULL),
	(1208, 40, 50, NULL, NULL),
	(1209, 41, 50, NULL, NULL),
	(1210, 42, 50, NULL, NULL),
	(1211, 43, 50, NULL, NULL),
	(1212, 44, 50, NULL, NULL),
	(1213, 1, 51, NULL, NULL),
	(1214, 2, 51, NULL, NULL),
	(1215, 3, 51, NULL, NULL),
	(1216, 4, 51, NULL, NULL),
	(1217, 5, 51, NULL, NULL),
	(1218, 6, 51, NULL, NULL),
	(1219, 7, 51, NULL, NULL),
	(1220, 8, 51, NULL, NULL),
	(1221, 9, 51, NULL, NULL),
	(1222, 10, 51, NULL, NULL),
	(1223, 11, 51, NULL, NULL),
	(1224, 12, 51, NULL, NULL),
	(1225, 13, 51, NULL, NULL),
	(1226, 14, 51, NULL, NULL),
	(1227, 15, 51, NULL, NULL),
	(1228, 16, 51, NULL, NULL),
	(1229, 17, 51, NULL, NULL),
	(1230, 18, 51, NULL, NULL),
	(1231, 19, 51, NULL, NULL),
	(1232, 20, 51, NULL, NULL),
	(1233, 21, 51, NULL, NULL),
	(1234, 22, 51, NULL, NULL),
	(1235, 23, 51, NULL, NULL),
	(1236, 24, 51, NULL, NULL),
	(1237, 25, 51, NULL, NULL),
	(1238, 26, 51, NULL, NULL),
	(1239, 27, 51, NULL, NULL),
	(1240, 28, 51, NULL, NULL),
	(1241, 37, 51, NULL, NULL),
	(1242, 38, 51, NULL, NULL),
	(1243, 39, 51, NULL, NULL),
	(1244, 40, 51, NULL, NULL),
	(1245, 41, 51, NULL, NULL),
	(1246, 43, 51, NULL, NULL),
	(1247, 1, 52, NULL, NULL),
	(1248, 2, 52, NULL, NULL),
	(1249, 3, 52, NULL, NULL),
	(1250, 4, 52, NULL, NULL),
	(1251, 5, 52, NULL, NULL),
	(1252, 6, 52, NULL, NULL),
	(1253, 7, 52, NULL, NULL),
	(1254, 8, 52, NULL, NULL),
	(1255, 9, 52, NULL, NULL),
	(1256, 10, 52, NULL, NULL),
	(1257, 11, 52, NULL, NULL),
	(1258, 12, 52, NULL, NULL),
	(1259, 13, 52, NULL, NULL),
	(1260, 14, 52, NULL, NULL),
	(1261, 15, 52, NULL, NULL),
	(1262, 16, 52, NULL, NULL),
	(1263, 17, 52, NULL, NULL),
	(1264, 18, 52, NULL, NULL),
	(1265, 19, 52, NULL, NULL),
	(1266, 20, 52, NULL, NULL),
	(1267, 21, 52, NULL, NULL),
	(1268, 22, 52, NULL, NULL),
	(1269, 23, 52, NULL, NULL),
	(1270, 24, 52, NULL, NULL),
	(1271, 25, 52, NULL, NULL),
	(1272, 26, 52, NULL, NULL),
	(1273, 27, 52, NULL, NULL),
	(1274, 28, 52, NULL, NULL),
	(1275, 29, 52, NULL, NULL),
	(1276, 30, 52, NULL, NULL),
	(1277, 31, 52, NULL, NULL),
	(1278, 32, 52, NULL, NULL),
	(1279, 33, 52, NULL, NULL),
	(1280, 34, 52, NULL, NULL),
	(1281, 35, 52, NULL, NULL),
	(1282, 36, 52, NULL, NULL),
	(1283, 37, 52, NULL, NULL),
	(1284, 38, 52, NULL, NULL),
	(1285, 39, 52, NULL, NULL),
	(1286, 40, 52, NULL, NULL),
	(1287, 41, 52, NULL, NULL),
	(1288, 42, 52, NULL, NULL),
	(1289, 43, 52, NULL, NULL),
	(1290, 44, 52, NULL, NULL),
	(1291, 1, 53, NULL, NULL),
	(1292, 2, 53, NULL, NULL),
	(1293, 3, 53, NULL, NULL),
	(1294, 4, 53, NULL, NULL),
	(1295, 5, 53, NULL, NULL),
	(1296, 6, 53, NULL, NULL),
	(1297, 7, 53, NULL, NULL),
	(1298, 8, 53, NULL, NULL),
	(1299, 9, 53, NULL, NULL),
	(1300, 10, 53, NULL, NULL),
	(1301, 11, 53, NULL, NULL);
INSERT INTO `permission_role` (`id`, `permission_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(1302, 12, 53, NULL, NULL),
	(1303, 13, 53, NULL, NULL),
	(1304, 14, 53, NULL, NULL),
	(1305, 15, 53, NULL, NULL),
	(1306, 16, 53, NULL, NULL),
	(1307, 17, 53, NULL, NULL),
	(1308, 18, 53, NULL, NULL),
	(1309, 19, 53, NULL, NULL),
	(1310, 20, 53, NULL, NULL),
	(1311, 21, 53, NULL, NULL),
	(1312, 22, 53, NULL, NULL),
	(1313, 23, 53, NULL, NULL),
	(1314, 24, 53, NULL, NULL),
	(1315, 25, 53, NULL, NULL),
	(1316, 26, 53, NULL, NULL),
	(1317, 27, 53, NULL, NULL),
	(1318, 28, 53, NULL, NULL),
	(1319, 37, 53, NULL, NULL),
	(1320, 38, 53, NULL, NULL),
	(1321, 39, 53, NULL, NULL),
	(1322, 40, 53, NULL, NULL),
	(1323, 41, 53, NULL, NULL),
	(1324, 43, 53, NULL, NULL),
	(1325, 1, 54, NULL, NULL),
	(1326, 2, 54, NULL, NULL),
	(1327, 3, 54, NULL, NULL),
	(1328, 4, 54, NULL, NULL),
	(1329, 5, 54, NULL, NULL),
	(1330, 6, 54, NULL, NULL),
	(1331, 7, 54, NULL, NULL),
	(1332, 8, 54, NULL, NULL),
	(1333, 9, 54, NULL, NULL),
	(1334, 10, 54, NULL, NULL),
	(1335, 11, 54, NULL, NULL),
	(1336, 12, 54, NULL, NULL),
	(1337, 13, 54, NULL, NULL),
	(1338, 14, 54, NULL, NULL),
	(1339, 15, 54, NULL, NULL),
	(1340, 16, 54, NULL, NULL),
	(1341, 17, 54, NULL, NULL),
	(1342, 18, 54, NULL, NULL),
	(1343, 19, 54, NULL, NULL),
	(1344, 20, 54, NULL, NULL),
	(1345, 21, 54, NULL, NULL),
	(1346, 22, 54, NULL, NULL),
	(1347, 23, 54, NULL, NULL),
	(1348, 24, 54, NULL, NULL),
	(1349, 25, 54, NULL, NULL),
	(1350, 26, 54, NULL, NULL),
	(1351, 27, 54, NULL, NULL),
	(1352, 28, 54, NULL, NULL),
	(1353, 29, 54, NULL, NULL),
	(1354, 30, 54, NULL, NULL),
	(1355, 31, 54, NULL, NULL),
	(1356, 32, 54, NULL, NULL),
	(1357, 33, 54, NULL, NULL),
	(1358, 34, 54, NULL, NULL),
	(1359, 35, 54, NULL, NULL),
	(1360, 36, 54, NULL, NULL),
	(1361, 37, 54, NULL, NULL),
	(1362, 38, 54, NULL, NULL),
	(1363, 39, 54, NULL, NULL),
	(1364, 40, 54, NULL, NULL),
	(1365, 41, 54, NULL, NULL),
	(1366, 42, 54, NULL, NULL),
	(1367, 43, 54, NULL, NULL),
	(1368, 44, 54, NULL, NULL),
	(1369, 1, 55, NULL, NULL),
	(1370, 2, 55, NULL, NULL),
	(1371, 3, 55, NULL, NULL),
	(1372, 4, 55, NULL, NULL),
	(1373, 5, 55, NULL, NULL),
	(1374, 6, 55, NULL, NULL),
	(1375, 7, 55, NULL, NULL),
	(1376, 8, 55, NULL, NULL),
	(1377, 9, 55, NULL, NULL),
	(1378, 10, 55, NULL, NULL),
	(1379, 11, 55, NULL, NULL),
	(1380, 12, 55, NULL, NULL),
	(1381, 13, 55, NULL, NULL),
	(1382, 14, 55, NULL, NULL),
	(1383, 15, 55, NULL, NULL),
	(1384, 16, 55, NULL, NULL),
	(1385, 17, 55, NULL, NULL),
	(1386, 18, 55, NULL, NULL),
	(1387, 19, 55, NULL, NULL),
	(1388, 20, 55, NULL, NULL),
	(1389, 21, 55, NULL, NULL),
	(1390, 22, 55, NULL, NULL),
	(1391, 23, 55, NULL, NULL),
	(1392, 24, 55, NULL, NULL),
	(1393, 25, 55, NULL, NULL),
	(1394, 26, 55, NULL, NULL),
	(1395, 27, 55, NULL, NULL),
	(1396, 28, 55, NULL, NULL),
	(1397, 37, 55, NULL, NULL),
	(1398, 38, 55, NULL, NULL),
	(1399, 39, 55, NULL, NULL),
	(1400, 40, 55, NULL, NULL),
	(1401, 41, 55, NULL, NULL),
	(1402, 43, 55, NULL, NULL),
	(1403, 1, 56, NULL, NULL),
	(1404, 2, 56, NULL, NULL),
	(1405, 3, 56, NULL, NULL),
	(1406, 4, 56, NULL, NULL),
	(1407, 5, 56, NULL, NULL),
	(1408, 6, 56, NULL, NULL),
	(1409, 7, 56, NULL, NULL),
	(1410, 8, 56, NULL, NULL),
	(1411, 9, 56, NULL, NULL),
	(1412, 10, 56, NULL, NULL),
	(1413, 11, 56, NULL, NULL),
	(1414, 12, 56, NULL, NULL),
	(1415, 13, 56, NULL, NULL),
	(1416, 14, 56, NULL, NULL),
	(1417, 15, 56, NULL, NULL),
	(1418, 16, 56, NULL, NULL),
	(1419, 17, 56, NULL, NULL),
	(1420, 18, 56, NULL, NULL),
	(1421, 19, 56, NULL, NULL),
	(1422, 20, 56, NULL, NULL),
	(1423, 21, 56, NULL, NULL),
	(1424, 22, 56, NULL, NULL),
	(1425, 23, 56, NULL, NULL),
	(1426, 24, 56, NULL, NULL),
	(1427, 25, 56, NULL, NULL),
	(1428, 26, 56, NULL, NULL),
	(1429, 27, 56, NULL, NULL),
	(1430, 28, 56, NULL, NULL),
	(1431, 29, 56, NULL, NULL),
	(1432, 30, 56, NULL, NULL),
	(1433, 31, 56, NULL, NULL),
	(1434, 32, 56, NULL, NULL),
	(1435, 33, 56, NULL, NULL),
	(1436, 34, 56, NULL, NULL),
	(1437, 35, 56, NULL, NULL),
	(1438, 36, 56, NULL, NULL),
	(1439, 37, 56, NULL, NULL),
	(1440, 38, 56, NULL, NULL),
	(1441, 39, 56, NULL, NULL),
	(1442, 40, 56, NULL, NULL),
	(1443, 41, 56, NULL, NULL),
	(1444, 42, 56, NULL, NULL),
	(1445, 43, 56, NULL, NULL),
	(1446, 44, 56, NULL, NULL),
	(1447, 1, 57, NULL, NULL),
	(1448, 2, 57, NULL, NULL),
	(1449, 3, 57, NULL, NULL),
	(1450, 4, 57, NULL, NULL),
	(1451, 5, 57, NULL, NULL),
	(1452, 6, 57, NULL, NULL),
	(1453, 7, 57, NULL, NULL),
	(1454, 8, 57, NULL, NULL),
	(1455, 9, 57, NULL, NULL),
	(1456, 10, 57, NULL, NULL),
	(1457, 11, 57, NULL, NULL),
	(1458, 12, 57, NULL, NULL),
	(1459, 13, 57, NULL, NULL),
	(1460, 14, 57, NULL, NULL),
	(1461, 15, 57, NULL, NULL),
	(1462, 16, 57, NULL, NULL),
	(1463, 17, 57, NULL, NULL),
	(1464, 18, 57, NULL, NULL),
	(1465, 19, 57, NULL, NULL),
	(1466, 20, 57, NULL, NULL),
	(1467, 21, 57, NULL, NULL),
	(1468, 22, 57, NULL, NULL),
	(1469, 23, 57, NULL, NULL),
	(1470, 24, 57, NULL, NULL),
	(1471, 25, 57, NULL, NULL),
	(1472, 26, 57, NULL, NULL),
	(1473, 27, 57, NULL, NULL),
	(1474, 28, 57, NULL, NULL),
	(1475, 37, 57, NULL, NULL),
	(1476, 38, 57, NULL, NULL),
	(1477, 39, 57, NULL, NULL),
	(1478, 40, 57, NULL, NULL),
	(1479, 41, 57, NULL, NULL),
	(1480, 43, 57, NULL, NULL),
	(1481, 2, 58, NULL, NULL),
	(1482, 1, 58, NULL, NULL),
	(1483, 44, 58, NULL, NULL),
	(1484, 9, 58, NULL, NULL),
	(1485, 37, 58, NULL, NULL),
	(1486, 39, 58, NULL, NULL),
	(1487, 38, 58, NULL, NULL),
	(1488, 26, 58, NULL, NULL),
	(1489, 41, 58, NULL, NULL),
	(1490, 22, 58, NULL, NULL),
	(1491, 4, 58, NULL, NULL),
	(1492, 2, 59, NULL, NULL),
	(1493, 1, 59, NULL, NULL),
	(1494, 44, 59, NULL, NULL),
	(1495, 9, 59, NULL, NULL),
	(1496, 37, 59, NULL, NULL),
	(1497, 39, 59, NULL, NULL),
	(1498, 38, 59, NULL, NULL),
	(1499, 26, 59, NULL, NULL),
	(1500, 41, 59, NULL, NULL),
	(1501, 4, 59, NULL, NULL),
	(1502, 2, 60, NULL, NULL),
	(1503, 1, 60, NULL, NULL),
	(1504, 2, 61, NULL, NULL),
	(1505, 1, 61, NULL, NULL),
	(1506, 44, 60, NULL, NULL),
	(1507, 9, 60, NULL, NULL),
	(1508, 37, 60, NULL, NULL),
	(1509, 39, 60, NULL, NULL),
	(1510, 38, 60, NULL, NULL),
	(1511, 26, 60, NULL, NULL),
	(1512, 41, 60, NULL, NULL),
	(1513, 22, 60, NULL, NULL),
	(1514, 4, 60, NULL, NULL),
	(1515, 44, 61, NULL, NULL),
	(1516, 9, 61, NULL, NULL),
	(1517, 37, 61, NULL, NULL),
	(1518, 39, 61, NULL, NULL),
	(1519, 38, 61, NULL, NULL),
	(1520, 26, 61, NULL, NULL),
	(1521, 41, 61, NULL, NULL),
	(1522, 22, 61, NULL, NULL),
	(1523, 4, 61, NULL, NULL),
	(1524, 29, 63, NULL, NULL),
	(1526, 30, 63, NULL, NULL),
	(1527, 44, 63, NULL, NULL),
	(1528, 9, 63, NULL, NULL),
	(1531, 1, 65, NULL, NULL),
	(1532, 2, 65, NULL, NULL),
	(1533, 3, 65, NULL, NULL),
	(1534, 4, 65, NULL, NULL),
	(1535, 5, 65, NULL, NULL),
	(1536, 6, 65, NULL, NULL),
	(1537, 7, 65, NULL, NULL),
	(1538, 8, 65, NULL, NULL),
	(1539, 9, 65, NULL, NULL),
	(1540, 10, 65, NULL, NULL),
	(1541, 11, 65, NULL, NULL),
	(1542, 12, 65, NULL, NULL),
	(1543, 13, 65, NULL, NULL),
	(1544, 14, 65, NULL, NULL),
	(1545, 15, 65, NULL, NULL),
	(1546, 16, 65, NULL, NULL),
	(1547, 17, 65, NULL, NULL),
	(1548, 18, 65, NULL, NULL),
	(1549, 19, 65, NULL, NULL),
	(1550, 20, 65, NULL, NULL),
	(1551, 21, 65, NULL, NULL),
	(1552, 22, 65, NULL, NULL),
	(1553, 23, 65, NULL, NULL),
	(1554, 24, 65, NULL, NULL);
INSERT INTO `permission_role` (`id`, `permission_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(1555, 25, 65, NULL, NULL),
	(1556, 26, 65, NULL, NULL),
	(1557, 27, 65, NULL, NULL),
	(1558, 28, 65, NULL, NULL),
	(1559, 29, 65, NULL, NULL),
	(1560, 30, 65, NULL, NULL),
	(1561, 31, 65, NULL, NULL),
	(1562, 32, 65, NULL, NULL),
	(1563, 33, 65, NULL, NULL),
	(1564, 34, 65, NULL, NULL),
	(1565, 35, 65, NULL, NULL),
	(1566, 36, 65, NULL, NULL),
	(1567, 37, 65, NULL, NULL),
	(1568, 38, 65, NULL, NULL),
	(1569, 39, 65, NULL, NULL),
	(1570, 40, 65, NULL, NULL),
	(1571, 41, 65, NULL, NULL),
	(1572, 42, 65, NULL, NULL),
	(1573, 43, 65, NULL, NULL),
	(1574, 44, 65, NULL, NULL),
	(1575, 1, 66, NULL, NULL),
	(1576, 2, 66, NULL, NULL),
	(1577, 3, 66, NULL, NULL),
	(1578, 4, 66, NULL, NULL),
	(1579, 5, 66, NULL, NULL),
	(1580, 6, 66, NULL, NULL),
	(1581, 7, 66, NULL, NULL),
	(1582, 8, 66, NULL, NULL),
	(1583, 9, 66, NULL, NULL),
	(1584, 10, 66, NULL, NULL),
	(1585, 11, 66, NULL, NULL),
	(1586, 12, 66, NULL, NULL),
	(1587, 13, 66, NULL, NULL),
	(1588, 14, 66, NULL, NULL),
	(1589, 15, 66, NULL, NULL),
	(1590, 16, 66, NULL, NULL),
	(1591, 17, 66, NULL, NULL),
	(1592, 18, 66, NULL, NULL),
	(1593, 19, 66, NULL, NULL),
	(1594, 20, 66, NULL, NULL),
	(1595, 21, 66, NULL, NULL),
	(1596, 22, 66, NULL, NULL),
	(1597, 23, 66, NULL, NULL),
	(1598, 24, 66, NULL, NULL),
	(1599, 25, 66, NULL, NULL),
	(1600, 26, 66, NULL, NULL),
	(1601, 27, 66, NULL, NULL),
	(1602, 28, 66, NULL, NULL),
	(1603, 37, 66, NULL, NULL),
	(1604, 38, 66, NULL, NULL),
	(1605, 39, 66, NULL, NULL),
	(1606, 40, 66, NULL, NULL),
	(1607, 41, 66, NULL, NULL),
	(1608, 43, 66, NULL, NULL),
	(1609, 44, 66, NULL, NULL),
	(1610, 1, 67, NULL, NULL),
	(1611, 2, 67, NULL, NULL),
	(1612, 3, 67, NULL, NULL),
	(1613, 4, 67, NULL, NULL),
	(1614, 5, 67, NULL, NULL),
	(1615, 6, 67, NULL, NULL),
	(1616, 7, 67, NULL, NULL),
	(1617, 8, 67, NULL, NULL),
	(1618, 9, 67, NULL, NULL),
	(1619, 10, 67, NULL, NULL),
	(1620, 11, 67, NULL, NULL),
	(1621, 12, 67, NULL, NULL),
	(1622, 13, 67, NULL, NULL),
	(1623, 14, 67, NULL, NULL),
	(1624, 15, 67, NULL, NULL),
	(1625, 16, 67, NULL, NULL),
	(1626, 17, 67, NULL, NULL),
	(1627, 18, 67, NULL, NULL),
	(1628, 19, 67, NULL, NULL),
	(1629, 20, 67, NULL, NULL),
	(1630, 21, 67, NULL, NULL),
	(1631, 22, 67, NULL, NULL),
	(1632, 23, 67, NULL, NULL),
	(1633, 24, 67, NULL, NULL),
	(1634, 25, 67, NULL, NULL),
	(1635, 26, 67, NULL, NULL),
	(1636, 27, 67, NULL, NULL),
	(1637, 28, 67, NULL, NULL),
	(1638, 29, 67, NULL, NULL),
	(1639, 30, 67, NULL, NULL),
	(1640, 31, 67, NULL, NULL),
	(1641, 32, 67, NULL, NULL),
	(1642, 33, 67, NULL, NULL),
	(1643, 34, 67, NULL, NULL),
	(1644, 35, 67, NULL, NULL),
	(1645, 36, 67, NULL, NULL),
	(1646, 37, 67, NULL, NULL),
	(1647, 38, 67, NULL, NULL),
	(1648, 39, 67, NULL, NULL),
	(1649, 40, 67, NULL, NULL),
	(1650, 41, 67, NULL, NULL),
	(1651, 42, 67, NULL, NULL),
	(1652, 43, 67, NULL, NULL),
	(1653, 44, 67, NULL, NULL),
	(1654, 1, 68, NULL, NULL),
	(1655, 2, 68, NULL, NULL),
	(1656, 3, 68, NULL, NULL),
	(1657, 4, 68, NULL, NULL),
	(1658, 5, 68, NULL, NULL),
	(1659, 6, 68, NULL, NULL),
	(1660, 7, 68, NULL, NULL),
	(1661, 8, 68, NULL, NULL),
	(1662, 9, 68, NULL, NULL),
	(1663, 10, 68, NULL, NULL),
	(1664, 11, 68, NULL, NULL),
	(1665, 12, 68, NULL, NULL),
	(1666, 13, 68, NULL, NULL),
	(1667, 14, 68, NULL, NULL),
	(1668, 15, 68, NULL, NULL),
	(1669, 16, 68, NULL, NULL),
	(1670, 17, 68, NULL, NULL),
	(1671, 18, 68, NULL, NULL),
	(1672, 19, 68, NULL, NULL),
	(1673, 20, 68, NULL, NULL),
	(1674, 21, 68, NULL, NULL),
	(1675, 22, 68, NULL, NULL),
	(1676, 23, 68, NULL, NULL),
	(1677, 24, 68, NULL, NULL),
	(1678, 25, 68, NULL, NULL),
	(1679, 26, 68, NULL, NULL),
	(1680, 27, 68, NULL, NULL),
	(1681, 28, 68, NULL, NULL),
	(1682, 37, 68, NULL, NULL),
	(1683, 38, 68, NULL, NULL),
	(1684, 39, 68, NULL, NULL),
	(1685, 40, 68, NULL, NULL),
	(1686, 41, 68, NULL, NULL),
	(1687, 43, 68, NULL, NULL),
	(1688, 1, 69, NULL, NULL),
	(1689, 2, 69, NULL, NULL),
	(1690, 3, 69, NULL, NULL),
	(1691, 4, 69, NULL, NULL),
	(1692, 5, 69, NULL, NULL),
	(1693, 6, 69, NULL, NULL),
	(1694, 7, 69, NULL, NULL),
	(1695, 8, 69, NULL, NULL),
	(1696, 9, 69, NULL, NULL),
	(1697, 10, 69, NULL, NULL),
	(1698, 11, 69, NULL, NULL),
	(1699, 12, 69, NULL, NULL),
	(1700, 13, 69, NULL, NULL),
	(1701, 14, 69, NULL, NULL),
	(1702, 15, 69, NULL, NULL),
	(1703, 16, 69, NULL, NULL),
	(1704, 17, 69, NULL, NULL),
	(1705, 18, 69, NULL, NULL),
	(1706, 19, 69, NULL, NULL),
	(1707, 20, 69, NULL, NULL),
	(1708, 21, 69, NULL, NULL),
	(1709, 22, 69, NULL, NULL),
	(1710, 23, 69, NULL, NULL),
	(1711, 24, 69, NULL, NULL),
	(1712, 25, 69, NULL, NULL),
	(1713, 26, 69, NULL, NULL),
	(1714, 27, 69, NULL, NULL),
	(1715, 28, 69, NULL, NULL),
	(1716, 29, 69, NULL, NULL),
	(1717, 30, 69, NULL, NULL),
	(1718, 31, 69, NULL, NULL),
	(1719, 32, 69, NULL, NULL),
	(1720, 33, 69, NULL, NULL),
	(1721, 34, 69, NULL, NULL),
	(1722, 35, 69, NULL, NULL),
	(1723, 36, 69, NULL, NULL),
	(1724, 37, 69, NULL, NULL),
	(1725, 38, 69, NULL, NULL),
	(1726, 39, 69, NULL, NULL),
	(1727, 40, 69, NULL, NULL),
	(1728, 41, 69, NULL, NULL),
	(1729, 42, 69, NULL, NULL),
	(1730, 43, 69, NULL, NULL),
	(1731, 44, 69, NULL, NULL),
	(1732, 1, 70, NULL, NULL),
	(1733, 2, 70, NULL, NULL),
	(1734, 3, 70, NULL, NULL),
	(1735, 4, 70, NULL, NULL),
	(1736, 5, 70, NULL, NULL),
	(1737, 6, 70, NULL, NULL),
	(1738, 7, 70, NULL, NULL),
	(1739, 8, 70, NULL, NULL),
	(1740, 9, 70, NULL, NULL),
	(1741, 10, 70, NULL, NULL),
	(1742, 11, 70, NULL, NULL),
	(1743, 12, 70, NULL, NULL),
	(1744, 13, 70, NULL, NULL),
	(1745, 14, 70, NULL, NULL),
	(1746, 15, 70, NULL, NULL),
	(1747, 16, 70, NULL, NULL),
	(1748, 17, 70, NULL, NULL),
	(1749, 18, 70, NULL, NULL),
	(1750, 19, 70, NULL, NULL),
	(1751, 20, 70, NULL, NULL),
	(1752, 21, 70, NULL, NULL),
	(1753, 22, 70, NULL, NULL),
	(1754, 23, 70, NULL, NULL),
	(1755, 24, 70, NULL, NULL),
	(1756, 25, 70, NULL, NULL),
	(1757, 26, 70, NULL, NULL),
	(1758, 27, 70, NULL, NULL),
	(1759, 28, 70, NULL, NULL),
	(1760, 37, 70, NULL, NULL),
	(1761, 38, 70, NULL, NULL),
	(1762, 39, 70, NULL, NULL),
	(1763, 40, 70, NULL, NULL),
	(1764, 41, 70, NULL, NULL),
	(1765, 43, 70, NULL, NULL),
	(1766, 3, 71, NULL, NULL),
	(1767, 36, 71, NULL, NULL),
	(1768, 35, 71, NULL, NULL),
	(1769, 42, 71, NULL, NULL),
	(1770, 8, 71, NULL, NULL),
	(1771, 44, 71, NULL, NULL),
	(1772, 9, 71, NULL, NULL),
	(1773, 1, 72, NULL, NULL),
	(1774, 2, 72, NULL, NULL),
	(1775, 3, 72, NULL, NULL),
	(1776, 4, 72, NULL, NULL),
	(1777, 5, 72, NULL, NULL),
	(1778, 6, 72, NULL, NULL),
	(1779, 7, 72, NULL, NULL),
	(1780, 8, 72, NULL, NULL),
	(1781, 9, 72, NULL, NULL),
	(1782, 10, 72, NULL, NULL),
	(1783, 11, 72, NULL, NULL),
	(1784, 12, 72, NULL, NULL),
	(1785, 13, 72, NULL, NULL),
	(1786, 14, 72, NULL, NULL),
	(1787, 15, 72, NULL, NULL),
	(1788, 16, 72, NULL, NULL),
	(1789, 17, 72, NULL, NULL),
	(1790, 18, 72, NULL, NULL),
	(1791, 19, 72, NULL, NULL),
	(1792, 20, 72, NULL, NULL),
	(1793, 21, 72, NULL, NULL),
	(1794, 22, 72, NULL, NULL),
	(1795, 23, 72, NULL, NULL),
	(1796, 24, 72, NULL, NULL),
	(1797, 25, 72, NULL, NULL),
	(1798, 26, 72, NULL, NULL),
	(1799, 27, 72, NULL, NULL),
	(1800, 28, 72, NULL, NULL),
	(1801, 29, 72, NULL, NULL),
	(1802, 30, 72, NULL, NULL),
	(1803, 31, 72, NULL, NULL),
	(1804, 32, 72, NULL, NULL);
INSERT INTO `permission_role` (`id`, `permission_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(1805, 33, 72, NULL, NULL),
	(1806, 34, 72, NULL, NULL),
	(1807, 35, 72, NULL, NULL),
	(1808, 36, 72, NULL, NULL),
	(1809, 37, 72, NULL, NULL),
	(1810, 38, 72, NULL, NULL),
	(1811, 39, 72, NULL, NULL),
	(1812, 40, 72, NULL, NULL),
	(1813, 41, 72, NULL, NULL),
	(1814, 42, 72, NULL, NULL),
	(1815, 43, 72, NULL, NULL),
	(1816, 44, 72, NULL, NULL),
	(1817, 1, 73, NULL, NULL),
	(1818, 2, 73, NULL, NULL),
	(1819, 3, 73, NULL, NULL),
	(1820, 4, 73, NULL, NULL),
	(1821, 5, 73, NULL, NULL),
	(1822, 6, 73, NULL, NULL),
	(1823, 7, 73, NULL, NULL),
	(1824, 8, 73, NULL, NULL),
	(1825, 9, 73, NULL, NULL),
	(1826, 10, 73, NULL, NULL),
	(1827, 11, 73, NULL, NULL),
	(1828, 12, 73, NULL, NULL),
	(1829, 13, 73, NULL, NULL),
	(1830, 14, 73, NULL, NULL),
	(1831, 15, 73, NULL, NULL),
	(1832, 16, 73, NULL, NULL),
	(1833, 17, 73, NULL, NULL),
	(1834, 18, 73, NULL, NULL),
	(1835, 19, 73, NULL, NULL),
	(1836, 20, 73, NULL, NULL),
	(1837, 21, 73, NULL, NULL),
	(1838, 22, 73, NULL, NULL),
	(1839, 23, 73, NULL, NULL),
	(1840, 24, 73, NULL, NULL),
	(1841, 25, 73, NULL, NULL),
	(1842, 26, 73, NULL, NULL),
	(1843, 27, 73, NULL, NULL),
	(1844, 28, 73, NULL, NULL),
	(1845, 37, 73, NULL, NULL),
	(1846, 38, 73, NULL, NULL),
	(1847, 39, 73, NULL, NULL),
	(1848, 40, 73, NULL, NULL),
	(1849, 41, 73, NULL, NULL),
	(1850, 43, 73, NULL, NULL),
	(1851, 1, 74, NULL, NULL),
	(1852, 2, 74, NULL, NULL),
	(1853, 3, 74, NULL, NULL),
	(1854, 4, 74, NULL, NULL),
	(1855, 5, 74, NULL, NULL),
	(1856, 6, 74, NULL, NULL),
	(1857, 7, 74, NULL, NULL),
	(1858, 8, 74, NULL, NULL),
	(1859, 9, 74, NULL, NULL),
	(1860, 10, 74, NULL, NULL),
	(1861, 11, 74, NULL, NULL),
	(1862, 12, 74, NULL, NULL),
	(1863, 13, 74, NULL, NULL),
	(1864, 14, 74, NULL, NULL),
	(1865, 15, 74, NULL, NULL),
	(1866, 16, 74, NULL, NULL),
	(1867, 17, 74, NULL, NULL),
	(1868, 18, 74, NULL, NULL),
	(1869, 19, 74, NULL, NULL),
	(1870, 20, 74, NULL, NULL),
	(1871, 21, 74, NULL, NULL),
	(1872, 22, 74, NULL, NULL),
	(1873, 23, 74, NULL, NULL),
	(1874, 24, 74, NULL, NULL),
	(1875, 25, 74, NULL, NULL),
	(1876, 26, 74, NULL, NULL),
	(1877, 27, 74, NULL, NULL),
	(1878, 28, 74, NULL, NULL),
	(1879, 29, 74, NULL, NULL),
	(1880, 30, 74, NULL, NULL),
	(1881, 31, 74, NULL, NULL),
	(1882, 32, 74, NULL, NULL),
	(1883, 33, 74, NULL, NULL),
	(1884, 34, 74, NULL, NULL),
	(1885, 35, 74, NULL, NULL),
	(1886, 36, 74, NULL, NULL),
	(1887, 37, 74, NULL, NULL),
	(1888, 38, 74, NULL, NULL),
	(1889, 39, 74, NULL, NULL),
	(1890, 40, 74, NULL, NULL),
	(1891, 41, 74, NULL, NULL),
	(1892, 42, 74, NULL, NULL),
	(1893, 43, 74, NULL, NULL),
	(1894, 44, 74, NULL, NULL),
	(1895, 1, 75, NULL, NULL),
	(1896, 2, 75, NULL, NULL),
	(1897, 3, 75, NULL, NULL),
	(1898, 4, 75, NULL, NULL),
	(1899, 5, 75, NULL, NULL),
	(1900, 6, 75, NULL, NULL),
	(1901, 7, 75, NULL, NULL),
	(1902, 8, 75, NULL, NULL),
	(1903, 9, 75, NULL, NULL),
	(1904, 10, 75, NULL, NULL),
	(1905, 11, 75, NULL, NULL),
	(1906, 12, 75, NULL, NULL),
	(1907, 13, 75, NULL, NULL),
	(1908, 14, 75, NULL, NULL),
	(1909, 15, 75, NULL, NULL),
	(1910, 16, 75, NULL, NULL),
	(1911, 17, 75, NULL, NULL),
	(1912, 18, 75, NULL, NULL),
	(1913, 19, 75, NULL, NULL),
	(1914, 20, 75, NULL, NULL),
	(1915, 21, 75, NULL, NULL),
	(1916, 22, 75, NULL, NULL),
	(1917, 23, 75, NULL, NULL),
	(1918, 24, 75, NULL, NULL),
	(1919, 25, 75, NULL, NULL),
	(1920, 26, 75, NULL, NULL),
	(1921, 27, 75, NULL, NULL),
	(1922, 28, 75, NULL, NULL),
	(1923, 37, 75, NULL, NULL),
	(1924, 38, 75, NULL, NULL),
	(1925, 39, 75, NULL, NULL),
	(1926, 40, 75, NULL, NULL),
	(1927, 41, 75, NULL, NULL),
	(1928, 43, 75, NULL, NULL);

/*!40000 ALTER TABLE `permission_role` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table priorities
# ------------------------------------------------------------

DROP TABLE IF EXISTS `priorities`;

CREATE TABLE `priorities` (
  `prio_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `prio_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`prio_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table product_features
# ------------------------------------------------------------

DROP TABLE IF EXISTS `product_features`;

CREATE TABLE `product_features` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `profeature_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table project_user
# ------------------------------------------------------------

DROP TABLE IF EXISTS `project_user`;

CREATE TABLE `project_user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `project_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `attachment_access` tinyint NOT NULL DEFAULT '1',
  `project_access` tinyint NOT NULL DEFAULT '1',
  `sprint_access` tinyint NOT NULL DEFAULT '1',
  `forum_access` tinyint NOT NULL DEFAULT '1',
  `userstory_access` tinyint NOT NULL DEFAULT '1',
  `secfeature_access` tinyint NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `project_user` WRITE;
/*!40000 ALTER TABLE `project_user` DISABLE KEYS */;

INSERT INTO `project_user` (`id`, `project_id`, `user_id`, `attachment_access`, `project_access`, `sprint_access`, `forum_access`, `userstory_access`, `secfeature_access`, `created_at`, `updated_at`) VALUES
	(1, 2, 4, 1, 1, 1, 1, 1, 1, NULL, NULL),
	(2, 4, 4, 1, 1, 1, 1, 1, 1, NULL, NULL),
	(3, 6, 4, 1, 1, 1, 1, 1, 1, NULL, NULL),
	(5, 8, 4, 1, 1, 1, 1, 1, 1, NULL, NULL),
	(7, 10, 8, 1, 1, 1, 1, 1, 1, '2025-03-20 12:47:26', '2025-03-20 12:47:26'),
	(8, 11, 7, 1, 1, 1, 1, 1, 1, '2025-03-25 14:04:15', '2025-03-25 14:04:15'),
	(10, 13, 4, 1, 1, 1, 1, 1, 1, '2025-05-22 15:29:11', '2025-05-22 15:29:11'),
	(22, 25, 21, 1, 1, 1, 1, 1, 1, '2025-05-28 14:28:37', '2025-05-28 14:28:37'),
	(23, 26, 4, 1, 1, 1, 1, 1, 1, '2025-06-06 07:42:25', '2025-06-06 07:42:25'),
	(24, 27, 25, 1, 1, 1, 1, 1, 1, '2025-06-08 07:23:57', '2025-06-08 07:23:57'),
	(25, 28, 25, 1, 1, 1, 1, 1, 1, '2025-06-09 14:27:49', '2025-06-09 14:27:49'),
	(26, 29, 27, 1, 1, 1, 1, 1, 1, '2025-06-11 15:33:28', '2025-06-11 15:33:28'),
	(27, 30, 27, 1, 1, 1, 1, 1, 1, '2025-06-11 15:33:59', '2025-06-11 15:33:59'),
	(28, 31, 27, 1, 1, 1, 1, 1, 1, '2025-06-12 10:00:41', '2025-06-12 10:00:41'),
	(29, 32, 4, 1, 1, 1, 1, 1, 1, '2025-06-12 10:06:42', '2025-06-12 10:06:42'),
	(30, 33, 24, 1, 1, 1, 1, 1, 1, '2025-06-12 10:07:18', '2025-06-12 10:07:18'),
	(31, 34, 4, 1, 1, 1, 1, 1, 1, '2025-06-16 04:20:54', '2025-06-16 04:20:54'),
	(32, 35, 29, 1, 1, 1, 1, 1, 1, '2025-06-18 07:15:59', '2025-06-18 07:15:59'),
	(33, 36, 29, 1, 1, 1, 1, 1, 1, '2025-06-19 08:05:21', '2025-06-19 08:05:21'),
	(34, 37, 29, 1, 1, 1, 1, 1, 1, '2025-06-21 14:29:38', '2025-06-21 14:29:38'),
	(35, 38, 29, 1, 1, 1, 1, 1, 1, '2025-06-23 03:50:36', '2025-06-23 03:50:36'),
	(36, 39, 29, 1, 1, 1, 1, 1, 1, '2025-06-23 03:52:42', '2025-06-23 03:52:42'),
	(37, 40, 29, 1, 1, 1, 1, 1, 1, '2025-06-24 03:10:49', '2025-06-24 03:10:49'),
	(38, 41, 25, 1, 1, 1, 1, 1, 1, '2025-06-25 10:44:30', '2025-06-25 10:44:30'),
	(39, 42, 29, 1, 1, 1, 1, 1, 1, '2025-06-26 09:48:25', '2025-06-26 09:48:25'),
	(40, 43, 29, 1, 1, 1, 1, 1, 1, '2025-06-26 09:57:10', '2025-06-26 09:57:10'),
	(41, 44, 29, 1, 1, 1, 1, 1, 1, '2025-06-26 10:06:36', '2025-06-26 10:06:36'),
	(42, 45, 30, 1, 1, 1, 1, 1, 1, '2025-06-27 04:07:25', '2025-06-27 04:07:25'),
	(43, 46, 25, 1, 1, 1, 1, 1, 1, '2025-06-27 11:40:33', '2025-06-27 11:40:33'),
	(44, 47, 31, 1, 1, 1, 1, 1, 1, '2025-07-01 08:00:55', '2025-07-01 08:00:55'),
	(45, 48, 34, 1, 1, 1, 1, 1, 1, '2025-07-01 12:31:46', '2025-07-01 12:31:46'),
	(46, 49, 33, 1, 1, 1, 1, 1, 1, '2025-07-01 12:35:43', '2025-07-01 12:35:43'),
	(47, 50, 34, 1, 1, 1, 1, 1, 1, '2025-07-01 12:36:37', '2025-07-01 12:36:37'),
	(48, 51, 37, 1, 1, 1, 1, 1, 1, '2025-07-01 12:37:36', '2025-07-01 12:37:36'),
	(49, 52, 32, 1, 1, 1, 1, 1, 1, '2025-07-01 12:43:38', '2025-07-01 12:43:38'),
	(50, 53, 32, 1, 1, 1, 1, 1, 1, '2025-07-01 12:50:56', '2025-07-01 12:50:56'),
	(51, 54, 29, 1, 1, 1, 1, 1, 1, '2025-07-06 14:29:21', '2025-07-06 14:29:21'),
	(52, 55, 29, 1, 1, 1, 1, 1, 1, '2025-07-06 23:52:37', '2025-07-06 23:52:37'),
	(53, 56, 4, 1, 1, 1, 1, 1, 1, '2025-07-07 00:37:08', '2025-07-07 00:37:08'),
	(54, 57, 29, 1, 1, 1, 1, 1, 1, '2025-07-07 01:33:06', '2025-07-07 01:33:06'),
	(55, 58, 32, 1, 1, 1, 1, 1, 1, '2025-08-18 08:31:33', '2025-08-18 08:31:33');

/*!40000 ALTER TABLE `project_user` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table projects
# ------------------------------------------------------------

DROP TABLE IF EXISTS `projects`;

CREATE TABLE `projects` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_name` varchar(191) NOT NULL,
  `proj_name` varchar(100) NOT NULL,
  `proj_desc` varchar(500) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `shareable_slug` varchar(191) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `projects` WRITE;
/*!40000 ALTER TABLE `projects` DISABLE KEYS */;

INSERT INTO `projects` (`id`, `team_name`, `proj_name`, `proj_desc`, `start_date`, `end_date`, `shareable_slug`, `created_at`, `updated_at`) VALUES
	(2, 'Testers', 'Lets', 'This is a description test for the size of the description limit', '2023-01-08', '2026-11-06', NULL, NULL, '2025-04-18 09:35:12'),
	(3, 'a', 'Project Demo', 'Project Demo Testing Creation Kanban', '2024-01-03', '2024-08-29', NULL, '2024-01-03 01:30:51', '2024-01-03 01:30:51'),
	(6, 'Testers', 'TestProj', 'test', '2024-01-16', '2026-03-04', NULL, '2024-01-16 11:50:22', '2025-05-27 22:53:47'),
	(8, 'Testers', 'Test1234', 'Jeevan', '2024-06-14', '2026-01-29', NULL, '2024-06-13 12:28:54', '2025-05-27 22:45:55'),
	(10, 'testTeamAdmin', 'testAdminAccess', 'admin Access to other projects', '2025-03-20', '2025-05-29', 'testadminaccess-NterZdex', '2025-03-20 12:47:26', '2025-06-15 13:35:04'),
	(11, 'testTeamAdmin', 'testAd', 'testaDADd', '2025-03-25', '2025-08-28', NULL, '2025-03-25 14:04:15', '2025-03-25 14:04:15'),
	(13, 'Testers', 'ProjectFlow', 'Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from beginning Testing Flow from begin', '2025-05-22', '2026-06-25', 'projectflow-MEbYmBJl', '2025-05-22 15:29:11', '2025-06-18 15:45:48'),
	(26, 'Testers', 'UATtest', 'This is a test for a UAT scope', '2025-06-06', '2026-09-01', NULL, '2025-06-06 07:42:25', '2025-06-06 07:42:25'),
	(29, 'TestTeam', 'ASDF', 'ASDF', '2025-06-12', '2026-05-26', NULL, '2025-06-11 15:33:28', '2025-06-11 15:33:28'),
	(31, 'new_team', 'TAUFIQ-PROJECT', 'asdf', '2025-06-12', '2025-06-20', NULL, '2025-06-12 10:00:41', '2025-06-12 10:00:41'),
	(33, 'new taufiq team', 'TAUFIQ123', 'TESTING SAGILE DIAG EDITOR', '2025-06-12', '2025-06-19', NULL, '2025-06-12 10:07:18', '2025-06-12 10:07:18'),
	(34, 'Testers', 'VoltView', 'VoltView is a comprehensive project management system designed to help teams plan, organise, and track projects efficiently.', '2025-06-16', '2026-03-26', 'voltview-Dy2cN0Rz', '2025-06-16 04:20:54', '2025-06-16 04:21:02'),
	(35, 'TestMails', 'TestProjInviAccess', 'adsadadasd', '2025-06-18', '2025-12-04', NULL, '2025-06-18 07:15:59', '2025-06-18 07:15:59'),
	(49, 'uatTestTeam_2', 'UATProjectTest_2', 'This is a project to perform user acceptance testing on SAgile\'s module', '2025-07-01', '2026-08-01', NULL, '2025-07-01 12:35:41', '2025-07-01 12:37:39'),
	(50, 'uatTestTeam_3', 'UATProjectTest_3', 'This is a project to perform user acceptance testing on SAgile', '2025-07-01', '2026-07-01', NULL, '2025-07-01 12:36:35', '2025-07-01 12:36:35'),
	(53, 'uatTestTeam_1', 'UATProjectTest_1', 'This is a project to perform user acceptance testing on SAgile', '2025-07-01', '2026-07-01', NULL, '2025-07-01 12:50:54', '2025-07-01 12:50:54'),
	(54, 'PSM2_DemoTeam', 'PSM2 Demo Project', 'This is a project to demonstrate features that require testing over a period of time. Covers burndown chart and sprint archives. This is an update', '2025-07-06', '2026-07-07', 'psm2-demo-project-rH31nekU', '2025-07-06 14:29:17', '2025-07-07 01:22:32'),
	(56, 'PSM2 Invite Team', 'Project Assignment Control', 'This is a project to test project assignment team member view', '2025-07-07', '2026-01-22', NULL, '2025-07-07 00:37:05', '2025-07-07 00:37:05'),
	(57, 'PS2Demo', 'PSM2Demo', 'this is a demo', '2025-07-07', '2025-11-25', NULL, '2025-07-07 01:33:03', '2025-07-07 01:33:03'),
	(58, 'uatTestTeam_1', 'Food Ordering System', 'A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.', '2025-08-18', '2025-12-31', 'food-ordering-system-VKTLgK8J', '2025-08-18 08:31:31', '2025-08-18 08:44:13');

/*!40000 ALTER TABLE `projects` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table replies
# ------------------------------------------------------------

DROP TABLE IF EXISTS `replies`;

CREATE TABLE `replies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `comment_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table roles
# ------------------------------------------------------------

DROP TABLE IF EXISTS `roles`;

CREATE TABLE `roles` (
  `role_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `role_name` varchar(191) NOT NULL,
  `team_id` bigint unsigned DEFAULT NULL,
  `project_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;

INSERT INTO `roles` (`role_id`, `role_name`, `team_id`, `project_id`, `created_at`, `updated_at`) VALUES
	(1, 'Developer', NULL, NULL, '2023-09-08 04:06:00', '2025-04-24 14:45:32'),
	(2, 'Project Manager', NULL, NULL, NULL, NULL),
	(3, 'Developer', NULL, 13, '2023-09-08 04:06:00', '2025-04-24 14:45:32'),
	(18, 'Project Manager', NULL, 13, '2025-06-21 11:21:06', '2025-06-21 11:21:06'),
	(20, 'Developer', NULL, 36, NULL, NULL),
	(21, 'Developers', NULL, 37, NULL, NULL),
	(22, 'Project Manager', NULL, 38, '2025-06-23 03:50:33', '2025-06-23 03:50:33'),
	(23, 'Developer', NULL, 38, '2025-06-23 03:50:33', '2025-06-23 03:50:33'),
	(24, 'Project Manager', NULL, 39, '2025-06-23 03:52:38', '2025-06-23 03:52:38'),
	(25, 'Developer', NULL, 39, '2025-06-23 03:52:38', '2025-06-23 03:52:38'),
	(26, 'Yahoo', NULL, 39, '2025-06-23 05:28:26', '2025-06-23 05:28:26'),
	(27, 'Project Manager', NULL, 40, '2025-06-24 03:10:40', '2025-06-24 03:10:40'),
	(28, 'Developer', NULL, 40, '2025-06-24 03:10:40', '2025-06-24 03:10:40'),
	(29, 'Project Manager', NULL, 41, '2025-06-25 10:44:25', '2025-06-25 10:44:25'),
	(30, 'Developer', NULL, 41, '2025-06-25 10:44:25', '2025-06-25 10:44:25'),
	(35, 'Project Manager', NULL, 44, '2025-06-26 10:06:32', '2025-06-26 10:06:32'),
	(36, 'Developer', NULL, 44, '2025-06-26 10:06:32', '2025-06-26 10:06:32'),
	(37, 'Project Manager', NULL, 45, '2025-06-27 04:07:21', '2025-06-27 04:07:21'),
	(38, 'Developer', NULL, 45, '2025-06-27 04:07:21', '2025-06-27 04:07:21'),
	(39, 'Project Manager', NULL, 28, NULL, NULL),
	(40, 'Developer', NULL, 28, '2025-06-27 10:53:11', '2025-06-27 10:53:11'),
	(41, 'Project Manager', NULL, 46, '2025-06-27 11:40:31', '2025-06-27 11:40:31'),
	(42, 'Developer', NULL, 46, '2025-06-27 11:40:31', '2025-06-27 11:40:31'),
	(43, 'Project Manager', NULL, 47, '2025-07-01 08:00:52', '2025-07-01 08:00:52'),
	(44, 'Developer', NULL, 47, '2025-07-01 08:00:52', '2025-07-01 08:00:52'),
	(46, 'Project Manager', NULL, 48, '2025-07-01 12:31:43', '2025-07-01 12:31:43'),
	(47, 'Developer', NULL, 48, '2025-07-01 12:31:43', '2025-07-01 12:31:43'),
	(48, 'Project Manager', NULL, 49, '2025-07-01 12:35:41', '2025-07-01 12:35:41'),
	(49, 'Developer', NULL, 49, '2025-07-01 12:35:41', '2025-07-01 12:35:41'),
	(50, 'Project Manager', NULL, 50, '2025-07-01 12:36:35', '2025-07-01 12:36:35'),
	(51, 'Developer', NULL, 50, '2025-07-01 12:36:35', '2025-07-01 12:36:35'),
	(52, 'Project Manager', NULL, 51, '2025-07-01 12:37:34', '2025-07-01 12:37:34'),
	(53, 'Developer', NULL, 51, '2025-07-01 12:37:34', '2025-07-01 12:37:34'),
	(54, 'Project Manager', NULL, 52, '2025-07-01 12:43:36', '2025-07-01 12:43:36'),
	(55, 'Developer', NULL, 52, '2025-07-01 12:43:36', '2025-07-01 12:43:36'),
	(56, 'Project Manager', NULL, 53, '2025-07-01 12:50:54', '2025-07-01 12:50:54'),
	(57, 'Developer', NULL, 53, '2025-07-01 12:50:54', '2025-07-01 12:50:54'),
	(60, 'Specialist', NULL, 53, '2025-07-01 13:23:55', '2025-07-01 13:25:02'),
	(61, 'Specialist', NULL, 49, '2025-07-01 13:24:44', '2025-07-01 13:25:41'),
	(62, 'Project Manager', NULL, 34, NULL, NULL),
	(65, 'Project Manager', NULL, 54, '2025-07-06 14:29:17', '2025-07-06 14:29:17'),
	(66, 'Developer', NULL, 54, '2025-07-06 14:29:17', '2025-07-06 14:29:17'),
	(67, 'Project Manager', NULL, 55, '2025-07-06 23:52:35', '2025-07-06 23:52:35'),
	(68, 'Developer', NULL, 55, '2025-07-06 23:52:35', '2025-07-06 23:52:35'),
	(69, 'Project Manager', NULL, 56, '2025-07-07 00:37:05', '2025-07-07 00:37:05'),
	(70, 'Developer', NULL, 56, '2025-07-07 00:37:05', '2025-07-07 00:37:05'),
	(71, 'Specialist', NULL, 54, '2025-07-07 01:30:30', '2025-07-07 01:30:30'),
	(72, 'Project Manager', NULL, 57, '2025-07-07 01:33:03', '2025-07-07 01:33:03'),
	(73, 'Developer', NULL, 57, '2025-07-07 01:33:03', '2025-07-07 01:33:03'),
	(74, 'Project Manager', NULL, 58, '2025-08-18 08:31:31', '2025-08-18 08:31:31'),
	(75, 'Developer', NULL, 58, '2025-08-18 08:31:31', '2025-08-18 08:31:31');

/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table security_feature_user_story
# ------------------------------------------------------------

DROP TABLE IF EXISTS `security_feature_user_story`;

CREATE TABLE `security_feature_user_story` (
  `my_row_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `secfeature_id` bigint unsigned NOT NULL,
  `user_story_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`my_row_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table security_features
# ------------------------------------------------------------

DROP TABLE IF EXISTS `security_features`;

CREATE TABLE `security_features` (
  `secfeature_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `secfeature_name` varchar(191) NOT NULL,
  `secfeature_desc` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`secfeature_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `security_features` WRITE;
/*!40000 ALTER TABLE `security_features` DISABLE KEYS */;

INSERT INTO `security_features` (`secfeature_id`, `secfeature_name`, `secfeature_desc`, `created_at`, `updated_at`) VALUES
	(1, 'TestAddSec', 'Security Description Test', '2024-01-04 04:28:57', '2024-01-04 04:28:57');

/*!40000 ALTER TABLE `security_features` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table sprint
# ------------------------------------------------------------

DROP TABLE IF EXISTS `sprint`;

CREATE TABLE `sprint` (
  `sprint_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sprint_name` varchar(100) NOT NULL,
  `sprint_desc` varchar(300) NOT NULL,
  `start_sprint` date NOT NULL,
  `end_sprint` date NOT NULL,
  `active_sprint` tinyint DEFAULT NULL,
  `proj_name` varchar(191) NOT NULL,
  `users_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`sprint_id`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `sprint` WRITE;
/*!40000 ALTER TABLE `sprint` DISABLE KEYS */;

INSERT INTO `sprint` (`sprint_id`, `sprint_name`, `sprint_desc`, `start_sprint`, `end_sprint`, `active_sprint`, `proj_name`, `users_name`, `created_at`, `updated_at`) VALUES
	(1, 'Sprint 1', 'Sprint to create manage sprint progress', '2023-12-03', '2023-12-09', NULL, 'Lets', 'ammar-', '2023-12-12 16:17:20', '2025-03-25 18:09:23'),
	(2, 'Sprint 2', 'SprintDesc2', '2023-05-08', '2023-12-20', NULL, 'Lets', 'ammar-', '2023-12-20 01:54:27', '2024-01-03 01:31:33'),
	(3, 'asdasd', 'Sprint to create manage sprint progress', '2023-12-03', '2023-12-17', NULL, 'Lets', '4', '2023-12-24 14:56:01', '2025-03-20 14:51:40'),
	(4, 'Test Sprint 3', 'Test make username include', '2023-12-03', '2023-12-26', NULL, 'Lets', 'Ammar', '2023-12-24 14:58:43', '2025-03-20 14:51:44'),
	(5, 'Test Sprint 4', 'Test make username not name', '2023-12-01', '2023-12-22', NULL, 'Lets', 'ammar-', '2023-12-24 14:59:50', '2025-03-20 14:52:12'),
	(6, 'Test for validation editing', 'Test for current date before sprint date', '2024-01-03', '2024-01-23', NULL, 'Lets', 'ammar-', '2024-01-02 09:01:30', '2025-03-20 14:52:14'),
	(7, 'Tasuhrfhqeryhqr', 'qW234HG', '2023-02-07', '2023-11-01', NULL, 'Lets', 'ammar-', '2024-01-08 14:54:25', '2025-03-20 14:52:16'),
	(8, 'test', 'Sprint to create manage sprint progress', '2024-01-09', '2024-01-31', NULL, 'Lets', 'ammar-', '2024-01-09 05:29:53', '2025-03-20 14:52:18'),
	(11, 'ddqwdqwdqwedqw', 'qdqdqw', '2024-01-16', '2024-02-13', NULL, 'TestProj', 'ammar-', '2024-01-16 11:50:41', '2025-03-20 14:51:56'),
	(13, 'final presentation', 'good', '2024-01-14', '2024-01-28', NULL, 'Lets', 'ammar-', '2024-01-17 00:42:57', '2025-03-20 14:52:21'),
	(14, 'wqddqqdqw', 'cwdfqwfdw', '2024-01-22', '2024-02-05', NULL, 'Lets', 'ammar-', '2024-01-30 06:52:21', '2025-03-25 18:10:03'),
	(15, 'New Burndown Test', 'Test New Burndown Implementation', '2025-03-23', '2025-04-13', 0, 'Lets', 'ammar-', '2025-03-26 23:56:04', '2025-03-26 23:57:59'),
	(16, 'UI Update Test', 'Testing new UI Burndown & Kanban Implementation', '2025-04-14', '2025-04-29', 2, 'Lets', 'ammar-', '2025-04-17 23:40:48', '2025-05-27 23:26:55'),
	(20, 'TestSprintFlow', 'new Sagile sprint flow test', '2025-05-22', '2025-06-05', 2, 'ProjectFlow', 'ammar-', '2025-05-22 15:31:35', '2025-05-27 23:17:14'),
	(21, 'Test Sprint Flow', 'TestNewSprint', '2025-05-27', '2025-06-10', 2, 'Test1234', 'ammar-', '2025-05-27 22:46:12', '2025-05-27 23:26:13'),
	(22, 'newSprintTest', 'asdasdsadas', '2025-05-27', '2025-06-10', 2, 'TestProj', 'ammar-', '2025-05-27 22:54:27', '2025-05-27 23:12:07'),
	(23, 'newSprint Testing', 'asdasd', '2025-05-28', '2025-06-11', 2, 'ProjectFlow', 'ammar-', '2025-05-28 10:51:18', '2025-05-28 10:55:18'),
	(24, 'testSprintNewas', 'asdasdasd', '2025-05-28', '2025-06-11', 2, 'ProjectFlow', 'ammar-', '2025-05-28 10:55:33', '2025-05-28 11:14:30'),
	(25, 'TestArchive', 'TestCompleteSprintArchive', '2025-05-28', '2025-06-11', 2, 'ProjectFlow', 'ammar-', '2025-05-28 11:31:36', '2025-06-18 10:40:26'),
	(26, 'TestSprintDEmo', 'TestSprintDEmo', '2025-05-30', '2025-06-13', 2, 'Test1234', 'ammar-', '2025-05-30 04:07:25', '2025-05-30 04:07:51'),
	(28, 'TestSprint123', 'asdasd', '2025-06-11', '2025-06-25', 1, 'Test1234', 'ammar-', '2025-06-11 06:28:08', '2025-06-11 06:28:08'),
	(31, 'Sprint 1', 'Sprint 1 focuses on building the basic task creation, assignment, status tracking, and progress metrics for VoltView.', '2025-06-12', '2025-07-08', 1, 'VoltView', 'ammar-', '2025-06-16 04:32:19', '2025-06-16 04:32:19'),
	(32, 'Sprint 1', 'Test sprint', '2025-06-18', '2025-07-02', 1, 'ProjectFlow', 'ammar-', '2025-06-18 15:53:34', '2025-06-18 15:53:34'),
	(37, 'UAT3_Sprint', 'This is a sprint to test UAT', '2025-07-01', '2025-07-15', 2, 'UATProjectTest_3', 'UAT_User3', '2025-07-01 12:47:56', '2025-07-01 12:49:47'),
	(38, 'UAT_Sprint', 'This is a sprint to test UAT', '2025-07-01', '2025-07-15', 1, 'UATProjectTest_3', 'UAT_User3', '2025-07-01 12:51:51', '2025-07-01 12:51:51'),
	(40, 'UAT2_Sprint', 'This is a sprint to test UAT', '2025-07-01', '2025-07-15', 2, 'UATProjectTest_2', 'UAT_User2', '2025-07-01 12:57:16', '2025-07-01 13:06:07'),
	(41, 'UAT1_Sprint', 'This is a sprint to test UAT', '2025-07-01', '2025-07-15', 2, 'UATProjectTest_1', 'UAT_User1', '2025-07-01 13:05:33', '2025-07-01 13:29:30'),
	(42, 'UAT2_Sprint2', 'This is a sprint to test UAT', '2025-07-15', '2025-07-29', 2, 'UATProjectTest_2', 'UAT_User2', '2025-07-01 13:07:56', '2025-07-01 13:23:38'),
	(48, 'PSM2 Demo Archive Sprint', 'This is a sprint created for the archive test of the kanban and burndown chart in PSM2', '2025-06-29', '2025-07-13', 2, 'PSM2 Demo Project', 'ammarjmldnout', '2025-07-06 14:57:01', '2025-07-06 14:59:45'),
	(49, 'PSM2 Current Sprint Demo', 'Testing Current Sprint features', '2025-07-06', '2025-07-20', 2, 'PSM2 Demo Project', 'ammarjmldnout', '2025-07-06 15:12:02', '2025-07-07 01:29:12'),
	(50, 'Sprint 3', 'Sprint 3', '2025-07-07', '2025-07-21', 1, 'PSM2 Demo Project', 'ammarjmldnout', '2025-07-07 01:29:44', '2025-07-07 01:29:44'),
	(51, 'Sprint 4', 'This is sprint 4', '2025-08-04', '2025-08-18', 1, 'Project Assignment Control', 'ammarjmldnout', '2025-08-04 07:12:51', '2025-08-04 07:12:51'),
	(52, 'Sprint 1_Food Ordering System', 'this is sprint 1 for food ordering system', '2025-08-18', '2025-09-01', 2, 'Food Ordering System', 'UAT_User1', '2025-08-18 08:38:07', '2025-08-18 08:56:48'),
	(53, 'Sprint 2_Food Ordering System', 'This is sprint 2', '2025-08-18', '2025-09-08', 1, 'Food Ordering System', 'UAT_User1', '2025-08-18 09:01:01', '2025-08-18 09:01:01');

/*!40000 ALTER TABLE `sprint` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table sprint_archives
# ------------------------------------------------------------

DROP TABLE IF EXISTS `sprint_archives`;

CREATE TABLE `sprint_archives` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sprint_id` bigint unsigned NOT NULL,
  `kanban_state` longtext,
  `burndown_data` longtext,
  `archived_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `sprint_archives` WRITE;
/*!40000 ALTER TABLE `sprint_archives` DISABLE KEYS */;

INSERT INTO `sprint_archives` (`id`, `sprint_id`, `kanban_state`, `burndown_data`, `created_at`, `updated_at`) VALUES
	(1, 24, '{\"82\":[],\"83\":[],\"84\":[{\"id\":77,\"title\":\"NewTaskForDB2\",\"description\":\"test task\",\"order\":1,\"status_id\":\"84\",\"userstory_id\":24,\"sprint_id\":24,\"proj_id\":13,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-05-22T07:31:18.000000Z\",\"updated_at\":\"2025-05-28T02:55:33.000000Z\",\"user_names\":\"[\\\"ammar-\\\"]\",\"newTask_update\":\"2025-05-22\"}],\"85\":[]}', '{\"idealData\":[1,0.9333333333333333,0.8666666666666667,0.8,0.7333333333333334,0.6666666666666667,0.6,0.5333333333333333,0.4666666666666667,0.4,0.33333333333333337,0.2666666666666667,0.19999999999999996,0.1333333333333333,0.06666666666666665,0],\"actualData\":[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-05-28 11:14:30', '2025-05-28 11:14:30'),
	(2, 26, '{\"53\":[{\"id\":79,\"title\":\"TaskTesting123-1\",\"description\":\"test task\",\"order\":0,\"status_id\":\"53\",\"userstory_id\":26,\"sprint_id\":26,\"proj_id\":8,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-05-27T22:40:01.000000Z\",\"updated_at\":\"2025-05-30T04:07:25.000000Z\",\"user_names\":\"[\\\"ammar-\\\"]\",\"newTask_update\":\"2025-05-27\"},{\"id\":80,\"title\":\"TaskTesting123-2\",\"description\":\"TaskTesting123-2\",\"order\":0,\"status_id\":\"53\",\"userstory_id\":26,\"sprint_id\":26,\"proj_id\":8,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-05-27T22:40:13.000000Z\",\"updated_at\":\"2025-05-30T04:07:36.000000Z\",\"user_names\":\"[\\\"ammar-\\\"]\",\"newTask_update\":\"2025-05-27\"}],\"54\":[],\"55\":[],\"56\":[]}', '{\"idealData\":[2,1.8666666666666667,1.7333333333333334,1.6,1.4666666666666668,1.3333333333333335,1.2,1.0666666666666667,0.9333333333333333,0.8,0.6666666666666667,0.5333333333333334,0.3999999999999999,0.2666666666666666,0.1333333333333333,0],\"actualData\":[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2]}', '2025-05-30 04:07:50', '2025-05-30 04:07:50'),
	(3, 29, '{\"142\":[],\"143\":[],\"144\":[{\"id\":85,\"title\":\"UAT Task2 Update\",\"description\":\"This is an update to UAT Task2\",\"order\":1,\"status_id\":\"144\",\"userstory_id\":31,\"sprint_id\":29,\"proj_id\":28,\"start_date\":\"2025-06-14\",\"end_date\":\"2025-06-21\",\"completion_date\":null,\"created_at\":\"2025-06-12T15:21:57.000000Z\",\"updated_at\":\"2025-06-13T01:56:18.000000Z\",\"user_names\":\"[\\\"UAT_1\\\"]\",\"newTask_update\":\"2025-06-13\"}],\"145\":[{\"id\":83,\"title\":\"Task_UAT1\",\"description\":\"This is a task to test the UAT for SAgile\'s task update\",\"order\":1,\"status_id\":\"145\",\"userstory_id\":31,\"sprint_id\":29,\"proj_id\":28,\"start_date\":null,\"end_date\":null,\"completion_date\":\"2025-06-15\",\"created_at\":\"2025-06-10T04:59:59.000000Z\",\"updated_at\":\"2025-06-15T09:32:16.000000Z\",\"user_names\":\"[\\\"UAT_1\\\"]\",\"newTask_update\":\"2025-06-12\"}]}', '{\"idealData\":[2,1.8666666666666667,1.7333333333333334,1.6,1.4666666666666668,1.3333333333333335,1.2,1.0666666666666667,0.9333333333333333,0.8,0.6666666666666667,0.5333333333333334,0.3999999999999999,0.2666666666666666,0.1333333333333333,0],\"actualData\":[2,2,2,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-06-15 10:34:49', '2025-06-15 10:34:49'),
	(4, 25, '{\"82\":[],\"83\":[],\"84\":[],\"85\":[{\"id\":77,\"title\":\"NewTaskForDB2\",\"description\":\"test task\",\"order\":1,\"status_id\":\"85\",\"userstory_id\":24,\"sprint_id\":25,\"proj_id\":13,\"start_date\":null,\"end_date\":null,\"completion_date\":\"2025-06-18\",\"created_at\":\"2025-05-22T15:31:18.000000Z\",\"updated_at\":\"2025-06-18T10:40:14.000000Z\",\"user_names\":\"[\\\"ammar-\\\"]\",\"newTask_update\":\"2025-05-22\"}]}', '{\"idealData\":[1,0.9333333333333333,0.8666666666666667,0.8,0.7333333333333334,0.6666666666666667,0.6,0.5333333333333333,0.4666666666666667,0.4,0.33333333333333337,0.2666666666666667,0.19999999999999996,0.1333333333333333,0.06666666666666665,0],\"actualData\":[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-06-18 10:40:26', '2025-06-18 10:40:26'),
	(5, 36, '{\"185\":[],\"186\":[],\"187\":[],\"188\":[]}', '{\"idealData\":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],\"actualData\":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}', '2025-06-26 10:26:24', '2025-06-26 10:26:24'),
	(6, 35, '{\"197\":[],\"198\":[],\"199\":[],\"200\":[{\"id\":97,\"title\":\"newTaskTestingNew\",\"description\":\"asdadadd\",\"order\":1,\"status_id\":\"200\",\"userstory_id\":42,\"sprint_id\":35,\"proj_id\":40,\"start_date\":null,\"end_date\":null,\"completion_date\":\"2025-07-01\",\"created_at\":\"2025-06-24T16:08:36.000000Z\",\"updated_at\":\"2025-07-01T09:34:23.000000Z\",\"user_names\":\"[\\\"ammarjmldnout\\\"]\",\"newTask_update\":\"2025-06-25\"}]}', '{\"idealData\":[1,0.9333333333333333,0.8666666666666667,0.8,0.7333333333333334,0.6666666666666667,0.6,0.5333333333333333,0.4666666666666667,0.4,0.33333333333333337,0.2666666666666667,0.19999999999999996,0.1333333333333333,0.06666666666666665,0],\"actualData\":[1,1,1,1,1,0,0,0,0,0,0,0,0,0,0]}', '2025-07-01 09:34:30', '2025-07-01 09:34:30'),
	(7, 37, '{\"238\":[{\"id\":108,\"title\":\"Task_UAT3\",\"description\":\"This is a task to test the UAT for SAgile\",\"order\":1,\"status_id\":\"238\",\"userstory_id\":49,\"sprint_id\":37,\"proj_id\":50,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-01T12:49:12.000000Z\",\"updated_at\":\"2025-07-01T12:49:20.000000Z\",\"user_names\":\"[\\\"UAT_User3\\\"]\",\"newTask_update\":\"2025-07-01\"}],\"239\":[],\"240\":[],\"241\":[]}', '{\"idealData\":[1,0.9333333333333333,0.8666666666666667,0.8,0.7333333333333334,0.6666666666666667,0.6,0.5333333333333333,0.4666666666666667,0.4,0.33333333333333337,0.2666666666666667,0.19999999999999996,0.1333333333333333,0.06666666666666665,0],\"actualData\":[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-07-01 12:49:47', '2025-07-01 12:49:47'),
	(8, 39, '{\"242\":[{\"id\":107,\"title\":\"Develop a task list view\",\"description\":\"asdadasdasasdsdada\",\"order\":1,\"status_id\":\"242\",\"userstory_id\":51,\"sprint_id\":39,\"proj_id\":51,\"start_date\":\"2025-07-01\",\"end_date\":\"2025-07-14\",\"completion_date\":null,\"created_at\":\"2025-07-01T12:46:53.000000Z\",\"updated_at\":\"2025-07-01T12:55:41.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-01\"}],\"243\":[],\"244\":[],\"245\":[{\"id\":109,\"title\":\"asdadadadsadsadaasdsasadsadasdsa\",\"description\":\"asdsadasdad\",\"order\":1,\"status_id\":\"245\",\"userstory_id\":51,\"sprint_id\":39,\"proj_id\":51,\"start_date\":\"2025-07-01\",\"end_date\":\"2025-07-14\",\"completion_date\":\"2025-07-01\",\"created_at\":\"2025-07-01T12:56:05.000000Z\",\"updated_at\":\"2025-07-01T12:56:32.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-01\"}]}', '{\"idealData\":[2,1.8666666666666667,1.7333333333333334,1.6,1.4666666666666668,1.3333333333333335,1.2,1.0666666666666667,0.9333333333333333,0.8,0.6666666666666667,0.5333333333333334,0.3999999999999999,0.2666666666666666,0.1333333333333333,0],\"actualData\":[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-07-01 13:01:04', '2025-07-01 13:01:04'),
	(9, 40, '{\"234\":[],\"235\":[{\"id\":106,\"title\":\"Task_UAT2\",\"description\":\"This is a task to test the UAT for SAgile\'s task update\",\"order\":1,\"status_id\":\"235\",\"userstory_id\":50,\"sprint_id\":40,\"proj_id\":49,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-01T12:46:26.000000Z\",\"updated_at\":\"2025-07-01T12:57:16.000000Z\",\"user_names\":\"[\\\"UAT_User3\\\"]\",\"newTask_update\":\"2025-07-01\"}],\"236\":[],\"237\":[]}', '{\"idealData\":[1,0.9333333333333333,0.8666666666666667,0.8,0.7333333333333334,0.6666666666666667,0.6,0.5333333333333333,0.4666666666666667,0.4,0.33333333333333337,0.2666666666666667,0.19999999999999996,0.1333333333333333,0.06666666666666665,0],\"actualData\":[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-07-01 13:06:07', '2025-07-01 13:06:07'),
	(10, 42, '{\"234\":[{\"id\":117,\"title\":\"UAT2_Task3\",\"description\":\"This is task3 for UAT\",\"order\":1,\"status_id\":\"234\",\"userstory_id\":50,\"sprint_id\":42,\"proj_id\":49,\"start_date\":\"2025-07-01\",\"end_date\":\"2025-07-08\",\"completion_date\":null,\"created_at\":\"2025-07-01T13:11:54.000000Z\",\"updated_at\":\"2025-07-01T13:19:09.000000Z\",\"user_names\":\"[\\\"UAT_User3\\\"]\",\"newTask_update\":\"2025-07-01\"}],\"235\":[{\"id\":106,\"title\":\"Task_UAT2\",\"description\":\"This is a task to test the UAT for SAgile\'s task update\",\"order\":1,\"status_id\":\"235\",\"userstory_id\":50,\"sprint_id\":42,\"proj_id\":49,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-01T12:46:26.000000Z\",\"updated_at\":\"2025-07-01T13:07:57.000000Z\",\"user_names\":\"[\\\"UAT_User3\\\"]\",\"newTask_update\":\"2025-07-01\"}],\"236\":[],\"237\":[]}', '{\"idealData\":[2,1.8666666666666667,1.7333333333333334,1.6,1.4666666666666668,1.3333333333333335,1.2,1.0666666666666667,0.9333333333333333,0.8,0.6666666666666667,0.5333333333333334,0.3999999999999999,0.2666666666666666,0.1333333333333333,0],\"actualData\":[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2]}', '2025-07-01 13:23:37', '2025-07-01 13:23:37'),
	(11, 41, '{\"250\":[{\"id\":114,\"title\":\"Task_UAT2\",\"description\":\"This is the second task to test the UAT for SAgile\",\"order\":1,\"status_id\":\"250\",\"userstory_id\":53,\"sprint_id\":41,\"proj_id\":53,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-01T13:06:55.000000Z\",\"updated_at\":\"2025-07-01T13:07:16.000000Z\",\"user_names\":\"[\\\"UAT_User1\\\"]\",\"newTask_update\":\"2025-07-01\"}],\"251\":[{\"id\":110,\"title\":\"Task_UAT1\",\"description\":\"This is a task to test the UAT for SAgile\'s task update\",\"order\":1,\"status_id\":\"251\",\"userstory_id\":53,\"sprint_id\":41,\"proj_id\":53,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-01T13:01:51.000000Z\",\"updated_at\":\"2025-07-01T13:05:33.000000Z\",\"user_names\":\"[\\\"UAT_User1\\\"]\",\"newTask_update\":\"2025-07-01\"}],\"252\":[],\"253\":[]}', '{\"idealData\":[2,1.8666666666666667,1.7333333333333334,1.6,1.4666666666666668,1.3333333333333335,1.2,1.0666666666666667,0.9333333333333333,0.8,0.6666666666666667,0.5333333333333334,0.3999999999999999,0.2666666666666666,0.1333333333333333,0],\"actualData\":[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2]}', '2025-07-01 13:29:30', '2025-07-01 13:29:30'),
	(12, 43, '{\"217\":[{\"id\":121,\"title\":\"dsadasdasd\",\"description\":\"asdasdasdasd\",\"order\":1,\"status_id\":\"217\",\"userstory_id\":46,\"sprint_id\":43,\"proj_id\":45,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-01T16:49:18.000000Z\",\"updated_at\":\"2025-07-01T16:49:30.000000Z\",\"user_names\":\"[\\\"UserAcceptance\\\",\\\"ammarjmldnout\\\"]\",\"newTask_update\":\"2025-07-02\"}],\"218\":[],\"219\":[],\"220\":[{\"id\":120,\"title\":\"adsadad\",\"description\":\"adasdsada\",\"order\":1,\"status_id\":\"220\",\"userstory_id\":46,\"sprint_id\":43,\"proj_id\":45,\"start_date\":null,\"end_date\":null,\"completion_date\":\"2025-07-02\",\"created_at\":\"2025-07-01T16:49:07.000000Z\",\"updated_at\":\"2025-07-01T16:49:30.000000Z\",\"user_names\":\"[\\\"UserAcceptance\\\"]\",\"newTask_update\":\"2025-07-02\"}]}', '{\"idealData\":[2,1.8666666666666667,1.7333333333333334,1.6,1.4666666666666668,1.3333333333333335,1.2,1.0666666666666667,0.9333333333333333,0.8,0.6666666666666667,0.5333333333333334,0.3999999999999999,0.2666666666666666,0.1333333333333333,0],\"actualData\":[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-07-01 16:49:37', '2025-07-01 16:49:37'),
	(13, 34, '{\"213\":[],\"214\":[],\"215\":[{\"id\":103,\"title\":\"Task1 - Edit SystemRequirement\",\"description\":\"Edit requirements in the SRS Document\",\"order\":1,\"status_id\":\"215\",\"userstory_id\":45,\"sprint_id\":34,\"proj_id\":44,\"start_date\":\"2025-07-02\",\"end_date\":\"2025-07-07\",\"completion_date\":null,\"created_at\":\"2025-06-26T10:26:47.000000Z\",\"updated_at\":\"2025-07-02T06:26:07.000000Z\",\"user_names\":\"[\\\"ammar-\\\"]\",\"newTask_update\":\"2025-07-02\"}],\"216\":[]}', '{\"idealData\":[1,0.9333333333333333,0.8666666666666667,0.8,0.7333333333333334,0.6666666666666667,0.6,0.5333333333333333,0.4666666666666667,0.4,0.33333333333333337,0.2666666666666667,0.19999999999999996,0.1333333333333333,0.06666666666666665,0],\"actualData\":[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-07-03 03:35:15', '2025-07-03 03:35:15'),
	(14, 47, '{\"189\":[],\"190\":[],\"191\":[{\"id\":125,\"title\":\"adsa\",\"description\":\"dasdasdsa\",\"order\":1,\"status_id\":\"191\",\"userstory_id\":56,\"sprint_id\":47,\"proj_id\":38,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-03T22:51:26.000000Z\",\"updated_at\":\"2025-07-03T22:51:41.000000Z\",\"user_names\":\"[\\\"ammarjmldnout\\\"]\",\"newTask_update\":\"2025-07-04\"}],\"192\":[]}', '{\"idealData\":[1,0.9333333333333333,0.8666666666666667,0.8,0.7333333333333334,0.6666666666666667,0.6,0.5333333333333333,0.4666666666666667,0.4,0.33333333333333337,0.2666666666666667,0.19999999999999996,0.1333333333333333,0.06666666666666665,0],\"actualData\":[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]}', '2025-07-03 22:51:48', '2025-07-03 22:51:48'),
	(15, 48, '{\"263\":[],\"264\":[{\"id\":126,\"title\":\"Design and build the \\\"Create Project\\\" form\",\"description\":\"Create a UI form that allows input for project name, description, and start\\/end dates.\",\"order\":1,\"status_id\":\"264\",\"userstory_id\":57,\"sprint_id\":48,\"proj_id\":54,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-06T14:34:53.000000Z\",\"updated_at\":\"2025-07-06T14:59:29.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-06\"}],\"265\":[{\"id\":133,\"title\":\"Track changes in task history log\",\"description\":\"Maintain a changelog or activity feed for each task showing status updates.\",\"order\":1,\"status_id\":\"265\",\"userstory_id\":59,\"sprint_id\":48,\"proj_id\":54,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-06T14:36:36.000000Z\",\"updated_at\":\"2025-07-06T14:59:28.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-06\"}],\"266\":[{\"id\":127,\"title\":\"Save project details to the database\",\"description\":\"Connect form inputs to backend logic that stores new project records in the database.\",\"order\":2,\"status_id\":\"266\",\"userstory_id\":57,\"sprint_id\":48,\"proj_id\":54,\"start_date\":null,\"end_date\":null,\"completion_date\":\"2025-07-06\",\"created_at\":\"2025-07-06T14:35:06.000000Z\",\"updated_at\":\"2025-07-06T14:59:24.000000Z\",\"user_names\":\"[\\\"ammarjmldnout\\\"]\",\"newTask_update\":\"2025-07-06\"},{\"id\":129,\"title\":\"Build UI for task assignment within a project\",\"description\":\"Create a task creation interface with fields for developer assignment and task metadata.\",\"order\":1,\"status_id\":\"266\",\"userstory_id\":58,\"sprint_id\":48,\"proj_id\":54,\"start_date\":null,\"end_date\":null,\"completion_date\":\"2025-07-04\",\"created_at\":\"2025-07-06T14:35:40.000000Z\",\"updated_at\":\"2025-07-06T14:58:12.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-06\"},{\"id\":132,\"title\":\"Create dropdown or button to change task status\",\"description\":\"Add controls to allow developers to update the status of their tasks.\",\"order\":3,\"status_id\":\"266\",\"userstory_id\":59,\"sprint_id\":48,\"proj_id\":54,\"start_date\":null,\"end_date\":null,\"completion_date\":\"2025-07-06\",\"created_at\":\"2025-07-06T14:36:24.000000Z\",\"updated_at\":\"2025-07-06T14:59:24.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-06\"}]}', '{\"idealData\":[5,4.666666666666667,4.333333333333333,4,3.666666666666667,3.3333333333333335,3,2.666666666666667,2.3333333333333335,2,1.666666666666667,1.3333333333333335,1,0.666666666666667,0.3333333333333339,0],\"actualData\":[5,5,5,5,5,4,4,2,2,2,2,2,2,2,2]}', '2025-07-06 14:59:43', '2025-07-06 14:59:43'),
	(16, 49, '{\"263\":[{\"id\":131,\"title\":\"Prevent task duplication under same sprint\",\"description\":\"Validate against existing task titles or references to avoid accidental duplication within the sprint.\",\"order\":5,\"status_id\":\"263\",\"userstory_id\":58,\"sprint_id\":49,\"proj_id\":54,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-06T14:36:02.000000Z\",\"updated_at\":\"2025-07-07T01:28:04.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-06\"},{\"id\":134,\"title\":\"Prevent status changes on closed sprints only\",\"description\":\"Disable editing if the associated sprint has already been closed to maintain data integrity.\",\"order\":1,\"status_id\":\"263\",\"userstory_id\":59,\"sprint_id\":49,\"proj_id\":54,\"start_date\":\"2025-07-07\",\"end_date\":\"2025-07-16\",\"completion_date\":null,\"created_at\":\"2025-07-06T14:36:47.000000Z\",\"updated_at\":\"2025-07-07T01:27:43.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-07\"}],\"264\":[],\"265\":[{\"id\":128,\"title\":\"Auto-assign creator as project owner\",\"description\":\"Automatically link the currently logged-in user as the owner or manager of the newly created project.\",\"order\":2,\"status_id\":\"265\",\"userstory_id\":57,\"sprint_id\":49,\"proj_id\":54,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-07-06T14:35:20.000000Z\",\"updated_at\":\"2025-07-07T01:27:43.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-06\"},{\"id\":130,\"title\":\"Enable reassignment and updates to task metadata\",\"description\":\"Allow project managers to edit existing tasks and reassign them when necessary.\",\"order\":1,\"status_id\":\"265\",\"userstory_id\":58,\"sprint_id\":49,\"proj_id\":54,\"start_date\":\"2025-07-06\",\"end_date\":\"2025-07-14\",\"completion_date\":null,\"created_at\":\"2025-07-06T14:35:51.000000Z\",\"updated_at\":\"2025-07-07T01:26:50.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-07\"}],\"266\":[{\"id\":126,\"title\":\"Design and build the \\\"Create Project\\\" form\",\"description\":\"Create a UI form that allows input for project name, description, and start\\/end dates.\",\"order\":1,\"status_id\":\"266\",\"userstory_id\":57,\"sprint_id\":49,\"proj_id\":54,\"start_date\":null,\"end_date\":null,\"completion_date\":\"2025-07-07\",\"created_at\":\"2025-07-06T14:34:53.000000Z\",\"updated_at\":\"2025-07-07T01:27:43.000000Z\",\"user_names\":\"null\",\"newTask_update\":\"2025-07-06\"}]}', '{\"idealData\":[5,4.666666666666667,4.333333333333333,4,3.666666666666667,3.3333333333333335,3,2.666666666666667,2.3333333333333335,2,1.666666666666667,1.3333333333333335,1,0.666666666666667,0.3333333333333339,0],\"actualData\":[5,4,4,4,4,4,4,4,4,4,4,4,4,4,4]}', '2025-07-07 01:29:12', '2025-07-07 01:29:12'),
	(17, 52, '{\"280\":[{\"id\":138,\"title\":\"Task 2 - View food menu\",\"description\":\"this is task 2\",\"order\":1,\"status_id\":\"280\",\"userstory_id\":64,\"sprint_id\":52,\"proj_id\":58,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-08-18T08:39:24.000000Z\",\"updated_at\":\"2025-08-18T08:55:34.000000Z\",\"user_names\":\"[\\\"UAT_User1\\\"]\",\"newTask_update\":\"2025-08-18\"}],\"281\":[{\"id\":137,\"title\":\"Task 1 - Create food menu\",\"description\":\"Create food menu for customer to browse\",\"order\":1,\"status_id\":\"281\",\"userstory_id\":64,\"sprint_id\":52,\"proj_id\":58,\"start_date\":null,\"end_date\":null,\"completion_date\":null,\"created_at\":\"2025-08-18T08:35:11.000000Z\",\"updated_at\":\"2025-08-18T08:55:34.000000Z\",\"user_names\":\"[\\\"UAT_User1\\\"]\",\"newTask_update\":\"2025-08-18\"}],\"282\":[],\"283\":[]}', '{\"idealData\":[2,1.8666666666666667,1.7333333333333334,1.6,1.4666666666666668,1.3333333333333335,1.2,1.0666666666666667,0.9333333333333333,0.8,0.6666666666666667,0.5333333333333334,0.3999999999999999,0.2666666666666666,0.1333333333333333,0],\"actualData\":[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2]}', '2025-08-18 08:56:48', '2025-08-18 08:56:48');

/*!40000 ALTER TABLE `sprint_archives` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table statuses
# ------------------------------------------------------------

DROP TABLE IF EXISTS `statuses`;

CREATE TABLE `statuses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `order` smallint NOT NULL DEFAULT '0',
  `project_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=284 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `statuses` WRITE;
/*!40000 ALTER TABLE `statuses` DISABLE KEYS */;

INSERT INTO `statuses` (`id`, `title`, `slug`, `order`, `project_id`, `created_at`, `updated_at`) VALUES
	(1, 'To-do', 'to-do', 1, 1, NULL, NULL),
	(2, 'backlog', 'backlog', 1, 2, NULL, NULL),
	(4, 'doing', 'doing', 3, 2, NULL, NULL),
	(9, 'test code', 'test code', 4, 2, NULL, NULL),
	(11, 'done', 'done', 5, 2, NULL, NULL),
	(14, 'Backlog', 'backlog', 1, 3, NULL, NULL),
	(15, 'Up Next', 'up-next', 2, 3, NULL, NULL),
	(16, 'In Progress', 'in-progress', 3, 3, NULL, NULL),
	(17, 'Done', 'done', 4, 3, NULL, NULL),
	(18, 'Backlog', 'backlog', 1, 4, NULL, NULL),
	(19, 'Up Next', 'up-next', 2, 4, NULL, NULL),
	(20, 'In Progress', 'in-progress', 3, 4, NULL, NULL),
	(21, 'Done', 'done', 4, 4, NULL, NULL),
	(22, 'Backlog', 'backlog', 1, 5, NULL, NULL),
	(23, 'Up Next', 'up-next', 2, 5, NULL, NULL),
	(24, 'In Progress', 'in-progress', 3, 5, NULL, NULL),
	(25, 'Done', 'done', 4, 5, NULL, NULL),
	(40, 'Backlog', 'backlog', 1, 6, NULL, NULL),
	(41, 'Up Next', 'up-next', 2, 6, NULL, NULL),
	(42, 'In Progress', 'in-progress', 3, 6, NULL, NULL),
	(43, 'Done', 'done', 4, 6, NULL, NULL),
	(45, 'test', 'test', 6, 6, NULL, NULL),
	(46, 'Backlog', 'backlog', 1, 7, NULL, NULL),
	(47, 'Up Next', 'up-next', 2, 7, NULL, NULL),
	(48, 'In Progress', 'in-progress', 3, 7, NULL, NULL),
	(49, 'Done', 'done', 4, 7, NULL, NULL),
	(53, 'Backlog', 'backlog', 1, 8, NULL, NULL),
	(54, 'Up Next', 'up-next', 2, 8, NULL, NULL),
	(55, 'In Progress', 'in-progress', 3, 8, NULL, NULL),
	(56, 'Done', 'done', 4, 8, NULL, NULL),
	(57, 'Backlog', 'backlog', 1, 9, NULL, NULL),
	(58, 'Up Next', 'up-next', 2, 9, NULL, NULL),
	(59, 'In Progress', 'in-progress', 3, 9, NULL, NULL),
	(60, 'Done', 'done', 4, 9, NULL, NULL),
	(61, 'Backlog', 'backlog', 1, 10, NULL, NULL),
	(62, 'Up Next', 'up-next', 2, 10, NULL, NULL),
	(63, 'In Progress', 'in-progress', 3, 10, NULL, NULL),
	(64, 'Done', 'done', 4, 10, NULL, NULL),
	(65, 'Backlog', 'backlog', 1, 11, NULL, NULL),
	(66, 'Up Next', 'up-next', 2, 11, NULL, NULL),
	(67, 'In Progress', 'in-progress', 3, 11, NULL, NULL),
	(68, 'Done', 'done', 4, 11, NULL, NULL),
	(69, 'Backlog', 'backlog', 1, 12, NULL, NULL),
	(70, 'Up Next', 'up-next', 2, 12, NULL, NULL),
	(71, 'In Progress', 'in-progress', 3, 12, NULL, NULL),
	(72, 'Done', 'done', 4, 12, NULL, NULL),
	(82, 'Backlog', 'backlog', 1, 13, NULL, NULL),
	(83, 'Up Next', 'up-next', 2, 13, NULL, NULL),
	(84, 'In Progress', 'in-progress', 3, 13, NULL, NULL),
	(85, 'Done', 'done', 4, 13, NULL, NULL),
	(86, 'Backlog', 'backlog', 1, 14, NULL, NULL),
	(87, 'Up Next', 'up-next', 2, 14, NULL, NULL),
	(88, 'In Progress', 'in-progress', 3, 14, NULL, NULL),
	(89, 'Done', 'done', 4, 14, NULL, NULL),
	(90, 'Backlog', 'backlog', 1, 15, NULL, NULL),
	(91, 'Up Next', 'up-next', 2, 15, NULL, NULL),
	(92, 'In Progress', 'in-progress', 3, 15, NULL, NULL),
	(93, 'Done', 'done', 4, 15, NULL, NULL),
	(94, 'Backlog', 'backlog', 1, 16, NULL, NULL),
	(95, 'Up Next', 'up-next', 2, 16, NULL, NULL),
	(96, 'In Progress', 'in-progress', 3, 16, NULL, NULL),
	(97, 'Done', 'done', 4, 16, NULL, NULL),
	(98, 'Backlog', 'backlog', 1, 17, NULL, NULL),
	(99, 'Up Next', 'up-next', 2, 17, NULL, NULL),
	(100, 'In Progress', 'in-progress', 3, 17, NULL, NULL),
	(101, 'Done', 'done', 4, 17, NULL, NULL),
	(102, 'Backlog', 'backlog', 1, 18, NULL, NULL),
	(103, 'Up Next', 'up-next', 2, 18, NULL, NULL),
	(104, 'In Progress', 'in-progress', 3, 18, NULL, NULL),
	(105, 'Done', 'done', 4, 18, NULL, NULL),
	(106, 'Backlog', 'backlog', 1, 19, NULL, NULL),
	(107, 'Up Next', 'up-next', 2, 19, NULL, NULL),
	(108, 'In Progress', 'in-progress', 3, 19, NULL, NULL),
	(109, 'Done', 'done', 4, 19, NULL, NULL),
	(110, 'Backlog', 'backlog', 1, 20, NULL, NULL),
	(111, 'Up Next', 'up-next', 2, 20, NULL, NULL),
	(112, 'In Progress', 'in-progress', 3, 20, NULL, NULL),
	(113, 'Done', 'done', 4, 20, NULL, NULL),
	(114, 'Backlog', 'backlog', 1, 21, NULL, NULL),
	(115, 'Up Next', 'up-next', 2, 21, NULL, NULL),
	(116, 'In Progress', 'in-progress', 3, 21, NULL, NULL),
	(117, 'Done', 'done', 4, 21, NULL, NULL),
	(118, 'Backlog', 'backlog', 1, 22, NULL, NULL),
	(119, 'Up Next', 'up-next', 2, 22, NULL, NULL),
	(120, 'In Progress', 'in-progress', 3, 22, NULL, NULL),
	(121, 'Done', 'done', 4, 22, NULL, NULL),
	(122, 'Backlog', 'backlog', 1, 23, NULL, NULL),
	(123, 'Up Next', 'up-next', 2, 23, NULL, NULL),
	(124, 'In Progress', 'in-progress', 3, 23, NULL, NULL),
	(125, 'Done', 'done', 4, 23, NULL, NULL),
	(126, 'Backlog', 'backlog', 1, 24, NULL, NULL),
	(127, 'Up Next', 'up-next', 2, 24, NULL, NULL),
	(128, 'In Progress', 'in-progress', 3, 24, NULL, NULL),
	(129, 'Done', 'done', 4, 24, NULL, NULL),
	(130, 'Backlog', 'backlog', 1, 25, NULL, NULL),
	(131, 'Up Next', 'up-next', 2, 25, NULL, NULL),
	(132, 'In Progress', 'in-progress', 3, 25, NULL, NULL),
	(133, 'Done', 'done', 4, 25, NULL, NULL),
	(134, 'Backlog', 'backlog', 1, 26, NULL, NULL),
	(135, 'Up Next', 'up-next', 2, 26, NULL, NULL),
	(136, 'In Progress', 'in-progress', 3, 26, NULL, NULL),
	(137, 'Done', 'done', 4, 26, NULL, NULL),
	(138, 'Backlog', 'backlog', 1, 27, NULL, NULL),
	(139, 'Up Next', 'up-next', 3, 27, NULL, NULL),
	(140, 'In Progress', 'in-progress', 2, 27, NULL, NULL),
	(141, 'Done', 'done', 4, 27, NULL, NULL),
	(142, 'Backlog', 'backlog', 1, 28, NULL, NULL),
	(143, 'Up Next', 'up-next', 2, 28, NULL, NULL),
	(144, 'In Progress', 'in-progress', 3, 28, NULL, NULL),
	(145, 'Done', 'done', 4, 28, NULL, NULL),
	(146, 'Backlog', 'backlog', 1, 29, NULL, NULL),
	(147, 'Up Next', 'up-next', 2, 29, NULL, NULL),
	(148, 'In Progress', 'in-progress', 3, 29, NULL, NULL),
	(149, 'Done', 'done', 4, 29, NULL, NULL),
	(150, 'Backlog', 'backlog', 1, 30, NULL, NULL),
	(151, 'Up Next', 'up-next', 2, 30, NULL, NULL),
	(152, 'In Progress', 'in-progress', 3, 30, NULL, NULL),
	(153, 'Done', 'done', 4, 30, NULL, NULL),
	(154, 'Backlog', 'backlog', 1, 31, NULL, NULL),
	(155, 'Up Next', 'up-next', 2, 31, NULL, NULL),
	(156, 'In Progress', 'in-progress', 3, 31, NULL, NULL),
	(157, 'Done', 'done', 4, 31, NULL, NULL),
	(158, 'Backlog', 'backlog', 1, 32, NULL, NULL),
	(159, 'Up Next', 'up-next', 2, 32, NULL, NULL),
	(160, 'In Progress', 'in-progress', 3, 32, NULL, NULL),
	(161, 'Done', 'done', 4, 32, NULL, NULL),
	(162, 'Backlog', 'backlog', 1, 33, NULL, NULL),
	(163, 'Up Next', 'up-next', 2, 33, NULL, NULL),
	(164, 'In Progress', 'in-progress', 3, 33, NULL, NULL),
	(165, 'Done', 'done', 4, 33, NULL, NULL),
	(170, 'Backlog', 'backlog', 1, 34, NULL, NULL),
	(171, 'Up Next', 'up-next', 2, 34, NULL, NULL),
	(172, 'In Progress', 'in-progress', 3, 34, NULL, NULL),
	(173, 'Done', 'done', 4, 34, NULL, NULL),
	(174, 'Backlog', 'backlog', 1, 35, NULL, NULL),
	(175, 'Up Next', 'up-next', 2, 35, NULL, NULL),
	(176, 'In Progress', 'in-progress', 3, 35, NULL, NULL),
	(177, 'Done', 'done', 4, 35, NULL, NULL),
	(178, 'Backlog', 'backlog', 1, 36, NULL, NULL),
	(179, 'Up Next', 'up-next', 2, 36, NULL, NULL),
	(180, 'In Progress', 'in-progress', 3, 36, NULL, NULL),
	(181, 'Done', 'done', 4, 36, NULL, NULL),
	(185, 'Backlog', 'backlog', 1, 37, NULL, NULL),
	(186, 'Up Next', 'up-next', 2, 37, NULL, NULL),
	(187, 'In Progress', 'in-progress', 3, 37, NULL, NULL),
	(188, 'Done', 'done', 4, 37, NULL, NULL),
	(189, 'Backlog', 'backlog', 1, 38, NULL, NULL),
	(190, 'Up Next', 'up-next', 2, 38, NULL, NULL),
	(191, 'In Progress', 'in-progress', 3, 38, NULL, NULL),
	(192, 'Done', 'done', 4, 38, NULL, NULL),
	(193, 'Backlog', 'backlog', 1, 39, NULL, NULL),
	(194, 'Up Next', 'up-next', 2, 39, NULL, NULL),
	(195, 'In Progress', 'in-progress', 3, 39, NULL, NULL),
	(196, 'Done', 'done', 4, 39, NULL, NULL),
	(197, 'Backlog', 'backlog', 2, 40, NULL, NULL),
	(198, 'Up Next', 'up-next', 1, 40, NULL, NULL),
	(199, 'In Progress', 'in-progress', 3, 40, NULL, NULL),
	(200, 'Done', 'done', 4, 40, NULL, NULL),
	(201, 'Backlog', 'backlog', 1, 41, NULL, NULL),
	(202, 'Up Next', 'up-next', 2, 41, NULL, NULL),
	(203, 'In Progress', 'in-progress', 3, 41, NULL, NULL),
	(204, 'Done', 'done', 4, 41, NULL, NULL),
	(209, 'Backlog', 'backlog', 1, 43, NULL, NULL),
	(210, 'Up Next', 'up-next', 2, 43, NULL, NULL),
	(211, 'In Progress', 'in-progress', 3, 43, NULL, NULL),
	(212, 'Done', 'done', 4, 43, NULL, NULL),
	(213, 'Backlog', 'backlog', 1, 44, NULL, NULL),
	(214, 'Up Next', 'up-next', 2, 44, NULL, NULL),
	(215, 'In Progress', 'in-progress', 3, 44, NULL, NULL),
	(216, 'Done', 'done', 4, 44, NULL, NULL),
	(217, 'Backlog', 'backlog', 1, 45, NULL, NULL),
	(218, 'Up Next', 'up-next', 2, 45, NULL, NULL),
	(219, 'In Progress', 'in-progress', 3, 45, NULL, NULL),
	(220, 'Done', 'done', 4, 45, NULL, NULL),
	(222, 'Backlog', 'backlog', 1, 46, NULL, NULL),
	(223, 'Up Next', 'up-next', 2, 46, NULL, NULL),
	(224, 'In Progress', 'in-progress', 3, 46, NULL, NULL),
	(225, 'Done', 'done', 4, 46, NULL, NULL),
	(226, 'Backlog', 'backlog', 1, 47, NULL, NULL),
	(227, 'Up Next', 'up-next', 2, 47, NULL, NULL),
	(228, 'In Progress', 'in-progress', 3, 47, NULL, NULL),
	(229, 'Done', 'done', 4, 47, NULL, NULL),
	(230, 'Backlog', 'backlog', 1, 48, NULL, NULL),
	(231, 'Up Next', 'up-next', 2, 48, NULL, NULL),
	(232, 'In Progress', 'in-progress', 3, 48, NULL, NULL),
	(233, 'Done', 'done', 4, 48, NULL, NULL),
	(234, 'Backlog', 'backlog', 1, 49, NULL, NULL),
	(235, 'Up Next', 'up-next', 2, 49, NULL, NULL),
	(236, 'In Progress', 'in-progress', 3, 49, NULL, NULL),
	(237, 'Done', 'done', 5, 49, NULL, NULL),
	(238, 'Backlog', 'backlog', 1, 50, NULL, NULL),
	(239, 'Up Next', 'up-next', 2, 50, NULL, NULL),
	(240, 'In Progress', 'in-progress', 3, 50, NULL, NULL),
	(241, 'Done', 'done', 5, 50, NULL, NULL),
	(242, 'Backlog', 'backlog', 1, 51, NULL, NULL),
	(243, 'Up Next', 'up-next', 3, 51, NULL, NULL),
	(244, 'In Progress', 'in-progress', 2, 51, NULL, NULL),
	(245, 'Done', 'done', 4, 51, NULL, NULL),
	(246, 'Backlog', 'backlog', 1, 52, NULL, NULL),
	(247, 'Up Next', 'up-next', 2, 52, NULL, NULL),
	(248, 'In Progress', 'in-progress', 3, 52, NULL, NULL),
	(249, 'Done', 'done', 4, 52, NULL, NULL),
	(250, 'Backlog', 'backlog', 1, 53, NULL, NULL),
	(251, 'Up Next', 'up-next', 2, 53, NULL, NULL),
	(252, 'In Progress', 'in-progress', 3, 53, NULL, NULL),
	(253, 'Done', 'done', 5, 53, NULL, NULL),
	(263, 'Backlog', 'backlog', 1, 54, NULL, NULL),
	(264, 'Up Next', 'up-next', 3, 54, NULL, NULL),
	(265, 'In Progress', 'in-progress', 2, 54, NULL, NULL),
	(266, 'Done', 'done', 4, 54, NULL, NULL),
	(267, 'Backlog', 'backlog', 1, 55, NULL, NULL),
	(268, 'Up Next', 'up-next', 2, 55, NULL, NULL),
	(269, 'In Progress', 'in-progress', 3, 55, NULL, NULL),
	(270, 'Done', 'done', 4, 55, NULL, NULL),
	(271, 'Backlog', 'backlog', 1, 56, NULL, NULL),
	(272, 'Up Next', 'up-next', 2, 56, NULL, NULL),
	(273, 'In Progress', 'in-progress', 3, 56, NULL, NULL),
	(274, 'Done', 'done', 4, 56, NULL, NULL),
	(275, 'Backlog', 'backlog', 1, 57, NULL, NULL),
	(276, 'Up Next', 'up-next', 2, 57, NULL, NULL),
	(277, 'In Progress', 'in-progress', 3, 57, NULL, NULL),
	(278, 'Done', 'done', 4, 57, NULL, NULL),
	(279, 'Testing', 'Testing', 5, 54, NULL, NULL),
	(280, 'Backlog', 'backlog', 1, 58, NULL, NULL),
	(281, 'Up Next', 'up-next', 2, 58, NULL, NULL),
	(282, 'In Progress', 'in-progress', 3, 58, NULL, NULL),
	(283, 'Done', 'done', 4, 58, NULL, NULL);

/*!40000 ALTER TABLE `statuses` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table taskcomment
# ------------------------------------------------------------

DROP TABLE IF EXISTS `taskcomment`;

CREATE TABLE `taskcomment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` bigint unsigned NOT NULL,
  `comment` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `assigned_to` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `taskcomment` WRITE;
/*!40000 ALTER TABLE `taskcomment` DISABLE KEYS */;

INSERT INTO `taskcomment` (`id`, `task_id`, `comment`, `created_by`, `assigned_to`, `updated_at`, `created_at`) VALUES
	(3, 77, 'test comment filter', 'passUser', '[\"ammar-\"]', '2025-05-22 16:20:01', '2025-05-22 16:20:01'),
	(4, 77, 'newComment', 'ammar-', '[\"ammar-\"]', '2025-05-22 16:20:25', '2025-05-22 16:20:25'),
	(6, 137, 'My first comment', 'UAT_User1', '[\"UAT_User1\"]', '2025-08-18 16:42:49', '2025-08-18 16:42:49');

/*!40000 ALTER TABLE `taskcomment` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table tasks
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tasks`;

CREATE TABLE `tasks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `description` text,
  `order` smallint NOT NULL DEFAULT '0',
  `status_id` varchar(191) NOT NULL,
  `userstory_id` int unsigned NOT NULL,
  `sprint_id` int unsigned NOT NULL,
  `proj_id` int unsigned NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `completion_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_names` longtext,
  `newTask_update` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=140 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `tasks` WRITE;
/*!40000 ALTER TABLE `tasks` DISABLE KEYS */;

INSERT INTO `tasks` (`id`, `title`, `description`, `order`, `status_id`, `userstory_id`, `sprint_id`, `proj_id`, `start_date`, `end_date`, `completion_date`, `created_at`, `updated_at`, `user_names`, `newTask_update`) VALUES
	(44, 'adada', 'addsad', 1, '9', 5, 1, 2, '2023-12-04', '2023-12-07', NULL, '2024-01-14 08:34:28', '2025-03-26 23:25:38', '[\"ammar-\"]', '2024-01-30'),
	(46, 'adadal', 'adasd', 2, '9', 5, 1, 2, '2023-12-05', '2023-12-08', NULL, '2024-01-14 08:37:59', '2025-03-26 23:25:38', 'null', NULL),
	(48, 'ccassa', 'dasdas', 1, '42', 10, 0, 6, NULL, NULL, NULL, '2024-01-16 11:55:10', '2025-05-27 23:06:55', '[\"ammar-\"]', NULL),
	(51, 'adadas', 'adasdasdas', 2, '4', 5, 1, 2, '2023-12-03', '2023-12-07', NULL, '2024-01-16 15:45:04', '2025-03-26 23:25:33', '[\"ammar-\"]', NULL),
	(53, 'task 1', '1', 1, '11', 12, 16, 2, '2024-01-15', '2024-01-16', '2025-04-23', '2024-01-17 00:44:41', '2025-04-23 23:53:59', 'null', '2024-01-30'),
	(54, 'task 2', '2', 2, '11', 12, 16, 2, '2024-01-17', '2024-01-18', '2025-04-27', '2024-01-17 00:45:06', '2025-04-27 22:12:18', 'null', '2024-01-30'),
	(55, 'task 3', '3', 3, '11', 12, 16, 2, '2024-01-18', '2024-01-19', '2025-04-23', '2024-01-17 00:45:31', '2025-04-27 22:12:18', 'null', '2024-01-30'),
	(58, 'TaskTest3', 'asdsad', 1, '9', 13, 14, 2, '2024-01-28', '2024-02-02', NULL, '2024-01-30 06:53:59', '2025-03-20 12:27:52', 'null', '2024-02-02'),
	(59, 'TaskTest10', 'eqeeqw', 1, '11', 13, 14, 2, '2024-01-28', '2024-02-02', NULL, '2024-01-30 06:54:13', '2025-03-25 18:10:28', '[\"ammar-\"]', '2024-02-02'),
	(61, 'Task1ForTest', 'Test Task', 4, '11', 17, 16, 2, '2025-03-24', '2025-04-05', '2025-04-23', '2025-03-26 23:57:13', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-03-26'),
	(62, 'TestTask2', 'dasd', 5, '11', 17, 16, 2, '2025-03-24', '2025-03-31', '2025-04-23', '2025-03-26 23:57:37', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-03-26'),
	(63, 'task3', 'aasdasd', 6, '11', 17, 16, 2, '2025-03-26', '2025-03-29', '2025-03-27', '2025-03-26 23:58:25', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-03-26'),
	(64, 'task4', 'weqe', 7, '11', 17, 16, 2, '2025-03-24', '2025-03-29', '2025-03-29', '2025-03-26 23:58:33', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-03-26'),
	(65, 'task5', 'daddd', 8, '11', 17, 16, 2, '2025-03-24', '2025-03-31', '2025-03-27', '2025-03-26 23:58:44', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-03-26'),
	(66, 'newTaskPS', 'This is a description', 9, '11', 17, 16, 2, '2025-03-24', '2025-04-06', '2025-04-17', '2025-04-09 16:26:30', '2025-04-27 22:12:18', '[\"ammar-\"]', '2025-04-09'),
	(75, 'newTask creation for Task page', 'task', 1, '4', 2, 0, 2, NULL, NULL, NULL, '2025-04-24 14:32:07', '2025-05-27 23:26:55', '[\"ammar-\"]', '2025-04-24'),
	(76, 'NewTaskForDB', 'new task creation with remade DB', 1, '85', 24, 0, 13, NULL, NULL, '2025-05-27', '2025-05-22 15:31:01', '2025-05-27 23:17:14', '[\"ammar-\"]', '2025-05-22'),
	(77, 'NewTaskForDB2', 'test task', 1, '85', 24, 32, 13, NULL, NULL, '2025-06-19', '2025-05-22 15:31:18', '2025-06-19 03:06:05', '[\"ammar-\"]', '2025-05-22'),
	(78, 'newTaskBacklog', 'description', 0, '82', 25, 32, 13, NULL, NULL, NULL, '2025-05-22 16:24:18', '2025-06-19 03:32:24', '[\"ammar-\"]', '2025-05-22'),
	(79, 'TaskTesting123-1', 'test task', 1, '53', 26, 28, 8, NULL, NULL, NULL, '2025-05-27 22:40:01', '2025-06-11 06:28:26', '[\"ammar-\"]', '2025-05-27'),
	(80, 'TaskTesting123-2', 'TaskTesting123-2', 0, '53', 26, 0, 8, NULL, NULL, NULL, '2025-05-27 22:40:13', '2025-05-30 04:07:51', '[\"ammar-\"]', '2025-05-27'),
	(81, 'TestTASK123123', 'asdsqd', 0, '40', 27, 0, 6, NULL, NULL, NULL, '2025-05-27 22:54:18', '2025-05-27 23:04:18', '[\"ammar-\"]', '2025-05-27'),
	(89, 'Design the task creation form', 'Create a user-friendly form with fields for task title, detailed description, assignee, priority, and deadline to capture all essential task details.', 1, '173', 32, 31, 34, NULL, NULL, '2025-06-16', '2025-06-16 04:28:47', '2025-06-16 04:33:00', '[\"testUser2\"]', '2025-06-16'),
	(90, 'Implement task assignment logic', 'Develop backend logic that links each task to a selected user account, ensuring tasks are correctly assigned and appear in the user’s task list.', 2, '170', 32, 31, 34, NULL, NULL, NULL, '2025-06-16 04:28:58', '2025-06-19 03:35:17', 'null', '2025-06-16'),
	(91, 'Develop a task list view', 'Build an interface that displays all tasks in a list or table format, with filtering and sorting options by project and assignee.', 0, '170', 32, 0, 34, NULL, NULL, NULL, '2025-06-16 04:29:09', '2025-06-16 04:29:09', 'null', '2025-06-16'),
	(92, 'Test task creation and assignment workflows', 'Perform end-to-end testing to ensure tasks can be created, assigned, edited, and removed without errors.', 0, '170', 32, 0, 34, NULL, NULL, NULL, '2025-06-16 04:29:23', '2025-06-16 04:29:23', 'null', '2025-06-16'),
	(93, 'Create a task status field', 'Add a status field to tasks with predefined values like “To Do”, “In Progress”, and “Done” to reflect the task’s current state.', 2, '173', 33, 31, 34, NULL, NULL, '2025-06-14', '2025-06-16 04:29:51', '2025-06-16 04:33:46', '[\"passUser\",\"TestRandomUser\"]', '2025-06-16'),
	(94, 'Display real-time status updates', 'Make sure that any status change is instantly reflected on the project’s dashboard and task list for accurate progress tracking.', 0, '170', 33, 0, 34, NULL, NULL, NULL, '2025-06-16 04:30:05', '2025-06-16 04:30:05', '[\"passUser\"]', '2025-06-16'),
	(95, 'Develop metrics and data fetching', 'Implement backend queries to calculate total tasks, completed tasks, pending tasks, and upcoming deadlines for each project.', 1, '170', 34, 31, 34, NULL, NULL, NULL, '2025-06-16 04:30:20', '2025-06-19 03:34:36', 'null', '2025-06-16'),
	(96, 'Add filtering and sorting options', 'Provide filtering by sprint, assignee, or time period so managers can focus on specific project segments.', 0, '170', 34, 0, 34, NULL, NULL, NULL, '2025-06-16 04:30:30', '2025-06-16 04:30:30', '[\"testUser2\"]', '2025-06-16'),
	(110, 'Task_UAT1', 'This is a task to test the UAT for SAgile\'s task update', 1, '251', 53, 0, 53, NULL, NULL, NULL, '2025-07-01 13:01:51', '2025-07-01 13:29:30', '[\"UAT_User1\"]', '2025-07-01'),
	(114, 'Task_UAT2', 'This is the second task to test the UAT for SAgile', 1, '250', 53, 0, 53, NULL, NULL, NULL, '2025-07-01 13:06:55', '2025-07-01 13:29:30', '[\"UAT_User1\"]', '2025-07-01'),
	(125, 'adsa', 'dasdasdsa', 1, '191', 56, 0, 38, NULL, NULL, NULL, '2025-07-03 22:51:26', '2025-07-03 22:51:48', '[\"ammarjmldnout\"]', '2025-07-04'),
	(126, 'Design and build the \"Create Project\" form', 'Create a UI form that allows input for project name, description, and start/end dates.', 1, '266', 57, 0, 54, NULL, NULL, '2025-07-07', '2025-07-06 14:34:53', '2025-07-07 01:29:12', 'null', '2025-07-06'),
	(127, 'Save project details to the database', 'Connect form inputs to backend logic that stores new project records in the database.', 2, '266', 57, 0, 54, NULL, NULL, '2025-07-06', '2025-07-06 14:35:06', '2025-07-06 14:59:43', '[\"ammarjmldnout\"]', '2025-07-06'),
	(128, 'Auto-assign creator as project owner', 'Automatically link the currently logged-in user as the owner or manager of the newly created project.', 1, '266', 57, 50, 54, NULL, NULL, '2025-08-04', '2025-07-06 14:35:20', '2025-08-04 07:11:22', 'null', '2025-07-06'),
	(129, 'Build UI for task assignment within a project', 'Create a task creation interface with fields for developer assignment and task metadata.', 1, '266', 58, 0, 54, NULL, NULL, '2025-07-04', '2025-07-06 14:35:40', '2025-07-06 14:59:43', 'null', '2025-07-06'),
	(130, 'Enable reassignment and updates to task metadata', 'Allow project managers to edit existing tasks and reassign them when necessary.', 1, '279', 58, 50, 54, NULL, NULL, NULL, '2025-07-06 14:35:51', '2025-08-04 07:11:22', 'null', '2025-07-07'),
	(131, 'Prevent task duplication under same sprint', 'Validate against existing task titles or references to avoid accidental duplication within the sprint.', 1, '263', 58, 50, 54, NULL, NULL, NULL, '2025-07-06 14:36:02', '2025-07-10 11:36:14', 'null', '2025-07-06'),
	(132, 'Create dropdown or button to change task status', 'Add controls to allow developers to update the status of their tasks.', 3, '266', 59, 0, 54, NULL, NULL, '2025-07-06', '2025-07-06 14:36:24', '2025-07-06 14:59:45', 'null', '2025-07-06'),
	(133, 'Track changes in task history log', 'Maintain a changelog or activity feed for each task showing status updates.', 1, '265', 59, 0, 54, NULL, NULL, NULL, '2025-07-06 14:36:36', '2025-07-06 14:59:45', 'null', '2025-07-06'),
	(134, 'Prevent status changes on closed sprints only', 'Disable editing if the associated sprint has already been closed to maintain data integrity.', 1, '263', 59, 49, 54, '2025-07-07', '2025-07-16', NULL, '2025-07-06 14:36:47', '2025-07-07 01:27:43', 'null', '2025-07-07'),
	(135, 'Update backend with new task state', 'Ensure that task status updates are saved to the database and reflected in the UI.', 9, '263', 59, 0, 54, NULL, NULL, NULL, '2025-07-06 14:37:34', '2025-07-06 14:37:34', 'null', '2025-07-06'),
	(136, 'NEW UAT1', 'This is a UAT', 1, '271', 63, 51, 56, NULL, NULL, NULL, '2025-08-04 07:12:29', '2025-08-04 07:12:51', '[\"ammar-\",\"ammarjmldnout\"]', '2025-08-04'),
	(137, 'Task 1 - Create food menu', 'Create food menu for customer to browse', 1, '281', 64, 0, 58, NULL, NULL, NULL, '2025-08-18 08:35:11', '2025-08-18 08:56:48', '[\"UAT_User1\"]', '2025-08-18'),
	(138, 'Task 2 - View food menu', 'this is task 2', 1, '280', 64, 0, 58, NULL, NULL, NULL, '2025-08-18 08:39:24', '2025-08-18 08:56:48', '[\"UAT_User1\"]', '2025-08-18'),
	(139, 'Task 3_Create view for history', 'Create view for history', 2, '281', 65, 0, 58, NULL, NULL, NULL, '2025-08-18 09:02:44', '2025-08-18 09:02:44', '[\"ivlyn\"]', '2025-08-18');

/*!40000 ALTER TABLE `tasks` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table teammappings
# ------------------------------------------------------------

DROP TABLE IF EXISTS `teammappings`;

CREATE TABLE `teammappings` (
  `teammapping_id` int unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(191) NOT NULL,
  `role_name` varchar(191) NOT NULL,
  `team_name` varchar(191) NOT NULL,
  `project_id` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `invitation_status` enum('pending','accepted','declined') NOT NULL DEFAULT 'pending',
  `invitation_token` varchar(191) DEFAULT NULL,
  PRIMARY KEY (`teammapping_id`)
) ENGINE=InnoDB AUTO_INCREMENT=188 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `teammappings` WRITE;
/*!40000 ALTER TABLE `teammappings` DISABLE KEYS */;

INSERT INTO `teammappings` (`teammapping_id`, `username`, `role_name`, `team_name`, `project_id`, `created_at`, `updated_at`, `invitation_status`, `invitation_token`) VALUES
	(7, 'ammar-', 'Project Manager', 'Testers', NULL, '2024-01-10 06:25:31', '2025-06-21 09:57:41', 'accepted', NULL),
	(9, 'testUser2', 'Project Manager', 'testTeamAdmin', NULL, NULL, '2025-06-19 03:08:43', 'accepted', NULL),
	(11, 'testuser', 'Student', 'A12345_Test User', NULL, '2025-05-26 19:29:53', '2025-05-26 19:29:53', 'pending', NULL),
	(29, 'testUser2', 'Team Member', 'Testers', NULL, '2025-05-30 03:52:27', '2025-07-04 07:55:39', 'accepted', NULL),
	(36, 'taufiq', 'Project Manager', 'new_team', NULL, '2025-06-12 10:00:19', '2025-06-22 08:15:01', 'accepted', NULL),
	(38, 'TestRandomUser', 'Project Manager', 'new taufiq team', NULL, '2025-06-12 10:06:38', '2025-06-12 10:06:38', 'pending', NULL),
	(123, 'UatDrive', 'Team Manager', 'UatTestTeam', NULL, '2025-07-01 09:25:00', '2025-07-01 09:25:00', 'accepted', NULL),
	(134, 'UAT_User3', 'Team Manager', 'uatTestTeam_3', NULL, '2025-07-01 12:28:44', '2025-07-01 12:28:44', 'accepted', NULL),
	(137, 'UAT_User3', 'Project Manager', 'uatTestTeam_3', 48, '2025-07-01 12:31:45', '2025-07-01 12:31:45', 'accepted', NULL),
	(142, 'UAT_User3', 'Project Manager', 'uatTestTeam_3', 50, '2025-07-01 12:36:37', '2025-07-01 13:05:35', 'accepted', NULL),
	(151, 'UAT_User1', 'Team Manager', 'uatTestTeam_1', NULL, '2025-07-01 12:42:44', '2025-07-01 12:42:44', 'accepted', NULL),
	(152, 'UAT_User1', 'Project Manager', 'uatTestTeam_1', 52, '2025-07-01 12:43:38', '2025-07-01 12:43:38', 'accepted', NULL),
	(153, 'UAT_User1', 'Project Manager', 'uatTestTeam_1', 53, '2025-07-01 12:50:56', '2025-07-01 12:50:56', 'accepted', NULL),
	(155, 'ivlyn', 'Team Member', 'uatTestTeam_1', NULL, '2025-07-01 12:54:33', '2025-07-01 12:55:01', 'accepted', 'yPfisCDEPg6l07eOZ65yrmnT4UQfJKU9'),
	(160, 'ammar-', 'Project Manager', 'Testers', 34, '2025-07-03 15:35:45', '2025-07-03 15:35:45', 'accepted', NULL),
	(167, 'ammarjmldnout', 'Team Manager', 'PSM2_DemoTeam', NULL, '2025-07-06 14:28:56', '2025-07-06 14:28:56', 'accepted', NULL),
	(171, 'ammarjmldnout', 'Project Manager', 'PSM2_DemoTeam', 54, '2025-07-06 16:16:25', '2025-08-04 07:10:49', 'accepted', NULL),
	(174, 'ammar-', 'Team Manager', 'PSM2 Invite Team', NULL, '2025-07-06 23:55:16', '2025-07-06 23:55:16', 'accepted', NULL),
	(175, 'ammarjmldnout', 'Team Member', 'PSM2 Invite Team', NULL, '2025-07-06 23:55:26', '2025-07-06 23:56:14', 'accepted', 'COaAdqVI9W2Tx59neOwDX5qydEY8rxfp'),
	(176, 'ammar-', 'Project Manager', 'PSM2 Invite Team', 56, '2025-07-07 00:37:07', '2025-07-07 00:37:07', 'accepted', NULL),
	(177, 'ammarjmldnout', 'Developer', 'PSM2 Invite Team', 56, '2025-07-07 00:37:07', '2025-07-07 00:37:07', 'accepted', NULL),
	(178, 'ammar-', 'Team Member', 'PSM2_DemoTeam', NULL, '2025-07-07 01:31:04', '2025-07-07 01:31:18', 'accepted', 'CpydAEiT8FXjdlb9KHXQ9B5ZgXLUjHFH'),
	(179, 'ammarjmldnout', 'Team Manager', 'PS2Demo', NULL, '2025-07-07 01:31:30', '2025-07-07 01:31:30', 'accepted', NULL),
	(180, 'ammar-', 'Team Member', 'PS2Demo', NULL, '2025-07-07 01:32:28', '2025-07-07 01:32:36', 'accepted', 'iQ9aDmosADvMPjpA59i4vrbTgF6SDUpn'),
	(181, 'ammarjmldnout', 'Project Manager', 'PS2Demo', 57, '2025-07-07 01:33:05', '2025-07-07 01:33:05', 'accepted', NULL),
	(182, 'ammar-', 'Developer', 'PS2Demo', 57, '2025-07-07 01:33:05', '2025-07-07 01:33:05', 'accepted', NULL),
	(183, 'ammar-', 'Project Manager', 'PSM2_DemoTeam', 54, '2025-07-07 01:42:05', '2025-07-07 01:42:05', 'accepted', NULL),
	(184, 'UAT_User1', 'Project Manager', 'uatTestTeam_1', 58, '2025-08-18 08:31:33', '2025-08-18 08:31:33', 'accepted', NULL),
	(187, 'ivlyn', 'Developer', 'uatTestTeam_1', 58, '2025-08-18 08:31:33', '2025-08-18 08:31:33', 'accepted', NULL);

/*!40000 ALTER TABLE `teammappings` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table teams
# ------------------------------------------------------------

DROP TABLE IF EXISTS `teams`;

CREATE TABLE `teams` (
  `team_id` int unsigned NOT NULL AUTO_INCREMENT,
  `team_name` varchar(191) NOT NULL,
  `proj_name` varchar(191) NOT NULL DEFAULT '',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `team_names` longtext,
  PRIMARY KEY (`team_id`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `teams` WRITE;
/*!40000 ALTER TABLE `teams` DISABLE KEYS */;

INSERT INTO `teams` (`team_id`, `team_name`, `proj_name`, `created_at`, `updated_at`, `team_names`) VALUES
	(3, 'Testers', '', '2024-01-10 06:25:31', '2024-01-10 06:25:31', NULL),
	(5, 'TestTeam', '', '2024-10-10 16:40:05', '2024-10-10 16:40:05', NULL),
	(6, 'newTeam', '', '2024-10-10 16:45:42', '2024-10-10 16:45:42', NULL),
	(7, 'testTeamAdmin', '', '2025-03-20 12:41:14', '2025-03-20 12:41:14', NULL),
	(8, 'testAddTea', '', '2025-03-25 14:04:32', '2025-03-25 14:04:32', NULL),
	(22, 'new_team', '', '2025-06-12 10:00:19', '2025-06-12 10:00:19', NULL),
	(24, 'new taufiq team', '', '2025-06-12 10:06:38', '2025-06-12 10:06:38', NULL),
	(41, 'UatTestTeam', '', '2025-07-01 09:25:00', '2025-07-01 09:25:00', NULL),
	(43, 'uatTestTeam_6', '', '2025-07-01 12:19:45', '2025-07-01 12:19:45', NULL),
	(47, 'uatTestTeam_3', '', '2025-07-01 12:28:44', '2025-07-01 12:28:44', NULL),
	(49, 'uatTestTeam_1', '', '2025-07-01 12:42:44', '2025-07-01 12:42:44', NULL),
	(52, 'PSM2_DemoTeam', '', '2025-07-06 14:28:56', '2025-07-06 14:28:56', NULL),
	(54, 'PSM2 Invite Team', '', '2025-07-06 23:55:16', '2025-07-06 23:55:16', NULL),
	(55, 'PS2Demo', '', '2025-07-07 01:31:30', '2025-07-07 01:31:30', NULL);

/*!40000 ALTER TABLE `teams` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table tvt
# ------------------------------------------------------------

DROP TABLE IF EXISTS `tvt`;

CREATE TABLE `tvt` (
  `tvt_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sprint` varchar(255) NOT NULL,
  `backlog_panel` varchar(255) NOT NULL,
  `user_story_panel` varchar(255) NOT NULL,
  `fr` varchar(255) NOT NULL,
  `nfr` varchar(255) DEFAULT NULL,
  `qaw` varchar(255) DEFAULT NULL,
  `project_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`tvt_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;





# Dump of table user_role
# ------------------------------------------------------------

DROP TABLE IF EXISTS `user_role`;

CREATE TABLE `user_role` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `user_role` WRITE;
/*!40000 ALTER TABLE `user_role` DISABLE KEYS */;

INSERT INTO `user_role` (`id`, `user_id`, `role_id`, `created_at`, `updated_at`) VALUES
	(1, 4, 0, NULL, NULL);

/*!40000 ALTER TABLE `user_role` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table user_stories
# ------------------------------------------------------------

DROP TABLE IF EXISTS `user_stories`;

CREATE TABLE `user_stories` (
  `u_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_story` varchar(255) NOT NULL,
  `means` varchar(191) NOT NULL,
  `prio_story` varchar(191) NOT NULL,
  `status_id` varchar(191) NOT NULL,
  `sprint_id` varchar(191) NOT NULL,
  `proj_id` varchar(191) NOT NULL,
  `perfeature_id` varchar(191) NOT NULL,
  `secfeature_id` varchar(191) NOT NULL,
  `general_nfr_id` varchar(255) NOT NULL,
  `specific_nfr_id` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_names` longtext,
  PRIMARY KEY (`u_id`)
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `user_stories` WRITE;
/*!40000 ALTER TABLE `user_stories` DISABLE KEYS */;

INSERT INTO `user_stories` (`u_id`, `user_story`, `means`, `prio_story`, `status_id`, `sprint_id`, `proj_id`, `perfeature_id`, `secfeature_id`, `general_nfr_id`, `specific_nfr_id`, `created_at`, `updated_at`, `user_names`) VALUES
	(2, 'As a Developer, I am able to 1231 so that I can adadd', '1231', '0', '4', '0', '2', 'null', 'null', '', '', '2023-12-20 01:54:58', '2025-05-27 23:26:55', NULL),
	(5, 'As a Developer, I am able to dassadasdas so that I can adadsd', 'dassadasdas', '0', '4', '1', '2', 'null', 'null', '', '', '2024-01-08 15:18:22', '2024-01-08 15:18:22', NULL),
	(6, 'As a Developer, I am able to sadsad so that I can sad', 'sadsad', '0', '4', '8', '2', 'null', '[\"TestAddSec\"]', '', '', '2024-01-09 14:43:40', '2024-01-09 14:43:40', '[\"Shushma\",\"ammar-\",\"periyaa1\"]'),
	(7, 'As a Developer, I am able to dassda so that I can adasd', 'dassda', '0', '2', '4', '2', 'null', '[\"TestAddSec\"]', '', '', '2024-01-10 02:23:21', '2024-01-10 02:23:21', '[\"Shushma\",\"periyaa1\"]'),
	(8, 'As a Developer, I am able to dedev', 'dedev', '0', '4', '4', '2', '[\"TestAddPerf\"]', 'null', '', '', '2024-01-10 02:24:24', '2024-01-10 02:24:24', '[\"jeevan\",\"ammar-\"]'),
	(9, 'As a Developer, I am able to makan so that I can burger', 'makan', '0', '4', '8', '2', '[\"TestAddPerf\"]', '[\"TestAddSec\"]', '', '', '2024-01-10 02:32:42', '2024-01-10 02:32:42', 'null'),
	(10, 'As a Project Manager, I am able to dev', 'dev', '0', '41', '0', '6', 'null', 'null', '', '', '2024-01-16 11:50:53', '2025-05-27 23:06:55', '[\"ammar-\"]'),
	(12, 'As a Project Manager, I am able to us1 so that I can 1', 'us1', '0', '11', '16', '2', '[\"TestAddPerf\"]', '[\"TestAddSec\"]', '', '', '2024-01-17 00:43:29', '2025-05-27 23:26:49', '[\"ammar-\"]'),
	(13, 'As a Project Manager, I am able to adasdq so that I can asda', 'adasdq', '0', '2', '14', '2', 'null', '[\"TestAddSec\"]', '', '', '2024-01-30 06:53:14', '2024-01-30 06:53:14', '[\"ammar-\"]'),
	(14, 'As a Project Manager, I am able to add roles so that I can manage project access', 'eyah', '0', '2', '1', '2', 'null', '[\"TestAddSec\"]', '', '', '2024-10-10 19:07:55', '2024-10-10 19:07:55', '[\"ammar-\"]'),
	(15, 'As a Project Manager, I am able to dev so that I can manage project access', 'dev', '0', '2', '0', '2', 'null', 'null', '', '', '2024-10-10 19:23:21', '2024-10-10 19:23:21', NULL),
	(17, 'As a Project Manager, I am able to Manage', 'Manage', '0', '2', '16', '2', 'null', 'null', '[]', '[]', '2025-03-26 23:56:33', '2025-04-24 09:20:30', '[\"ammar-\"]'),
	(20, 'As a Project Manager, I am able to create backlog user stories', 'create backlog user stories', '0', '18', '0', '4', 'null', 'null', '[]', '[]', '2025-04-23 23:14:56', '2025-04-23 23:14:56', '[\"ammar-\"]'),
	(21, 'As a Project Manager, I am able to create new user story from backlog', 'create new user story from backlog', '0', '18', '0', '4', 'null', 'null', '[]', '[]', '2025-04-23 23:36:22', '2025-04-23 23:36:22', '[\"ammar-\"]'),
	(22, 'As a Project Manager, I am able to use story test', 'use story test', '0', '70', '0', '12', 'null', 'null', '[]', '[]', '2025-04-24 10:10:16', '2025-04-27 12:35:25', '[\"ammar-\"]'),
	(24, 'As a Project Manager, I am able to create tasks from US', 'create tasks from US', '0', '83', '32', '13', 'null', 'null', '[]', '[]', '2025-05-22 15:29:36', '2025-06-18 15:53:34', '[\"ammar-\"]'),
	(25, 'As a Developer, I am able to test new tasks', 'test new tasks', '0', '84', '32', '13', 'null', 'null', '[]', '[]', '2025-05-22 16:23:58', '2025-06-19 03:32:24', '[\"ammar-\"]'),
	(26, 'As a Project Manager, I am able to testFlow', 'testFlow', '0', '53', '28', '8', 'null', 'null', '[1]', '{\"1\":[\"1\"]}', '2025-05-27 22:39:42', '2025-06-11 06:28:08', '[\"ammar-\"]'),
	(27, 'As a Project Manager, I am able to testasdasd', 'testasdasd', '0', '40', '0', '6', 'null', 'null', '[]', '[]', '2025-05-27 22:53:57', '2025-05-27 23:04:18', '[\"ammar-\"]'),
	(28, 'As a Project Manager, I am able to adaasdasdasdasd', 'adaasdasdasdasd', '0', '40', '0', '6', 'null', 'null', '[]', '[]', '2025-05-27 22:57:27', '2025-05-27 22:57:27', '[\"ammar-\"]'),
	(29, 'As a Project Manager, I am able to adas', 'adas', '0', '43', '0', '6', 'null', 'null', '[]', '[]', '2025-05-27 23:05:34', '2025-06-19 08:50:12', 'null'),
	(32, 'As a Project Manager, I am able to create and assign tasks to team members so that I can organise project work efficiently.', 'create and assign tasks to team members', '0', '170', '31', '34', 'null', 'null', '[]', '[]', '2025-06-16 04:27:19', '2025-06-16 04:32:19', '[\"ammar-\"]'),
	(33, 'As a Developer, I am able to update the status of my assigned tasks.', 'update the status of my assigned tasks.', '0', '170', '31', '34', 'null', 'null', '[]', '[]', '2025-06-16 04:27:34', '2025-06-16 04:32:19', 'null'),
	(34, 'As a Project Manager, I am able to view a project dashboard to monitor overall progress.', 'view a project dashboard to monitor overall progress.', '0', '170', '31', '34', 'null', 'null', '[]', '[]', '2025-06-16 04:27:45', '2025-06-16 04:32:19', 'null'),
	(41, 'As a Project Manager, I am able to perform UAT tests so that I can manage project access', 'perform UAT tests', '0', '185', '0', '37', 'null', 'null', '[]', '[]', '2025-06-24 05:30:47', '2025-06-24 05:30:47', '[\"ammarjmldnout\"]'),
	(53, 'As a Project Manager, I am able to (perform UAT tests so that I can validate SAgile module features', '(perform UAT tests', '0', '250', '0', '53', 'null', 'null', '[]', '[]', '2025-07-01 13:01:13', '2025-08-18 09:18:14', 'null'),
	(54, 'As a Project Manager, I am able to perform UAT tests  so that I can validate SAgile module features.', 'perform UAT tests', '0', '238', '38', '50', 'null', 'null', '[]', '[]', '2025-07-01 13:11:53', '2025-07-01 13:12:05', '[\"UAT_User3\"]'),
	(56, 'As a Project Manager, I am able to asdsadd so that I can adadsa', 'asdsadd', '0', '190', '0', '38', 'null', '[\"TestAddSec\"]', '[]', '[]', '2025-07-03 22:51:16', '2025-07-03 22:51:48', 'null'),
	(57, 'As a Project Manager, I am able to create new projects so that I can manage team activities in an organised space', 'create new projects', '0', '263', '50', '54', 'null', 'null', '[]', '[]', '2025-07-06 14:33:00', '2025-07-07 01:29:44', 'null'),
	(58, 'As a Project Manager, I am able to assign tasks to developers so that I can delegate work effectively', 'assign tasks to developers', '0', '263', '50', '54', 'null', 'null', '[]', '[]', '2025-07-06 14:33:27', '2025-07-10 07:15:12', '[\"ammarjmldnout\"]'),
	(59, 'As a Developer, I am able to update the status of tasks assigned to me.', 'update the status of tasks assigned to me.', '0', '266', '0', '54', 'null', 'null', '[]', '[]', '2025-07-06 14:34:24', '2025-07-06 15:14:04', 'null'),
	(61, 'As a Project Manager, I am able to manage team activities in an organised space', 'manage team activities in an organised space', '0', '263', '0', '54', 'null', 'null', '[1]', '{\"1\":[\"1\"]}', '2025-07-06 18:00:22', '2025-07-06 18:00:22', 'null'),
	(62, 'As a Project Manager, I am able to develop test cases', 'develop test cases', '0', '275', '0', '57', 'null', 'null', '[]', '[]', '2025-07-07 01:33:41', '2025-07-07 01:33:41', '[\"ammar-\"]'),
	(63, 'As a Project Manager, I am able to perform new UAT', 'perform new UAT', '0', '271', '51', '56', 'null', 'null', '[]', '[]', '2025-08-04 07:12:09', '2025-08-04 07:12:51', '[\"ammar-\"]'),
	(64, 'As a Project Manager, I am able to assign tasks to team members', 'assign tasks to team members', '0', '283', '0', '58', 'null', 'null', '[1]', '{\"1\":[\"1\"]}', '2025-08-18 08:34:16', '2025-08-18 08:57:45', 'null'),
	(65, 'As a Developer, I am able to create history view', 'create history view', '0', '280', '0', '58', 'null', 'null', '[1]', '{\"1\":[\"1\"]}', '2025-08-18 09:00:37', '2025-08-18 09:00:37', '[\"UAT_User1\"]');

/*!40000 ALTER TABLE `user_stories` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table user_story_general_nfr
# ------------------------------------------------------------

DROP TABLE IF EXISTS `user_story_general_nfr`;

CREATE TABLE `user_story_general_nfr` (
  `user_story_id` bigint unsigned NOT NULL,
  `general_nfr_id` bigint unsigned NOT NULL,
  `specific_nfr_id` int NOT NULL,
  PRIMARY KEY (`user_story_id`,`general_nfr_id`,`specific_nfr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `user_story_general_nfr` WRITE;
/*!40000 ALTER TABLE `user_story_general_nfr` DISABLE KEYS */;

INSERT INTO `user_story_general_nfr` (`user_story_id`, `general_nfr_id`, `specific_nfr_id`) VALUES
	(26, 1, 1),
	(45, 1, 1),
	(47, 1, 1),
	(48, 1, 1),
	(49, 1, 1),
	(53, 1, 1),
	(60, 1, 1),
	(61, 1, 1),
	(64, 1, 1),
	(65, 1, 1);

/*!40000 ALTER TABLE `user_story_general_nfr` ENABLE KEYS */;
UNLOCK TABLES;



# Dump of table users
# ------------------------------------------------------------

DROP TABLE IF EXISTS `users`;

CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `username` varchar(191) DEFAULT NULL,
  `identifier` varchar(191) DEFAULT NULL,
  `country` varchar(191) NOT NULL,
  `email` varchar(191) NOT NULL,
  `is_lecturer` tinyint NOT NULL DEFAULT '0',
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(191) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;

INSERT INTO `users` (`id`, `name`, `username`, `identifier`, `country`, `email`, `is_lecturer`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
	(1, 'jeevan', 'jeevan', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$JRPDtIDLnuMCs7jb6h8WL.xbBz/YxctPX6fQznFcJGvDLteLjHYhy', 'xFmPPTQfJIsRefbD2k8xDSBLhgAcV1tRhK23T3H3ZFHRxoSexc0dUC2bSQhr', '2023-09-08 04:02:56', '2023-09-08 04:02:56'),
	(3, 'periyaa', 'periyaa1', NULL, 'Afghanistan', '<EMAIL>', 0, NULL, '$2y$10$Pl7aHWuNzGtr430UBDvAu.rqe3xDCrulTDWUUoluc7nG.GLS9o4Cu', NULL, '2023-11-06 06:14:09', '2023-11-06 06:14:09'),
	(4, 'Ammar', 'ammar-', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$mFZFJ7SPGl1DADnzxu8uiuU4CMwnrUZDX7yslyVRmRiCd0BSJKxGW', 'gMkLddA3U0U2QKlBhHnRAejca0W9lUQXUNDRHYNKzBtjqCejPb10SrEbkwBn', '2023-12-12 16:16:33', '2023-12-12 16:16:33'),
	(5, 'testUser', 'passUser', NULL, 'Afghanistan', '<EMAIL>', 0, NULL, '$2y$10$XDELxRxjcfTV9FyNFPxkzegRRYjBKWcnHlUWBsy1EAAHmhpkSX/C.', NULL, '2024-10-10 15:58:59', '2024-10-10 15:58:59'),
	(6, 'newUser', 'newUser', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$c9p0RmS8L6Wladsd2nsWFuu5BDrVWkV2p9qyNxl4umxwSBbzLaMF.', NULL, '2024-10-10 16:20:47', '2024-10-10 16:20:47'),
	(7, 'admin', 'admin', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$n0noKkBk26yCc2pOjOgqkeunsvJ1Ux2iYrWDhx6Iqddebklyk5kKC', NULL, '2025-03-20 12:08:49', '2025-03-20 12:08:49'),
	(8, 'testUser2', 'testUser2', NULL, 'Afghanistan', '<EMAIL>', 0, NULL, '$2y$10$VhGJWPSHp5zoCNDE5A8jX.e5LLDpvW8JTDxpUAP8U4033.YqWQnBq', NULL, '2025-03-20 12:40:58', '2025-03-20 12:40:58'),
	(9, 'Test User', 'testuser', 'A12345', 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$qFnNZlSTp.zLH5WyDYDi1eX.hjAlLwJE/8Yo857f4ElphFg6o38iS', 'cDI1Ih1WNfOGhlh1Xf4IzkYvNXipm30oHcuC7ZAt6GJXVkQRydmsmbhgBeSI', '2025-05-22 23:31:57', '2025-05-22 23:31:57'),
	(13, 'testLecturer', 'TestLecturer', 'STAFF123', 'Malaysia', '<EMAIL>', 1, NULL, '$2y$10$KHxXUx52mkohnIjEzwelmuZSf1b16FjrJ4IQsfPGmYdTVkRp9WS22', 'EksqMedXSUb85daSqaZW99WfYDaQhT7zBJpjUDdjNvsIBcYwmjIfCeJOtNu2', '2025-05-25 17:08:12', '2025-05-28 14:29:11'),
	(14, 'testUserSSOO', 'testUSERSSO', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$oPPVsUUqk3Ka9RIf9tYrzewqMsiYoWk76oDA7B38cLVVMV1EuR4K2', 'bNcdsds52GrPGaktHgXHvxSCg8HBasYkMNdAEX1faYBSqbS4Qvrut2umjKiS', '2025-05-25 19:28:12', '2025-05-25 19:31:11'),
	(24, 'TestRandomUser', 'TestRandomUser', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$tlXT.yi.9pI/hXV3DOPZFeK5pf95Y.uypqrDeGU3hmjl5yd0MmB9m', NULL, '2025-05-30 13:53:06', '2025-05-30 13:53:06'),
	(25, 'UAT_TestUser1', 'UAT_1', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$XYhv8VgQJieeQ/ph3399t.3kVJxc8Aal8T3MJMAUoxX81OhwDY.QG', NULL, '2025-06-08 06:43:10', '2025-06-08 06:43:10'),
	(26, 'uatTeamMember', 'uatTeamMember', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$pYkfSMnTkbzDg1/e2hU72OEH5zAJM3N8fud3mqSl/CsZZFC.vj9Oe', NULL, '2025-06-08 10:26:53', '2025-06-08 10:26:53'),
	(27, 'ASDF ASDF', 'taufiq', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$05UBnYB09BwQowOBCqvEXeTFgYlKrz57szeKWF87vYaKRl4cftij6', NULL, '2025-06-11 15:31:34', '2025-06-11 15:31:34'),
	(28, 'fypagile', 'PlaceholderPSM', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$32K8pWo4DlE29AOjsY3AHeEk.kJhX8HzVdwCcW7ARMrfyHcpKBXIO', NULL, '2025-06-16 04:23:14', '2025-06-16 04:23:14'),
	(29, 'Ammar_jmldn', 'ammarjmldnout', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$cA1q9cHfv2cH9etsxFlhMOJfL683I5GWWL0EM8ixmhzi2Os2qU1ae', NULL, '2025-06-18 03:15:32', '2025-06-18 03:15:32'),
	(30, 'userAcceptance2', 'UserAcceptance', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$ett9zINse/ZSZmfLYv8o7.K99ri7sZpKnhYyqzrHSoe7ly2vWwdvS', NULL, '2025-06-27 03:50:26', '2025-06-27 03:50:26'),
	(31, 'UATDrive', 'UatDrive', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$WjXDW6vMrV.WRuqwunK5D.zIFkVmtBQSWVbWfr7.XZtE/g63HqzHy', NULL, '2025-07-01 07:51:35', '2025-07-01 07:51:35'),
	(32, 'UAT_User1', 'UAT_User1', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$ZSj2IzD/iVxeUBK7hGW5j.aDXlyp1YC7sxMoF/z4Cwt/8tPifkESK', NULL, '2025-07-01 10:34:46', '2025-07-01 10:34:46'),
	(33, 'UAT_User2', 'UAT_User2', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$Fif8WKMlQbjQv7XoSSBO/.NyoyW8LZmN.HDbQg4zQl2YJNvRxEDya', NULL, '2025-07-01 10:35:33', '2025-07-01 10:35:33'),
	(34, 'UAT_User3', 'UAT_User3', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$wr8WX1pizI3KRRboWhXa1uj59h6qANH/RAtPO96VLcNnWAwiJLmNm', NULL, '2025-07-01 10:37:55', '2025-07-01 10:37:55'),
	(35, 'UAT_User4', 'UAT_User4', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$f56sO9Oy1S8qSNZ102MiBeD733mZ9ZevtQ4Jw/42AOEhnqrOuM/x2', NULL, '2025-07-01 10:39:00', '2025-07-01 10:39:00'),
	(36, 'UAT_User5', 'UAT_User5', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$bLRBuumYlSQCQnP/RCe0tuOtzDNSGfkVQITqIphJ8pblJZICVX69u', NULL, '2025-07-01 10:39:28', '2025-07-01 10:39:28'),
	(37, 'UAT_User6', 'UAT_User6', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$QUzYvlDWnvdELn5fFdEmyedTrmwUHc0.nvDH2lMxmLOFoeUv4zAnm', NULL, '2025-07-01 10:39:45', '2025-07-01 10:39:45'),
	(38, 'CHONG SIEW ZHEN', 'sz', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$IWClcKgXtteUTnInFOXtEuHgFHNyOkXtSAzNIBi/tj08DhnTsdvMK', NULL, '2025-07-01 12:12:39', '2025-07-01 12:12:39'),
	(39, 'Ivlyn Tay Wan Rou', 'ivlyn', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$i9hrqhcYqCgil/R9AKLE1.h0uXXViLq8eHWI03q8TMBuwgqGvmlAS', NULL, '2025-07-01 12:13:05', '2025-07-01 12:13:05'),
	(40, 'Harun', 'harun', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$ONc2RzvZ0nUpKIWLZbBI3.Q.rKV4h.6dsxN6G2bmHsxNdG1Hsswgy', NULL, '2025-08-04 07:15:13', '2025-08-04 07:15:13'),
	(41, 'Ivlyn Tay Wan Rou', 'tay', NULL, 'Malaysia', '<EMAIL>', 0, NULL, '$2y$10$lfLGnDgtCmq9Nl7WNEVTh.P.CNpZpYBrRvofI3IPqDf.1tqSWLPjy', NULL, '2025-08-18 14:28:49', '2025-08-18 14:28:49');

/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;



/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;


