{{-- Character Counter Component --}}
{{-- Usage: @include('inc.character-counter', ['inputId' => 'input_id', 'counterId' => 'counter_id', 'maxLength' => 100]) --}}

@php
    $inputId = $inputId ?? 'input_field';
    $counterId = $counterId ?? 'char_count';
    $maxLength = $maxLength ?? 100;
@endphp

<small class="text-muted">
    <span id="{{ $counterId }}">0</span>/{{ $maxLength }} characters
</small>

@once
<script>
    // Character Counter Utility
    window.CharCounter = window.CharCounter || {
        updateCount: function(inputId, counterId, maxLength) {
            const input = document.getElementById(inputId);
            const counter = document.getElementById(counterId);
            
            if (!input || !counter) return;
            
            const currentLength = input.value.length;
            counter.textContent = currentLength;
            
            if (currentLength >= maxLength) {
                counter.style.color = 'red';
            } else {
                counter.style.color = '';
            }
        },
        
        init: function(inputId, counterId, maxLength) {
            const input = document.getElementById(inputId);
            
            if (input) {
                // Initialize count
                this.updateCount(inputId, counterId, maxLength);
                
                // Add event listeners
                input.addEventListener('input', () => {
                    this.updateCount(inputId, counterId, maxLength);
                });
                
                input.addEventListener('keyup', () => {
                    this.updateCount(inputId, counterId, maxLength);
                });
                
                input.addEventListener('paste', () => {
                    setTimeout(() => {
                        this.updateCount(inputId, counterId, maxLength);
                    }, 10);
                });
            }
        }
    };
    
    // Legacy function for backward compatibility
    function updateCharCount(inputId, counterId, maxLength) {
        CharCounter.updateCount(inputId, counterId, maxLength);
    }
</script>
@endonce

<script>
    document.addEventListener('DOMContentLoaded', function() {
        CharCounter.init('{{ $inputId }}', '{{ $counterId }}', {{ $maxLength }});
    });
</script>
