<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInvitationStatusToTeammappings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('teammappings', function (Blueprint $table) {
            $table->enum('invitation_status', ['pending', 'accepted', 'declined'])->default('pending');
            $table->string('invitation_token')->nullable()->unique();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('teammappings', function (Blueprint $table) {
            $table->dropColumn('invitation_status');
            $table->dropColumn('invitation_token');
        });
    }
}
