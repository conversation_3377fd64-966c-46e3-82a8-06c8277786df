<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Burn Down Chart</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }
        .no-sprint-container {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .no-sprint-container h3 {
            font-size: 1.5rem;
            color: #374151;
            margin-bottom: 12px;
        }
        .no-sprint-container p {
            color: #6b7280;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        @if(!$hasActiveSprint)
            <div class="no-sprint-container">
                <h3><i class="fas fa-calendar-times"></i> No Active Sprint</h3>
                <p>There is currently no active sprint for this project.</p>
            </div>
        @else
            <div class="row mb-4">
                <div class="col-12">
                    <div>
                        <h4 class="mb-0">Burn Down Chart - {{ $sprintName }}</h4>
                        <p class="text-muted mb-0">
                            Start Date: <span class="text-primary">{{ $startDate->format('j M Y') }}</span> | 
                            End Date: <span class="text-danger">{{ $endDate->format('j M Y') }}</span>
                        </p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div id="burnDownChart" style="height: 400px;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Tasks</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($tasks as $task)
                                        <tr>
                                            <td>{{ $task->id }}</td>
                                            <td>{{ $task->title }}</td>
                                            <td>{{ $task->description }}</td>
                                            <td>
                                                @php
                                                    $status = $statuses->where('id', $task->status_id)->first();
                                                @endphp
                                                <span class="badge bg-{{ $status->color ?? 'secondary' }}">
                                                    {{ $status->title ?? 'No Status' }}
                                                </span>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                google.charts.load('current', {'packages':['corechart']});
                google.charts.setOnLoadCallback(drawChart);

                function drawChart() {
                    var data = google.visualization.arrayToDataTable([
                        ['Day', 'Ideal Work', 'Actual Work'],
                        @foreach($idealData as $key => $value)
                            [{{ $key }}, {{ $value }}, {{ $actualData[$key] ?? 'null' }}],
                        @endforeach
                    ]);

                    var options = {
                        title: 'Sprint Progress',
                        titleTextStyle: { 
                            fontSize: 18,
                            bold: true
                        },
                        curveType: 'none',
                        legend: { 
                            position: 'bottom',
                            textStyle: { fontSize: 12 }
                        },
                        hAxis: {
                            title: 'Days',
                            titleTextStyle: { fontSize: 12 },
                            viewWindow: { min: 0 }
                        },
                        vAxis: {
                            title: 'Remaining Work',
                            titleTextStyle: { fontSize: 12 },
                            minValue: 0
                        },
                        chartArea: {
                            width: '85%',
                            height: '75%'
                        },
                        series: {
                            0: { lineDashStyle: [5, 5], color: '#4e73df' }, // Ideal line
                            1: { color: '#1cc88a' } // Actual line
                        }
                    };

                    var chart = new google.visualization.LineChart(document.getElementById('burnDownChart'));
                    chart.draw(data, options);

                    // Handle window resize
                    window.addEventListener('resize', function() {
                        chart.draw(data, options);
                    });
                }
            </script>
        @endif
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>