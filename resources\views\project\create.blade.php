@extends('layouts.app2')
@include('inc.style')
@include('inc.navbar')

@section('content')
@include('inc.title')
<form action="{{route('projects.store')}}" method="post" enctype="multipart/form-data" class="mt-4">
@csrf

<div class="mb-3">
    <label for="proj_name_input" class="form-label">Project Title :</label>
    <input type="text" name="proj_name" id="proj_name_input" class="form-control @error('proj_name') is-invalid @enderror" 
           value="{{ old('proj_name') }}" maxlength="100">
    @include('inc.character-counter', ['inputId' => 'proj_name_input', 'counterId' => 'name_char_count', 'maxLength' => 100])
    @error('proj_name')
        <div class="invalid-feedback">
            {{ $message }}
        </div>
    @enderror
</div>

<div class="mb-3">
    <label for="proj_desc_input" class="form-label">Description :</label>
    <textarea name="proj_desc" id="proj_desc_input" class="form-control @error('proj_desc') is-invalid @enderror" 
              rows="4" maxlength="500">{{ old('proj_desc') }}</textarea>
    @include('inc.character-counter', ['inputId' => 'proj_desc_input', 'counterId' => 'desc_char_count', 'maxLength' => 500])
    @error('proj_desc')
        <div class="invalid-feedback">
            {{ $message }}
        </div>
    @enderror
</div>

<div class="mb-3">
    <label for="start_date_input" class="form-label">Start Date :</label>
    <input type="date" name="start_date" id="start_date_input" class="form-control @error('start_date') is-invalid @enderror" value="{{ old('start_date') }}">
    @error('start_date')
        <div class="invalid-feedback">
            {{ $message }}
        </div>
    @enderror
</div>

<div class="mb-3">
    <label for="end_date_input" class="form-label">Completion Date :</label>
    <input type="date" name="end_date" id="end_date_input" class="form-control @error('end_date') is-invalid @enderror" value="{{ old('end_date') }}">
    @error('end_date')
        <div class="invalid-feedback">
            {{ $message }}
        </div>
    @enderror
</div>

<div class="mb-3">
    <label for="team_select" class="form-label">Team:</label> 
    <select name="team" id="team_select" class="form-control @error('team') is-invalid @enderror">
        @foreach($teams as $team)
            <option value="{{ $team->team_name }}" {{ old('team') == $team->team_name ? 'selected' : '' }}>{{ $team->team_name }}</option>
        @endforeach
    </select>
    @error('team')
        <div class="invalid-feedback">
            {{ $message }}
        </div>
    @enderror
</div>

<button type="submit" class="btn btn-success">Submit</button>
<a href="{{route('project.newIndex')}}" class="btn btn-secondary">Cancel</a>
</form>
@endsection


