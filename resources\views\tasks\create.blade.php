<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Task</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: none;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 1rem;
        }
        .card-body {
            padding: 1.25rem;
        }
        .form-label {
            font-weight: 500;
        }
        .text-danger {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        .date-info {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Create New Task</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('tasks.store') }}" method="post" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="userstory_id" value="{{ $userstory_id }}">
                    <input type="hidden" name="sprint_id" value=0>

                    <div class="mb-3">
                        <label for="title" class="form-label">Task Name</label>
                        <input type="text" id="title" name="title" class="form-control" value="{{ old('title') }}" maxlength="100">
                        @include('inc.character-counter', 
                        [ 'inputId' => 'title', 
                                'counterId' => 'title_char_count', 
                                'maxLength' => 100])
                        @error('title')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description" name="description" class="form-control" rows="3" maxlength="300">{{ old('description') }}</textarea>
                        @include('inc.character-counter', 
                        [ 'inputId' => 'description', 
                                'counterId' => 'description_char_count', 
                                'maxLength' => 300])
                        @error('description')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="user_names" class="form-label">Assigned to</label>
                            <select name="user_names[]" id="user_names" class="form-select" multiple>
                                @foreach($teamlist as $teammember)
                                <option value="{{ $teammember['username'] }}"
                                    {{ (old('user_names') && in_array($teammember['username'], old('user_names'))) ? 'selected' : '' }}>
                                    {{ $teammember['username'] }}
                                </option>
                                @endforeach
                            </select>
                            @error('user_names')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                            @error('user_id')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6">
                            <label for="status_id" class="form-label">Status</label>
                            <select name="status_id" id="status_id" class="form-select">
                                <option value="" selected disabled>Select status</option>
                                @foreach($statuses as $status)
                                <option value="{{ $status->id }}" {{ old('status_id') == $status->id ? 'selected' : '' }}>
                                    {{ $status->title }}
                                </option>
                                @endforeach
                            </select>
                            @error('status_id')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>


                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i> Add Task
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Function to notify parent iframe about height changes
            function notifyParentAboutHeight() {
                const height = Math.max(
                    document.body.scrollHeight,
                    document.documentElement.scrollHeight,
                    document.body.offsetHeight,
                    document.documentElement.offsetHeight,
                    document.body.clientHeight,
                    document.documentElement.clientHeight
                );
                
                // Add small buffer to avoid scrollbars
                const heightWithBuffer = height + 30;
                
                // Send message to parent
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'resize',
                        height: heightWithBuffer,
                        iframeId: 'task-create-iframe'
                    }, '*');
                }
            }
            
            // Initial height notification
            notifyParentAboutHeight();
            
            // Set up mutation observer to detect content changes
            const observer = new MutationObserver(function() {
                notifyParentAboutHeight();
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });
            
            // Additional triggers for height recalculation
            window.addEventListener('load', notifyParentAboutHeight);
            window.addEventListener('resize', notifyParentAboutHeight);
        });
    </script>
</body>
</html>